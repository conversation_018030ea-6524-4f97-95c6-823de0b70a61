import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate, Outlet } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import api from '@/lib/axios';
import { io } from 'socket.io-client';

import {
    CreditCard,
    ShoppingCart,
    Wallet,
    User,
    BookOpen,
    Menu,
    X,
    LogOut,
    ChevronDown,
    ShoppingBag,
    Activity,
    Key,
    Headset 
} from 'lucide-react';

const SIDEBAR_WIDTH = '16rem';

const Sidebar = ({ isOpen, toggleSidebar }) => {
    const location = useLocation();

    const menuItems = [
        {
            title: 'Dashboard',
            icon: <Activity className="w-5 h-5" />,
            path: '/dashboard'
        },
        {
            title: 'eSIM Plans',
            icon: <CreditCard className="w-5 h-5" />,
            path: '/dashboard/plans'
        },
        {
            title: 'My Orders',
            icon: <ShoppingBag className="w-5 h-5" />,
            path: '/dashboard/orders'
        },
        {
            title: 'My Wallet',
            icon: <Wallet className="w-5 h-5" />,
            path: '/dashboard/wallet'
        },
        {
            title: 'My Account',
            icon: <User className="w-5 h-5" />,
            path: '/dashboard/account'
        },
        {
            title: 'API Access',
            icon: <Key className="w-5 h-5" />,
            path: '/dashboard/api-access'
        },
        {
            title: 'Knowledge Base',
            icon: <BookOpen className="w-5 h-5" />,
            path: '/dashboard/knowledge-base'
        },
        {
            title: 'Support',
            icon: <Headset  className="w-5 h-5" />,
            path: 'https://support.vizlync.net/login',
            external: true
        }
    ];

    
    return (
        <>
            {/* Backdrop for mobile */}
            {isOpen && (
                <div 
                    className="fixed inset-0 bg-black/50 lg:hidden z-40"
                    onClick={toggleSidebar}
                />
            )}
            
            {/* Sidebar */}
            <aside
                className={`fixed left-0 top-0 h-screen bg-white border-r shadow-sm transition-transform duration-300 z-50 ${
                    isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
                }`}
                style={{ width: SIDEBAR_WIDTH }}
            >
                <div className="flex flex-col h-full">
                    <div className="flex items-center justify-between p-4 border-b">
                        <Link to="/dashboard" className="flex items-center space-x-2">
                            {/* <img src="/logo.png" alt="eSIM Logo" className="h-8" /> */}
                            <span className="font-bold text-xl">eSIM Portal</span>
                        </Link>
                        <button onClick={toggleSidebar} className="lg:hidden">
                            <X className="w-6 h-6" />
                        </button>
                    </div>

                    <nav className="flex-1 overflow-y-auto py-6">
                        <ul className="space-y-1 px-3">
                            {menuItems.map((item) => (
                                <li key={item.path}>
                                    {item.external ? (
                                        <a
                                            href={item.path}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors hover:bg-gray-100"
                                        >
                                            {item.icon}
                                            <span>{item.title}</span>
                                        </a>
                                    ) : (
                                        <Link
                                            to={item.path}
                                            className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                                                location.pathname === item.path
                                                    ? 'bg-blue-800 text-white'
                                                    : 'hover:bg-gray-100'
                                            }`}
                                        >
                                            {item.icon}
                                            <span>{item.title}</span>
                                        </Link>
                                    )}
                                </li>
                            ))}
                        </ul>
                    </nav>
                </div>
            </aside>
        </>
    );
};

const Navbar = ({ toggleSidebar, cartCount }) => {
    const { user, logout } = useAuth();
    const [showProfileMenu, setShowProfileMenu] = useState(false);
    const [walletBalance, setWalletBalance] = useState(0);
    const [loading, setLoading] = useState(true);
    const navigate = useNavigate();

    useEffect(() => {
        if (user) {
            fetchWalletBalance();
            setupWebSocket();
        }
    }, [user]);

    const setupWebSocket = () => {
        const socket = io(import.meta.env.VITE_API_URL || 'http://localhost:3000', {
            withCredentials: true
        });

        socket.on('connect', () => {
            // console.log('Connected to WebSocket');
            socket.emit('join', user.id);
        });

        socket.on('walletUpdate', (data) => {
            setWalletBalance(data.balance);
            setLoading(false);
        });

        socket.on('disconnect', () => {
            // console.log('Disconnected from WebSocket');
        });

        // Cleanup on component unmount
        return () => {
            socket.disconnect();
        };
    };

    const fetchWalletBalance = async () => {
        try {
            setLoading(true);
            const response = await api.get('/api/partner/wallet');
            setWalletBalance(response.data.balance);
        } catch (error) {
            // console.error('Error fetching wallet balance:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleLogout = async () => {
        await logout();
        navigate('/login');
    };

    return (
        <header className="fixed top-0 right-0 left-0 lg:left-[16rem] h-16 bg-white border-b z-30 flex items-center px-4">
            {/* Left Side - Menu Button */}
            <div className="flex items-center">
                <button
                    onClick={toggleSidebar}
                    className="lg:hidden p-2 rounded-lg hover:bg-gray-400 bg-blue-800 text-white"
                >
                    <Menu className="w-6 h-6" />
                </button>
            </div>
    
            {/* Right Side - Wallet, Cart, Profile */}
            <div className="flex-1 flex justify-end items-center gap-6">
                {/* Wallet Balance */}
                <Link to="/dashboard/wallet" className="hidden sm:flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-lg text-blue-800">
                    <Wallet className="w-5 h-5" />
                    {loading ? (
                        <div className="w-20 h-5 bg-gray-200 animate-pulse rounded" />
                    ) : (
                        <span className="font-semibold">${walletBalance.toFixed(2)}</span>
                    )}
                </Link>
    
                {/* Cart */}
                <Link to="/dashboard/cart" className="relative">
                    <ShoppingCart className="w-6 h-6 text-blue-800"  />
                    {cartCount > 0 && (
                        <span className="absolute -top-1 -right-1 bg-blue-800 text-white text-xs w-4 h-4 rounded-full flex items-center justify-center">
                            {cartCount}
                        </span>
                    )}
                </Link>
    
                {/* Profile Menu */}
                <div className="relative">
                    <button
                        onClick={() => setShowProfileMenu(!showProfileMenu)}
                        className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 bg-white"
                    >
                        <div className="w-8 h-8 rounded-full bg-blue-900 text-white flex items-center justify-center">
                            {user?.email?.charAt(0).toUpperCase()}
                        </div>
                        <span className="hidden sm:inline">{user?.email}</span>
                        <ChevronDown className="w-4 h-4" />
                    </button>
    
                    {showProfileMenu && (
                        <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border py-1">
                            <Link
                                to="/dashboard/account"
                                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                                My Account
                            </Link>
                            <Link
                                to="/dashboard/orders"
                                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            >
                                My Orders
                            </Link>
                            <button
                                onClick={handleLogout}
                                className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100 flex items-center space-x-2 bg-white"
                            >
                                <LogOut className="w-4 h-4" />
                                <span>Logout</span>
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </header>
    );
    
};

export default function PartnerLayout() {
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [cartCount, setCartCount] = useState(0);
    const { user } = useAuth();

    useEffect(() => {
        if (user) {
            fetchCartCount();
        }
    }, [user]);

    const fetchCartCount = async () => {
        try {
            const response = await api.get('/api/cart/count');
            setCartCount(response.data.count);
        } catch (error) {
            console.error('Error fetching cart count:', error);
        }
    };

    return (
        <div className="min-h-screen w-screen bg-gray-50">
            <Sidebar isOpen={isSidebarOpen} toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)} />
            <Navbar toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)} cartCount={cartCount} />
            
            <main className="lg:ml-[16rem] pt-16">
                <div className="p-6 min-h-[calc(100vh-4rem)]">
                    <Outlet context={{ fetchCartCount }} />
                </div>
            </main>
        </div>
    );
}
