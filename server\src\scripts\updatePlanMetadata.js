// Script to update all external plans by extracting metadata from providerMetadata field
// and storing it in planInfo and additionalInfo fields
require('dotenv').config();
const sequelize = require('../config/database');
const { EsimPlan } = require('../models');

async function updatePlansMetadata() {
    try {
        console.log('Starting plan metadata update process...');
        
        // Find all external plans (those with providerMetadata)
        const plans = await EsimPlan.findAll({
            where: {
                providerMetadata: {
                    [sequelize.Sequelize.Op.not]: null
                }
            }
        });
        
        console.log(`Found ${plans.length} external plans to update`);
        
        let updatedCount = 0;
        let errorCount = 0;
        
        for (const plan of plans) {
            try {
                const metadata = plan.providerMetadata;
                let planInfoContent = '';
                let additionalInfoContent = '';
                
                // Extract plan details from customData
                if (metadata.customData) {
                    const planDetails = metadata.customData.find(d => d.name?.trim() === 'PLAN_DETAILS');
                    if (planDetails?.value) {
                        try {
                            const parsed = JSON.parse(planDetails.value);
                            
                            // Store description in planInfo
                            if (parsed.description) {
                                planInfoContent += `<div>${parsed.description}</div>`;
                            }
                            
                            // Store key features in planInfo
                            if (parsed.items && Array.isArray(parsed.items) && parsed.items.length > 0) {
                                planInfoContent += `
                                <div class="mt-4">
                                    <h4 class="text-sm font-semibold mb-2">Key Features:</h4>
                                    <ul class="list-disc list-inside space-y-1">
                                        ${parsed.items.map(item => `<li>${item}</li>`).join('\n')}
                                    </ul>
                                </div>`;
                            }
                        } catch (e) {
                            console.error(`Error parsing PLAN_DETAILS for plan ${plan.id}:`, e.message);
                        }
                    }
                }
                
                // Extract additional information like usage tracking
                if (metadata.usageTracking) {
                    additionalInfoContent += `
                    <div class="mt-4">
                        <h4 class="text-sm font-semibold mb-2">Usage Tracking:</h4>
                        <p>${metadata.usageTracking}</p>
                    </div>`;
                }
                
                // Extract any other useful information from metadata
                if (metadata.productDetails && Array.isArray(metadata.productDetails)) {
                    const heading = metadata.productDetails.find(detail => detail.name === "heading")?.value;
                    if (heading) {
                        additionalInfoContent += `
                        <div class="mt-4">
                            <h4 class="text-sm font-semibold mb-2">Product Details:</h4>
                            <p>${heading}</p>
                        </div>`;
                    }
                    
                    // Add other product details that might be useful
                    metadata.productDetails
                        .filter(detail => 
                            detail.name !== "heading" && 
                            detail.name !== "PLAN_DATA_LIMIT" && 
                            detail.value)
                        .forEach(detail => {
                            additionalInfoContent += `
                            <div class="mt-2">
                                <strong>${detail.name}:</strong> ${detail.value}
                            </div>`;
                        });
                }
                
                // Extract original data if available
                if (metadata.originalData) {
                    if (metadata.originalData.additionalDetails) {
                        additionalInfoContent += `
                        <div class="mt-4">
                            <h4 class="text-sm font-semibold mb-2">Additional Details:</h4>
                            <p>${metadata.originalData.additionalDetails}</p>
                        </div>`;
                    }
                }
                
                // Update the plan only if we have content to add
                if (planInfoContent || additionalInfoContent) {
                    await plan.update({
                        planInfo: planInfoContent || plan.planInfo,
                        additionalInfo: additionalInfoContent || plan.additionalInfo
                    });
                    updatedCount++;
                    console.log(`Updated plan: ${plan.id} - ${plan.name}`);
                }
            } catch (error) {
                console.error(`Error updating plan ${plan.id}:`, error);
                errorCount++;
            }
        }
        
        console.log(`\nUpdate completed. Updated ${updatedCount} plans. Errors: ${errorCount}`);
        
    } catch (error) {
        console.error('Error in update script:', error);
    } finally {
        // Close database connection
        await sequelize.close();
    }
}

// Run the script
updatePlansMetadata()
    .then(() => {
        console.log('Script execution completed.');
        process.exit(0);
    })
    .catch(err => {
        console.error('Script failed:', err);
        process.exit(1);
    }); 