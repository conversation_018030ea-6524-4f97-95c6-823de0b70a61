const express = require('express');
const router = express.Router();
const cartController = require('../controllers/cartController');
const { isAuthenticated } = require('../middleware/auth');

// Apply authentication middleware to all cart routes
router.use(isAuthenticated);

// Cart routes
router.post('/', cartController.addToCart);
router.get('/', cartController.getCart);
router.delete('/', cartController.removeFromCart);
router.get('/count', cartController.getCartCount);
router.post('/:itemId/activation-policy', isAuthenticated, cartController.updateActivationPolicy);

module.exports = router;