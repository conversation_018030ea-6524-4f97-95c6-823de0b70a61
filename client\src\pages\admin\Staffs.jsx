import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Edit2, Trash2, UserCheck, Plus } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import api from '@/lib/axios';

export default function Staffs() {
    const [staff, setStaff] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [selectedStaffId, setSelectedStaffId] = useState(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalItems, setTotalItems] = useState(0);
    const { toast } = useToast();
    const navigate = useNavigate();

    useEffect(() => {
        fetchStaff();
    }, [currentPage]);

    const fetchStaff = async () => {
        try {
            const response = await api.get('/api/staff', {
                params: {
                    page: currentPage,
                    limit: 10,
                    search: searchTerm
                }
            });
            setStaff(response.data.staff);
            setTotalPages(response.data.totalPages);
            setTotalItems(response.data.totalItems);
        } catch (error) {
            // Only log errors in development
            if (import.meta.env.DEV) {
                // console.error('Error fetching staff:', error);
            }
            toast({
                title: "Error",
                description: "Failed to fetch staff",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = (event) => {
        setSearchTerm(event.target.value);
        setCurrentPage(1); 
    };

    useEffect(() => {
        const timer = setTimeout(() => {
            fetchStaff();
        }, 300);

        return () => clearTimeout(timer);
    }, [searchTerm, currentPage]);

    const handleToggleStatus = async (staffId) => {
        try {
            await api.patch(`/api/staff/${staffId}/toggle-status`);
            toast({
                title: "Success",
                description: "Staff status updated successfully"
            });
            fetchStaff();
        } catch (error) {
            // Only log errors in development
            if (import.meta.env.DEV) {
                // console.error('Error toggling staff status:', error);
            }
            toast({
                title: "Error",
                description: "Failed to update staff status",
                variant: "destructive"
            });
        }
    };

    const handleDelete = async () => {
        try {
            await api.delete(`/api/staff/${selectedStaffId}`);
            setDeleteDialogOpen(false);
            toast({
                title: "Success",
                description: "Staff deleted successfully"
            });
            fetchStaff();
        } catch (error) {
            // Only log errors in development
            if (import.meta.env.DEV) {
                // console.error('Error deleting staff:', error);
            }
            toast({
                title: "Error",
                description: "Failed to delete staff",
                variant: "destructive"
            });
        }
    };

    const confirmDelete = (staffId) => {
        setSelectedStaffId(staffId);
        setDeleteDialogOpen(true);
    };

    const filteredStaff = staff;

    return (
        <Card className="p-4">
            <CardHeader className="bg-gradient-to-r from-blue-800 to-blue-600 mb-2 rounded-t-lg px-12">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                    <div>
                        <CardTitle className="text-2xl font-bold tracking-tight text-white">Staff Management</CardTitle>
                        <p className="text-muted-foreground text-white">Manage admin staff accounts</p>
                    </div>
                    <Button onClick={() => navigate('/admin/staffs/add')} className="flex items-center gap-2 bg-purple-500 text-white">
                    <Plus className="w-4 h-4 mr-2" />
                     Add Staff
                    </Button>
                </div>
            </CardHeader>
            <CardContent>
        <div className="h-full flex flex-col gap-6 ">
            <div className="bg-white rounded-lg shadow-sm border ">
                <div className="p-4 border-b">
                    <Input
                        placeholder="Search staff..."
                        value={searchTerm}
                        onChange={handleSearch}
                        className="max-w-sm"
                    />
                </div>

                <Table>
                    <TableHeader>
                        <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100">
                            <TableHead>Staff ID</TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>Country</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Join Date</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {loading ? (
                            <TableRow>
                                <TableCell colSpan={7} className="text-center">
                                    Loading...
                                </TableCell>
                            </TableRow>
                        ) : filteredStaff.length === 0 ? (
                            <TableRow>
                                <TableCell colSpan={7} className="text-center">
                                    No staff found
                                </TableCell>
                            </TableRow>
                        ) : (
                            filteredStaff.map((member) => (
                                <TableRow key={member.id}>
                                    <TableCell>{member.id}</TableCell>
                                    <TableCell>{`${member.firstName} ${member.lastName}`}</TableCell>
                                    <TableCell>{member.email}</TableCell>
                                    <TableCell>{member.country?.name}</TableCell>
                                    <TableCell>
                                        <div className="flex items-center">
                                            <div className={`w-2 h-2 rounded-full mr-2 ${member.isActive ? 'bg-green-500' : 'bg-gray-300'}`} />
                                            {member.isActive ? 'Active' : 'Inactive'}
                                        </div>
                                    </TableCell>
                                    <TableCell>{new Date(member.createdAt).toLocaleDateString()}</TableCell>
                                    <TableCell className="text-right">
                                        <div className="flex justify-end gap-2">
                                            <Button 
                                                variant="ghost" 
                                                size="sm"
                                                className="h-8 w-8 p-0"
                                                title="Edit Staff"
                                                onClick={() => navigate(`/admin/staffs/edit/${member.id}`)}
                                            >
                                                <Edit2 className="w-4 h-4" />
                                            </Button>
                                            <Button 
                                                variant="ghost" 
                                                size="sm"
                                                className="h-8 w-8 p-0"
                                                title="Toggle Status"
                                                onClick={() => handleToggleStatus(member.id)}
                                            >
                                                <UserCheck className="w-4 h-4" />
                                            </Button>
                                            <Button
                                                variant="ghost" 
                                                size="sm"
                                                className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                                title="Delete Staff"
                                                onClick={() => confirmDelete(member.id)}
                                            >
                                                <Trash2 className="w-4 h-4" />
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))
                        )}
                    </TableBody>
                </Table>

                {/* Pagination */}
                <div className="flex items-center justify-between px-2 py-4">
                    <p className="text-sm text-muted-foreground">
                        Showing {staff.length} of {totalItems} staff members
                    </p>
                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                            disabled={currentPage === 1 || loading}
                        >
                            Previous
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(prev => prev + 1)}
                            disabled={currentPage >= totalPages || loading}
                        >
                            Next
                        </Button>
                    </div>
                </div>
            </div>

            <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the staff account.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
            </CardContent>
        </Card>
    );
}
