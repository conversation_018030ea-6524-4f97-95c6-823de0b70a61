const express = require('express');
const router = express.Router();
const countryController = require('../controllers/countryController');
const { isAuthenticated } = require('../middleware/auth');

// All routes require authentication
router.use(isAuthenticated);

// Get all countries
router.get('/', countryController.getAllCountries);

// Get all regions
router.get('/regions', countryController.getRegions);

module.exports = router;
