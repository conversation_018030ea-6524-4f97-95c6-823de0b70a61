'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Drop the existing foreign key
    await queryInterface.removeConstraint('OTPs', 'OTPs_ibfk_1');

    // Add the new foreign key with correct table name
    await queryInterface.addConstraint('OTPs', {
      fields: ['userId'],
      type: 'foreign key',
      name: 'OTPs_userId_fkey',
      references: {
        table: 'users',
        field: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Revert back to original foreign key
    await queryInterface.removeConstraint('OTPs', 'OTPs_userId_fkey');
    
    await queryInterface.addConstraint('OTPs', {
      fields: ['userId'],
      type: 'foreign key',
      name: 'OTPs_ibfk_1',
      references: {
        table: 'Users',
        field: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });
  }
}; 