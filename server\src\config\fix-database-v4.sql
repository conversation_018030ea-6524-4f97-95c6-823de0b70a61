USE esim_platform;

-- Insert Spain into existing Countries table
INSERT INTO Countries (id, iso3, name, phoneCode, currencyCode, currencySymbol, region, isActive, createdAt, updatedAt)
VALUES ('ES', 'ESP', 'Spain', '+34', 'EUR', '€', 'Europe', true, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    iso3 = VALUES(iso3),
    name = VALUES(name),
    phoneCode = VALUES(phoneCode),
    currencyCode = VALUES(currencyCode),
    currencySymbol = VALUES(currencySymbol),
    region = VALUES(region),
    isActive = VALUES(isActive),
    updatedAt = NOW();
