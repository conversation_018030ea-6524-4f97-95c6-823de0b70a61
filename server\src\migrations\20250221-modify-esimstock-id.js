'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('EsimStocks', 'id', {
      type: Sequelize.UUID,
      defaultValue: Sequelize.literal('UUID()'),
      primaryKey: true
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('EsimStocks', 'id', {
      type: Sequelize.INTEGER,
      autoIncrement: true,
      primaryKey: true
    });
  }
};
