describe('Plans Page', () => {
  beforeEach(() => {
    cy.createTestPartner().then((partner) => {
      cy.login(partner.email, partner.password);
      cy.visit('/partner/plans');
    });
  });

  it('should display plans page header', () => {
    cy.contains('h1', 'eSIM Plans').should('be.visible');
    cy.contains('Choose from our wide range of eSIM plans').should('be.visible');
  });

  it('should display plan cards with correct information', () => {
    cy.get('.card').first().within(() => {
      // Check plan card structure
      cy.get('.badge').should('have.length.at.least', 1); // Plan type badge
      cy.get('[class*="text-3xl"]').should('exist'); // Price
      cy.get('[class*="text-gray-500"]').should('contain', 'days'); // Validity
      cy.get('.lucide-wifi').should('exist'); // Data plan icon
      cy.get('.lucide-clock').should('exist'); // Validity icon
      cy.get('.lucide-globe').should('exist'); // Coverage icon
    });
  });

  it('should filter plans by search', () => {
    // Get the first plan name
    cy.get('.card').first().find('.text-xl').invoke('text').then((planName) => {
      // Type the plan name in search
      cy.get('input[placeholder="Search plans..."]').type(planName);
      
      // Verify filtered results contain the plan name
      cy.get('.card').each(($card) => {
        cy.wrap($card).should('contain', planName);
      });
    });
  });

  it('should filter plans by country', () => {
    // Open country select
    cy.get('select').first().then(($select) => {
      if ($select.find('option').length > 1) {
        // Select first country option that's not 'all'
        cy.wrap($select).select($select.find('option').eq(1).val());
        
        // Verify API call includes country parameter
        cy.intercept('GET', '/api/esim-plans/partner*').as('getPlans');
        cy.wait('@getPlans').its('request.url').should('include', 'countryId=');
      }
    });
  });

  it('should filter plans by region', () => {
    // Open region select
    cy.get('select').eq(1).then(($select) => {
      if ($select.find('option').length > 1) {
        // Select first region option that's not 'all'
        cy.wrap($select).select($select.find('option').eq(1).val());
        
        // Verify API call includes region parameter
        cy.intercept('GET', '/api/esim-plans/partner*').as('getPlans');
        cy.wait('@getPlans').its('request.url').should('include', 'region=');
      }
    });
  });

  it('should show loading state', () => {
    cy.intercept('GET', '/api/esim-plans/partner*', (req) => {
      req.on('response', (res) => {
        res.setDelay(1000);
      });
    }).as('getPlans');

    cy.visit('/partner/plans');
    cy.get('.lucide-loader2').should('be.visible');
    cy.wait('@getPlans');
    cy.get('.lucide-loader2').should('not.exist');
  });

  it('should handle error states', () => {
    cy.intercept('GET', '/api/esim-plans/partner*', {
      statusCode: 500,
      body: { message: 'Internal Server Error' }
    }).as('getPlansError');

    cy.visit('/partner/plans');
    cy.contains('Failed to fetch plans').should('be.visible');
  });

  it('should reset filters', () => {
    // Set some filters
    cy.get('input[placeholder="Search plans..."]').type('test');
    
    // Reset filters
    cy.get('button').contains('Reset').click();
    
    // Verify filters are reset
    cy.get('input[placeholder="Search plans..."]').should('have.value', '');
    cy.get('select').first().should('have.value', 'all');
    cy.get('select').eq(1).should('have.value', 'all');
  });

  it('should add plan to cart and navigate', () => {
    cy.intercept('POST', '/api/cart').as('addToCart');

    // Click add to cart button on first plan
    cy.get('.card').first().find('button').contains('Add to Cart').click();

    // Wait for API call
    cy.wait('@addToCart').then((interception) => {
      expect(interception.response.statusCode).to.eq(200);
    });

    // Verify success toast
    cy.contains('Plan added to cart').should('be.visible');

    // Verify navigation to cart
    cy.url().should('include', '/dashboard/cart');
  });

  it('should handle add to cart errors', () => {
    cy.intercept('POST', '/api/cart', {
      statusCode: 500,
      body: { message: 'Failed to add plan to cart' }
    }).as('addToCartError');

    // Click add to cart button on first plan
    cy.get('.card').first().find('button').contains('Add to Cart').click();

    // Verify error toast
    cy.contains('Failed to add plan to cart').should('be.visible');
  });
});
