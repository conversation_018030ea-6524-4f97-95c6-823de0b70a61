import { io } from 'socket.io-client';

class WebSocketService {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.listeners = new Map();
    }

    connect(token) {
        if (this.socket && this.isConnected) {
            console.log('WebSocket already connected');
            return;
        }

        const serverUrl = import.meta.env.VITE_API_URL || 'http://localhost:3000';
        
        this.socket = io(serverUrl, {
            auth: {
                token: token
            },
            transports: ['websocket', 'polling'],
            timeout: 20000,
            forceNew: true
        });

        this.socket.on('connect', () => {
            console.log('WebSocket connected');
            this.isConnected = true;
            this.reconnectAttempts = 0;
        });

        this.socket.on('disconnect', (reason) => {
            console.log('WebSocket disconnected:', reason);
            this.isConnected = false;
            
            // Auto-reconnect for certain disconnect reasons
            if (reason === 'io server disconnect') {
                // Server initiated disconnect, don't reconnect automatically
                return;
            }
            
            this.handleReconnect(token);
        });

        this.socket.on('connect_error', (error) => {
            console.error('WebSocket connection error:', error);
            this.isConnected = false;
            this.handleReconnect(token);
        });

        // Listen for plan visibility updates
        this.socket.on('plan-visibility-updated', (data) => {
            console.log('Plan visibility updated:', data);
            this.notifyListeners('plan-visibility-updated', data);
        });

        // Listen for plan updates
        this.socket.on('plan-updated', (data) => {
            console.log('Plan updated:', data);
            this.notifyListeners('plan-updated', data);
        });

        // Listen for new plans
        this.socket.on('plan-added', (data) => {
            console.log('Plan added:', data);
            this.notifyListeners('plan-added', data);
        });
    }

    handleReconnect(token) {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
        
        console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            if (!this.isConnected) {
                this.connect(token);
            }
        }, delay);
    }

    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
            this.isConnected = false;
            this.listeners.clear();
            console.log('WebSocket disconnected manually');
        }
    }

    // Add event listener
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, new Set());
        }
        this.listeners.get(event).add(callback);
    }

    // Remove event listener
    off(event, callback) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).delete(callback);
        }
    }

    // Notify all listeners for an event
    notifyListeners(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('Error in WebSocket event listener:', error);
                }
            });
        }
    }

    // Check if connected
    isSocketConnected() {
        return this.isConnected && this.socket && this.socket.connected;
    }

    // Get connection status
    getStatus() {
        return {
            connected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            socketId: this.socket?.id
        };
    }
}

// Create singleton instance
const websocketService = new WebSocketService();

export default websocketService;
