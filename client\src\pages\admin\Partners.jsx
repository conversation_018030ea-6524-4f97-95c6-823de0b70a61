import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Search, Filter, Plus, Edit2, Trash2, UserCheck, Eye, Power } from 'lucide-react';
import api from '@/lib/axios';
import { useToast } from "@/components/ui/use-toast";
import { useNavigate } from 'react-router-dom';
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";

export default function Partners() {
    const [partners, setPartners] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalItems, setTotalItems] = useState(0);
    const [sortBy, setSortBy] = useState('latest');
    const [countries, setCountries] = useState([]);
    const [selectedCountry, setSelectedCountry] = useState('');
    const { toast } = useToast();
    const navigate = useNavigate();

    useEffect(() => {
        fetchPartners();
    }, [currentPage]);

    const fetchPartners = async () => {
        try {
            const response = await api.get('/api/partners', {
                params: {
                    page: currentPage,
                    limit: 10,
                    search: searchTerm,
                    sortBy,
                    countryId: selectedCountry
                }
            });
            setPartners(Array.isArray(response.data.partners) ? response.data.partners : []);
            setTotalPages(response.data.totalPages);
            setTotalItems(response.data.totalItems);
            setCountries(response.data.countries || []);
        } catch (error) {
            // Only log errors in development
            if (import.meta.env.DEV) {
                // console.error('Error fetching partners:', error);
            }
            toast({
                title: "Error",
                description: "Failed to fetch partners",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    };

    const handleDelete = async (partnerId) => {
        if (!window.confirm('Are you sure you want to delete this partner?')) {
            return;
        }

        try {
            await api.delete(`/api/partners/${partnerId}`);
            toast({
                title: "Success",
                description: "Partner deleted successfully"
            });
            fetchPartners();
        } catch (error) {
            toast({
                title: "Error",
                description: error.response?.data?.message || "Failed to delete partner",
                variant: "destructive"
            });
        }
    };

    const handleToggleStatus = async (partnerId) => {
        try {
            const partner = partners.find(p => p.id === partnerId);
            await api.patch(`/api/partners/${partnerId}/toggle-status`);

            // Update local state
            setPartners(partners.map(p =>
                p.id === partnerId
                    ? { ...p, isActive: !p.isActive }
                    : p
            ));

            toast({
                title: "Success",
                description: `Partner status ${!partner.isActive ? 'activated' : 'deactivated'} successfully`,
            });
        } catch (error) {
            // Only log errors in development
            if (import.meta.env.DEV) {
                // console.error('Error toggling partner status:', error);
            }
            toast({
                title: "Error",
                description: "Failed to toggle partner status",
                variant: "destructive"
            });
        }
    };

    const handleEdit = (partner) => {
        navigate(`/admin/partners/edit/${partner.id}`);
    };

    const handleSearch = (e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1); // Reset to first page when searching
    };

    const handleSortChange = (value) => {
        setSortBy(value);
        setCurrentPage(1);
    };

    const handleCountryChange = (value) => {
        setSelectedCountry(value);
        setCurrentPage(1);
    };

    // Debounce search to avoid too many API calls
    useEffect(() => {
        const timer = setTimeout(() => {
            fetchPartners();
        }, 300);

        return () => clearTimeout(timer);
    }, [searchTerm, currentPage, sortBy, selectedCountry]);

    const filteredPartners = partners;

    return (
        <Card className="p-4">
            <CardHeader className="bg-gradient-to-r from-blue-800 to-blue-600 mb-2 rounded-t-lg">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                    <div>
                        <CardTitle className="text-2xl font-bold tracking-tight text-white">Partners</CardTitle>
                        <p className="text-muted-foreground text-white">Manage your partners here</p>
                    </div>
                    <Button onClick={() => navigate('/admin/partners/add')} className="flex items-center gap-2 bg-purple-500 text-white">
                        <Plus className="w-4 h-4" /> Add Partner
                    </Button>
                </div>
            </CardHeader>


            <CardContent>
                {/* Filters & Actions */}
                <div className="flex flex-col gap-6">

                    {/* Search & Filters */}
                    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                        <div className="relative w-full sm:w-auto">
                            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                placeholder="Search partners..."
                                className="pl-8 w-full sm:w-[300px]"
                                value={searchTerm}
                                onChange={handleSearch}
                            />
                        </div>
                        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                            <select
                                className="h-10 rounded-md border border-input bg-background px-3 py-2 text-sm"
                                value={selectedCountry}
                                onChange={(e) => handleCountryChange(e.target.value)}
                            >
                                <option value="">All Countries</option>
                                {countries.map((country) => (
                                    <option key={country.id} value={country.id}>
                                        {country.name}
                                    </option>
                                ))}
                            </select>
                            <select
                                className="h-10 rounded-md border border-input bg-background px-3 py-2 text-sm"
                                value={sortBy}
                                onChange={(e) => handleSortChange(e.target.value)}
                            >
                                <option value="latest">Latest First</option>
                                <option value="oldest">Oldest First</option>
                                <option value="balance">Balance (Low to High)</option>
                                <option value="balance_desc">Balance (High to Low)</option>
                            </select>
                        </div>
                    </div>

                    {/* Table */}
                    <div className="border rounded-lg overflow-hidden">
                        <Table>
                            <TableHeader>
                                <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100">
                                    <TableHead>Partner ID</TableHead>
                                    <TableHead>Name</TableHead>
                                    <TableHead>Login Email</TableHead>
                                    <TableHead>Business Email</TableHead>
                                    <TableHead>Business Name</TableHead>
                                    <TableHead>Country</TableHead>
                                    <TableHead>Balance</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Join Date</TableHead>
                                    <TableHead className="text-right">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {loading ? (
                                    <TableRow>
                                        <TableCell colSpan={10} className="text-center py-10">
                                            Loading...
                                        </TableCell>
                                    </TableRow>
                                ) : filteredPartners.length === 0 ? (
                                    <TableRow>
                                        <TableCell colSpan={10} className="text-center py-10">
                                            No partners found
                                        </TableCell>
                                    </TableRow>
                                ) : (
                                    filteredPartners.map((partner) => (
                                        <TableRow key={partner.id}>
                                            <TableCell className="font-medium">{partner.id}</TableCell>
                                            <TableCell>{`${partner.firstName} ${partner.lastName}`}</TableCell>
                                            <TableCell>{partner.email}</TableCell>
                                            <TableCell>{partner.businessEmail}</TableCell>
                                            <TableCell>{partner.businessName}</TableCell>
                                            <TableCell>{partner.country?.name}</TableCell>
                                            <TableCell>
                                                ${Number(partner.walletBalance || partner.wallet?.balance || 0).toFixed(2)}
                                            </TableCell>
                                            <TableCell>
                                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${partner.isActive
                                                        ? 'bg-green-100 text-green-800'
                                                        : 'bg-red-100 text-red-800'
                                                    }`}>
                                                    {partner.isActive ? 'Active' : 'Inactive'}
                                                </span>
                                            </TableCell>
                                            <TableCell>{new Date(partner.createdAt).toLocaleDateString()}</TableCell>
                                            <TableCell className="text-right space-x-2">
                                                <div className="flex justify-end gap-2">
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                                        onClick={() => navigate(`/admin/partners/${partner.id}`)}
                                                    >
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        className="h-8 w-8 p-0"
                                                        onClick={() => handleEdit(partner)}
                                                    >
                                                        <Edit2 className="h-4 w-4" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        className={`h-8 w-8 p-0 ${partner.isActive ? 'text-green-600 hover:text-green-700 hover:bg-green-50' : 'text-gray-600 hover:text-gray-700 hover:bg-gray-50'}`}
                                                        onClick={() => handleToggleStatus(partner.id)}
                                                    >
                                                        <Power className="h-4 w-4" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="icon"
                                                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                                                        onClick={() => handleDelete(partner.id)}
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                )}
                            </TableBody>
                        </Table>

                        {/* Pagination */}
                        <div className="flex items-center justify-between px-4 py-4">
                            <p className="text-sm text-muted-foreground">
                                Showing {partners.length} of {totalItems} partners
                            </p>
                            <div className="flex gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                    disabled={currentPage === 1 || loading}
                                >
                                    Previous
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setCurrentPage(prev => prev + 1)}
                                    disabled={currentPage >= totalPages || loading}
                                >
                                    Next
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
