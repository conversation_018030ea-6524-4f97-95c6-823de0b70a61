// Map region codes to their full names
const regionNameMap = {
    'APAC': 'Asia Pacific',
    'EMEA': 'Europe, Middle East & Africa',
    'EU': 'Europe',
    'NAM': 'North America',
    'LATAM': 'Latin America',
    'Global': 'Global',
    'Asia': 'Asia',
    'Europe': 'Europe',
    'Africa': 'Africa',
    'Americas': 'Americas',
    'Oceania': 'Oceania',
    'Caribbean': 'Caribbean'
};

/**
 * Get the full name of a region
 * @param {string} region - The region code or abbreviated name
 * @returns {string} - The full name of the region or the original input if no mapping exists
 */
export const getRegionName = (region) => {
    if (!region) return 'Global';
    const trimmedRegion = region.trim();
    return regionNameMap[trimmedRegion] || trimmedRegion;
};

/**
 * Format an array or comma-separated string of regions into their full names
 * @param {string|string[]} regions - Array of region codes or comma-separated string
 * @returns {string[]} - Array of full region names
 */
export const formatRegions = (regions) => {
    if (!regions) return ['Global'];
    
    if (typeof regions === 'string') {
        return regions.split(',')
            .map(r => r.trim())
            .filter(Boolean)
            .map(getRegionName);
    }
    
    if (Array.isArray(regions)) {
        return regions.map(getRegionName);
    }
    
    return ['Global'];
};

/**
 * Client-side implementation of mapCountriesToRegion
 * Maps a list of country ISO codes to their regions based on the API data
 * @param {string[]} isoCodes - Array of country ISO codes (e.g., ["IN", "FR", "US"])
 * @returns {string} - Returns a string representing the coverage region
 */
export const mapCountriesToRegion = (isoCodes) => {
    if (!Array.isArray(isoCodes) || isoCodes.length === 0) {
        return 'Global';
    }

    // Since we don't have direct access to the database on the client side,
    // we'll return a comma-separated list of regions that will be updated
    // when the server responds with the actual region data
    return 'Global';
}; 