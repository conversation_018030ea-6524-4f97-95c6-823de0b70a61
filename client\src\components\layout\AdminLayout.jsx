import React, { useState } from 'react';
import { Link, useLocation, useNavigate, Outlet } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import {
    Activity,
    CreditCard,
    ShoppingBag,
    Users,
    User,
    UserCog,
    BookOpen,
    Wallet,
    Menu,
    X,
    LogOut,
    ChevronDown,
    Headset
} from 'lucide-react';

const SIDEBAR_WIDTH = '16rem';

const Sidebar = ({ isOpen, toggleSidebar }) => {
    const location = useLocation();

    const menuItems = [
        {
            title: 'Dashboard',
            icon: <Activity className="w-5 h-5" />,
            path: '/admin/dashboard'
        },
        {
            title: 'eSIM Plans',
            icon: <CreditCard className="w-5 h-5" />,
            path: '/admin/esim-plans'
        },
        {
            title: 'Orders',
            icon: <ShoppingBag className="w-5 h-5" />,
            path: '/admin/orders'
        },
        {
            title: 'Partners',
            icon: <Users className="w-5 h-5" />,
            path: '/admin/partners'
        },
        {
            title: 'Providers',
            icon: <User className="w-5 h-5" />,
            path: '/admin/providers'
        },
        {
            title: 'Admin Staff',
            icon: <UserCog className="w-5 h-5" />,
            path: '/admin/staffs'
        },
        {
            title: 'Knowledge Base',
            icon: <BookOpen className="w-5 h-5" />,
            path: '/admin/knowledge-base'
        },
        {
            title: 'Central Wallet',
            icon: <Wallet className="w-5 h-5" />,
            path: '/admin/central-wallet'
        },
        {
            title: 'Support',
            icon: <Headset  className="w-5 h-5" />,
            path: 'https://support.vizlync.net/login',
            external: true
        }
    ];

    return (
        <>
            {/* Backdrop for mobile */}
            {isOpen && (
                <div 
                    className="fixed inset-0 bg-black/50 lg:hidden z-40"
                    onClick={toggleSidebar}
                />
            )}
            
            {/* Sidebar */}
            <aside
                className={`fixed left-0 top-0 h-screen bg-white border-r shadow-sm transition-transform duration-300 z-50 ${
                    isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
                }`}
                style={{ width: SIDEBAR_WIDTH }}
            >
                <div className="flex flex-col h-full">
                    <div className="flex items-center justify-between p-4 border-b">
                        <Link to="/admin/dashboard" className="flex items-center space-x-2">
                            {/* <img src="/logo.png" alt="eSIM Logo" className="h-8" /> */}
                            <span className="font-bold text-xl">eSIM Admin</span>
                        </Link>
                        <button onClick={toggleSidebar} className="lg:hidden">
                            <X className="w-6 h-6" />
                        </button>
                    </div>

                    <nav className="flex-1 overflow-y-auto py-6">
                        <ul className="space-y-1 px-3">
                            {menuItems.map((item) => (
                                <li key={item.path}>
                                    {item.external ? (
                                        <a
                                            href={item.path}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors hover:bg-gray-100"
                                        >
                                            {item.icon}
                                            <span>{item.title}</span>
                                        </a>
                                    ) : (
                                    <Link
                                        to={item.path}
                                        className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                                            location.pathname === item.path
                                                ? 'bg-blue-800 text-white'
                                                : 'hover:bg-gray-100'
                                        }`}
                                    >
                                        {item.icon}
                                        <span>{item.title}</span>
                                    </Link>
                                    )}
                                </li>
                            ))}
                        </ul>
                    </nav>
                </div>
            </aside>
        </>
    );
};

const Navbar = ({ toggleSidebar }) => {
    const { user, logout } = useAuth();
    const [showProfileMenu, setShowProfileMenu] = useState(false);
    const navigate = useNavigate();

    const handleLogout = async () => {
        await logout();
        navigate('/login');
    };

    return (
        <header className="fixed top-0 right-0 left-0 lg:left-[16rem] h-16 bg-white border-b z-30">
            <div className="flex h-full items-center justify-between px-4">
                <button
                    onClick={toggleSidebar}
                    className="lg:hidden p-2 rounded-lg hover:bg-gray-100 bg-blue-800 text-white"
                >
                    <Menu className="w-6 h-6" />
                </button>

                <div className="ml-auto relative">
                    <button
                        onClick={() => setShowProfileMenu(!showProfileMenu)}
                        className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 bg-white"
                    >
                        <div className="w-8 h-8 rounded-full bg-blue-900 text-white flex items-center justify-center">
                            {user?.email?.charAt(0).toUpperCase()}
                        </div>
                        <span className="hidden sm:inline">{user?.email}</span>
                        <ChevronDown className="w-4 h-4" />
                    </button>

                    {showProfileMenu && (
                        <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border py-1">
                            <button
                                onClick={handleLogout}
                                className="w-full px-4 py-2 text-left text-red-600 hover:bg-gray-100 flex items-center space-x-2 bg-white"
                            >
                                <LogOut className="w-4 h-4" />
                                <span>Logout</span>
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </header>
    );
};

export default function AdminLayout() {
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);

    return (
        <div className="min-h-screen w-screen bg-gray-50">
            <Sidebar isOpen={isSidebarOpen} toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)} />
            <Navbar toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)} />
            
            <main className="lg:ml-[16rem] pt-16">
                <div className="p-6 min-h-[calc(100vh-4rem)]">
                    <Outlet />
                </div>
            </main>
        </div>
    );
}
