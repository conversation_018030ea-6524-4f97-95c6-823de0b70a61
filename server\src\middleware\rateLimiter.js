const rateLimit = require('express-rate-limit');

const loginLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: process.env.NODE_ENV === 'test' ? 1000 : 5, // Higher limit for testing
    message: { message: 'Too many login attempts. Please try again after 15 minutes.' },
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers

    keyGenerator: (req) => {
        // Use X-Forwarded-For header when behind proxy, fallback to connection IP
        const forwarded = req.headers['x-forwarded-for'];
        const ip = forwarded ? forwarded.split(',')[0].trim() : req.connection.remoteAddress;

        // For testing, use a combination of IP and email to allow more requests
        return process.env.NODE_ENV === 'test'
            ? `${ip}-${req.body.email}`
            : ip;
    },

    // Skip rate limiting for trusted internal requests
    skip: (req) => {
        const forwarded = req.headers['x-forwarded-for'];
        const ip = forwarded ? forwarded.split(',')[0].trim() : req.connection.remoteAddress;
        // Skip rate limiting for internal Docker network requests
        return ip && (ip.startsWith('172.') || ip.startsWith('10.') || ip === '127.0.0.1');
    }
});

// General API rate limiter
const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 300, // 300 requests per windowMs
    message: { message: 'Too many requests. Please try again after 15 minutes.' },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
        const forwarded = req.headers['x-forwarded-for'];
        return forwarded ? forwarded.split(',')[0].trim() : req.connection.remoteAddress;
    }
});

// Less strict rate limiter for API endpoints used by client-side UI
const clientApiLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 120, // 120 requests per minute (allowing for multiple components loading)
    message: { message: 'Too many requests. Please try again after a minute.' },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
        const forwarded = req.headers['x-forwarded-for'];
        return forwarded ? forwarded.split(',')[0].trim() : req.connection.remoteAddress;
    }
});

// Rate limiter specific for the sync endpoint, allowing more requests
const syncLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 10, // 10 sync operations per hour
    message: { message: 'Too many sync operations. Please try again later.' },
    standardHeaders: true,
    legacyHeaders: false
});

// Partner plan API rate limiter (to avoid the 429 errors we're seeing)
const partnerPlanLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 200, // 200 requests per minute
    message: { message: 'Too many requests. Please try again after a minute.' },
    standardHeaders: true,
    legacyHeaders: false
});

module.exports = {
    loginLimiter,
    apiLimiter,
    clientApiLimiter,
    syncLimiter,
    partnerPlanLimiter
};
