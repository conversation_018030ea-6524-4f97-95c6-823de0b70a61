const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const { User } = require('../models');

class WebSocketService {
    constructor() {
        this.io = null;
        this.connectedUsers = new Map(); // userId -> socket
    }

    initialize(server) {
        this.io = new Server(server, {
            cors: {
                origin: process.env.CLIENT_URL || "http://localhost:3000",
                methods: ["GET", "POST"],
                credentials: true
            }
        });

        // Authentication middleware for WebSocket
        this.io.use(async (socket, next) => {
            try {
                const token = socket.handshake.auth.token;
                if (!token) {
                    return next(new Error('Authentication error: No token provided'));
                }

                const decoded = jwt.verify(token, process.env.JWT_SECRET);
                const user = await User.findByPk(decoded.id);
                
                if (!user) {
                    return next(new Error('Authentication error: User not found'));
                }

                socket.userId = user.id;
                socket.userRole = user.role;
                next();
            } catch (error) {
                next(new Error('Authentication error: Invalid token'));
            }
        });

        this.io.on('connection', (socket) => {
            console.log(`User ${socket.userId} (${socket.userRole}) connected via WebSocket`);
            
            // Store the connection
            this.connectedUsers.set(socket.userId, socket);

            // Join role-specific rooms
            socket.join(socket.userRole);
            
            // Handle disconnection
            socket.on('disconnect', () => {
                console.log(`User ${socket.userId} disconnected from WebSocket`);
                this.connectedUsers.delete(socket.userId);
            });

            // Handle plan visibility updates (admin only)
            socket.on('plan-visibility-changed', (data) => {
                if (socket.userRole === 'admin') {
                    this.broadcastPlanVisibilityChange(data);
                }
            });
        });

        console.log('WebSocket service initialized');
    }

    // Broadcast plan visibility changes to all partners
    broadcastPlanVisibilityChange(data) {
        if (!this.io) return;

        console.log('Broadcasting plan visibility change:', data);
        
        // Send to all partners
        this.io.to('partner').emit('plan-visibility-updated', {
            type: data.type, // 'single' or 'provider'
            planId: data.planId,
            providerId: data.providerId,
            status: data.status,
            timestamp: Date.now()
        });
    }

    // Broadcast plan updates (for future use)
    broadcastPlanUpdate(planData) {
        if (!this.io) return;

        this.io.to('partner').emit('plan-updated', {
            plan: planData,
            timestamp: Date.now()
        });
    }

    // Broadcast new plans (for future use)
    broadcastNewPlan(planData) {
        if (!this.io) return;

        this.io.to('partner').emit('plan-added', {
            plan: planData,
            timestamp: Date.now()
        });
    }

    // Get connected users count
    getConnectedUsersCount() {
        return this.connectedUsers.size;
    }

    // Get connected users by role
    getConnectedUsersByRole(role) {
        const users = [];
        this.connectedUsers.forEach((socket, userId) => {
            if (socket.userRole === role) {
                users.push(userId);
            }
        });
        return users;
    }
}

module.exports = new WebSocketService();
