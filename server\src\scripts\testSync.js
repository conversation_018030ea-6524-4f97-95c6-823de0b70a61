require('dotenv').config();
const { Provider, EsimPlan } = require('../models');
const providerFactory = require('../services/provider.factory');
const sequelize = require('../config/database');

async function testSync() {
    try {
        await sequelize.authenticate();
        console.log('Database connection established.');

        // Get Mobimatter provider
        const provider = await Provider.findOne({
            where: { 
                name: 'Mobimatter',
                type: 'API',
                status: 'active'
            }
        });

        if (!provider) {
            console.log('Mobimatter provider not found');
            return;
        }

        console.log('\nTesting with provider:', {
            id: provider.id,
            name: provider.name,
            type: provider.type,
            status: provider.status,
            hasApiKey: !!provider.apiKey,
            hasMerchantId: !!provider.merchantId
        });

        // Get provider service
        const providerService = providerFactory.getProvider(provider.name);
        console.log('\nFetching plans from Mobimatter...');
        
        // Fetch plans
        const externalPlans = await providerService.getProducts();
        console.log(`\nFetched ${externalPlans.length} plans from Mobimatter`);

        if (externalPlans.length > 0) {
            console.log('\nSample plan:', {
                id: externalPlans[0].id,
                name: externalPlans[0].name,
                price: externalPlans[0].price,
                dataAmount: externalPlans[0].dataAmount,
                dataUnit: externalPlans[0].dataUnit,
                validityDays: externalPlans[0].validityDays
            });

            // Extract plan data from PLAN_DATA_LIMIT
            const planDataLimit = externalPlans[0].providerMetadata?.productDetails?.find(
                detail => detail.name === 'PLAN_DATA_LIMIT'
            )?.value;
            
            // Test standardization
            console.log('\nTesting plan standardization...');
            const standardizedPlan = {
                externalProductId: externalPlans[0].id,
                externalSkuId: externalPlans[0].id,
                name: externalPlans[0].name,
                buyingPrice: externalPlans[0].price,
                planData: planDataLimit ? parseFloat(planDataLimit) : null,
                planDataUnit: planDataLimit ? 'GB' : null,
                validityDays: externalPlans[0].validityDays,
                providerId: provider.id,
                networkName: externalPlans[0].networkName,
                region: externalPlans[0].supportedCountries[0],
                supportedRegions: JSON.stringify(externalPlans[0].supportedCountries),
                description: `\n\nAdditional Information:\n${externalPlans[0].additionalDetails || ''}\n\nUsage Tracking:\n${externalPlans[0].usageTracking || ''}`,
                category: 'esim_realtime',
                planCategory: 'Data Only',
                planType: externalPlans[0].isUnlimited || externalPlans[0].customData?.find(d => d.name === 'UNLIMITED')?.value === '1' ? 'Unlimited' : 'Fixed',
                features: JSON.stringify([]),
                providerMetadata: JSON.stringify(externalPlans[0].providerMetadata),
                profile: 'local',
                status: 'visible',
                isActive: true,
                startDateEnabled: false,
                hotspot: 'Available',
                activationPolicy: 'Activation upon first usage',
                speed: 'Restricted',
                stockThreshold: 10
            };
            console.log('\nStandardized plan:', standardizedPlan);

            // Try to save the plan
            console.log('\nAttempting to save plan to database...');
            const [plan, created] = await EsimPlan.findOrCreate({
                where: {
                    externalProductId: standardizedPlan.externalProductId
                },
                defaults: {
                    ...standardizedPlan,
                    providerId: provider.id
                }
            });

            if (created) {
                console.log('\nNew plan created:', {
                    id: plan.id,
                    name: plan.name,
                    externalProductId: plan.externalProductId
                });
            } else {
                console.log('\nPlan already exists, updating...');
                await plan.update({
                    ...standardizedPlan,
                    providerId: provider.id
                });
                console.log('\nPlan updated:', {
                    id: plan.id,
                    name: plan.name,
                    externalProductId: plan.externalProductId
                });
            }
        }

    } catch (error) {
        console.error('Error in sync test:', error);
    } finally {
        await sequelize.close();
    }
}

testSync(); 