version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: esim-backend
    restart: always
    env_file:
      - ./server/.env
    networks:
      - esim-network

  nginx:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    container_name: nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/certbot/conf:/etc/letsencrypt
      - certbot_webroot:/var/www/certbot
    depends_on:
      - app
    networks:
      - esim-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/.well-known/acme-challenge/test-file"]
      interval: 30s
      timeout: 10s
      retries: 3

  certbot:
    image: certbot/certbot
    container_name: certbot
    volumes:
      - ./nginx/certbot/conf:/etc/letsencrypt
      - certbot_webroot:/var/www/certbot
    # Simpler command that just sleeps - we'll run certbot manually
    entrypoint: /bin/sh -c 'trap exit TERM; while :; do sleep 12h; done'

networks:
  esim-network:
    driver: bridge

volumes:
  certbot_webroot:
    driver: local
