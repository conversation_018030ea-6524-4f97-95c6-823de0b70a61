const express = require('express');
const router = express.Router();
const { isAuthenticated, isAdmin } = require('../middleware/auth');
const knowledgeBaseController = require('../controllers/knowledgeBaseController');

// Apply authentication middleware to all routes
router.use(isAuthenticated);

// Get all articles (filtered by user role)
router.get('/', knowledgeBaseController.getArticles);

// Get single article
router.get('/:id', knowledgeBaseController.getArticle);

// Admin-only routes
router.post('/', isAdmin, knowledgeBaseController.createArticle);
router.put('/:id', isAdmin, knowledgeBaseController.updateArticle);
router.delete('/:id', isAdmin, knowledgeBaseController.deleteArticle);
router.patch('/:id/status', isAdmin, knowledgeBaseController.updateArticleStatus);

module.exports = router;
