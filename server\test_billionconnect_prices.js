// Load environment variables
require('dotenv').config();

const billionconnectService = require('./src/services/billionconnect.service');

async function testBillionConnectPrices() {
    console.log('🚀 Testing BillionConnect F003 Price API...\n');

    try {
        // First, get available commodities to get real SKU IDs
        console.log('📦 Step 1: Getting available commodities to get real SKU IDs...');
        const commodities = await billionconnectService.getCommodities();
        
        if (commodities.length === 0) {
            console.log('❌ No commodities available for price testing');
            return;
        }

        console.log(`✅ Found ${commodities.length} commodities`);
        
        // Get first few SKU IDs for testing
        const testSkuIds = commodities.slice(0, 3).map(commodity => commodity.skuId);
        console.log('📋 Test SKU IDs:', testSkuIds);

        // Test 1: Single plan price
        console.log('\n💰 Step 2: Testing single plan price (F003)...');
        const firstSkuId = testSkuIds[0];
        console.log(`   Testing SKU: ${firstSkuId}`);
        
        const singlePrice = await billionconnectService.getSinglePlanPrice(firstSkuId, 1);
        
        if (singlePrice) {
            console.log('✅ Single plan price retrieved successfully!');
            console.log('   SKU ID:', singlePrice.skuId);
            console.log('   Settlement Price:', singlePrice.settlementPrice);
            console.log('   Copies:', singlePrice.copies);
            console.log('   Currency:', singlePrice.currency || 'Not specified');
        } else {
            console.log('⚠️  No price data returned for single plan');
        }

        // Test 2: Multiple plan prices
        console.log('\n💰 Step 3: Testing multiple plan prices...');
        const priceRequests = testSkuIds.map(skuId => ({
            skuId: skuId,
            copies: Math.floor(Math.random() * 3) + 1 // Random quantity 1-3
        }));

        console.log('   Price requests:', priceRequests);
        
        const multiplePrices = await billionconnectService.getPlanPrices(priceRequests);
        
        if (multiplePrices.length > 0) {
            console.log(`✅ Multiple plan prices retrieved successfully! (${multiplePrices.length} results)`);
            multiplePrices.forEach((price, index) => {
                console.log(`   Plan ${index + 1}:`);
                console.log(`     SKU ID: ${price.skuId}`);
                console.log(`     Settlement Price: ${price.settlementPrice}`);
                console.log(`     Copies: ${price.copies}`);
                console.log(`     Currency: ${price.currency || 'Not specified'}`);
            });
        } else {
            console.log('⚠️  No price data returned for multiple plans');
        }

        // Test 3: Transformed prices
        console.log('\n🔄 Step 4: Testing price data transformation...');
        const transformedPrices = await billionconnectService.getTransformedPrices(priceRequests);
        
        if (transformedPrices.length > 0) {
            console.log(`✅ Price data transformed successfully! (${transformedPrices.length} results)`);
            transformedPrices.forEach((price, index) => {
                console.log(`   Transformed Price ${index + 1}:`);
                console.log(`     SKU ID: ${price.skuId}`);
                console.log(`     Buying Price: $${price.buyingPrice}`);
                console.log(`     Quantity: ${price.quantity}`);
                console.log(`     Total Price: $${price.totalPrice}`);
                console.log(`     Price Per Unit: $${price.pricePerUnit}`);
                console.log(`     Currency: ${price.currency}`);
            });
        } else {
            console.log('⚠️  No transformed price data available');
        }

        // Test 4: Products with prices
        console.log('\n📦💰 Step 5: Testing products with prices...');
        const productsWithPrices = await billionconnectService.getProductsWithPrices(testSkuIds);
        
        if (productsWithPrices.length > 0) {
            console.log(`✅ Products with prices retrieved successfully! (${productsWithPrices.length} results)`);
            productsWithPrices.forEach((product, index) => {
                console.log(`\n   Product ${index + 1}:`);
                console.log(`     Name: ${product.name}`);
                console.log(`     SKU ID: ${product.externalSkuId}`);
                console.log(`     Data: ${product.planData} ${product.planDataUnit}`);
                console.log(`     Validity: ${product.validityDays} days`);
                console.log(`     Buying Price: $${product.buyingPrice}`);
                console.log(`     Has Price Data: ${product.hasPriceData ? 'Yes' : 'No'}`);
                console.log(`     Country: ${product.country.name}`);
                console.log(`     Network: ${product.networkName} (${product.networkType})`);
                console.log(`     Hotspot: ${product.hotspot ? 'Supported' : 'Not Supported'}`);
            });
        } else {
            console.log('⚠️  No products with price data available');
        }

        // Test 5: Error handling
        console.log('\n🛡️  Step 6: Testing error handling...');
        
        // Test with invalid SKU ID
        const invalidPrice = await billionconnectService.getSinglePlanPrice('INVALID_SKU', 1);
        console.log(`✅ Invalid SKU handling: ${invalidPrice ? 'Returned data' : 'Properly returned null'}`);
        
        // Test with empty requests
        const emptyPrices = await billionconnectService.getPlanPrices([]);
        console.log(`✅ Empty requests handling: ${emptyPrices.length === 0 ? 'Properly handled' : 'Unexpected result'}`);

        console.log('\n🎉 BillionConnect F003 Price API testing completed successfully!');
        console.log('\n📊 Summary:');
        console.log(`   • Commodities available: ${commodities.length}`);
        console.log(`   • Price requests tested: ${priceRequests.length}`);
        console.log(`   • Transformed prices: ${transformedPrices.length}`);
        console.log(`   • Products with prices: ${productsWithPrices.length}`);
        
        console.log('\n💡 Key Features Verified:');
        console.log('   ✅ F003 API integration working');
        console.log('   ✅ Single plan price retrieval');
        console.log('   ✅ Multiple plan price retrieval');
        console.log('   ✅ Price data transformation (settlementPrice → buyingPrice, copies → quantity)');
        console.log('   ✅ Products with integrated price data');
        console.log('   ✅ Error handling for invalid requests');

    } catch (error) {
        console.error('❌ Price API test failed:', error.message);
        
        if (error.response) {
            console.error('   HTTP Status:', error.response.status);
            console.error('   Response Data:', error.response.data);
        }
        
        if (error.message.includes('BillionConnect API Error')) {
            console.log('\n💡 Troubleshooting tips:');
            console.log('   - Verify your BILLIONCONNECT_CHANNEL_ID is correct');
            console.log('   - Verify your BILLIONCONNECT_APP_SECRET is correct');
            console.log('   - Check if your account has access to the F003 trade type');
            console.log('   - Ensure the SKU IDs exist and are valid');
        }
    }
}

// Run the test if this file is executed directly
if (require.main === module) {
    testBillionConnectPrices()
        .then(() => {
            console.log('\n✨ Price API test completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 Price API test failed:', error);
            process.exit(1);
        });
}

module.exports = { testBillionConnectPrices };
