import http from 'k6/http';
import { sleep } from 'k6';
import { CONFIG } from './config.js';
import { checkResponse } from './helpers.js';

export const options = {
    thresholds: CONFIG.thresholds,
    scenarios: {
        auth_flow: CONFIG.scenarios.load
    }
};

export function setup() {
    return { baseUrl: CONFIG.baseUrl };
}

export default function (data) {
    const baseUrl = data.baseUrl;

    // Login
    const loginResponse = http.post(`${baseUrl}/api/auth/login`, JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
    }), {
        headers: { 'Content-Type': 'application/json' }
    });
    checkResponse(loginResponse);
    const tempToken = loginResponse.json('tempToken');

    sleep(1);

    // Send OTP
    const otpResponse = http.post(`${baseUrl}/api/auth/send-otp`, JSON.stringify({
        tempToken
    }), {
        headers: { 'Content-Type': 'application/json' }
    });
    checkResponse(otpResponse);

    sleep(1);

    // Verify OTP
    const verifyResponse = http.post(`${baseUrl}/api/auth/verify-otp`, JSON.stringify({
        otp: '123456',
        tempToken
    }), {
        headers: { 'Content-Type': 'application/json' }
    });
    checkResponse(verifyResponse);

    sleep(1);
}
