USE esim_platform;

-- Drop existing foreign key constraints
SET @constraint_name = (
    SELECT CONSTRAINT_NAME 
    FROM information_schema.KEY_COLUMN_USAGE 
    WHERE TABLE_NAME = 'Users' 
    AND COLUMN_NAME = 'billingCountryId' 
    AND REFERENCED_TABLE_NAME = 'country'
    AND CONSTRAINT_SCHEMA = 'esim_platform'
);

SET @sql = CONCAT('ALTER TABLE Users DROP FOREIGN KEY ', @constraint_name);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @constraint_name = (
    SELECT CONSTRAINT_NAME 
    FROM information_schema.KEY_COLUMN_USAGE 
    WHERE TABLE_NAME = 'Users' 
    AND COLUMN_NAME = 'countryId' 
    AND REFERENCED_TABLE_NAME = 'country'
    AND CONSTRAINT_SCHEMA = 'esim_platform'
);

SET @sql = CONCAT('ALTER TABLE Users DROP FOREIGN KEY ', @constraint_name);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Rename table if it exists
RENAME TABLE IF EXISTS country TO Countries_old;

-- Create the correct Countries table if it doesn't exist
CREATE TABLE IF NOT EXISTS Countries (
    id CHAR(2) PRIMARY KEY,
    iso3 CHAR(3) NOT NULL,
    name VARCHAR(100) NOT NULL,
    phoneCode VARCHAR(10),
    currencyCode CHAR(3),
    currencySymbol VARCHAR(5),
    region VARCHAR(50),
    isActive BOOLEAN DEFAULT true,
    createdAt DATETIME NOT NULL,
    updatedAt DATETIME NOT NULL,
    UNIQUE KEY idx_country_iso3 (iso3),
    INDEX idx_country_search (name, region, isActive)
) ENGINE=InnoDB;

-- Copy data from old table if it exists
INSERT IGNORE INTO Countries (id, iso3, name, phoneCode, currencyCode, currencySymbol, region, isActive, createdAt, updatedAt)
SELECT id, iso3, name, phoneCode, currencyCode, currencySymbol, region, isActive, NOW(), NOW()
FROM Countries_old;

-- Drop old table
DROP TABLE IF EXISTS Countries_old;

-- Add foreign key constraints back
ALTER TABLE Users
ADD CONSTRAINT fk_users_country
FOREIGN KEY (countryId) REFERENCES Countries(id)
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE Users
ADD CONSTRAINT fk_users_billing_country
FOREIGN KEY (billingCountryId) REFERENCES Countries(id)
ON DELETE SET NULL ON UPDATE CASCADE;

-- Insert Spain if it doesn't exist
INSERT IGNORE INTO Countries (id, iso3, name, phoneCode, currencyCode, currencySymbol, region, isActive, createdAt, updatedAt)
VALUES ('ES', 'ESP', 'Spain', '+34', 'EUR', '€', 'Europe', true, NOW(), NOW());
