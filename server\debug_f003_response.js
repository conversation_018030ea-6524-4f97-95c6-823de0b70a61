// Load environment variables
require('dotenv').config();

const billionconnectService = require('./src/services/billionconnect.service');

async function debugF003Response() {
    console.log('🔍 Debugging BillionConnect F003 Response Format...\n');

    try {
        // First get a real SKU ID
        console.log('1. Getting real SKU ID...');
        const commodities = await billionconnectService.getCommodities();
        const testSkuId = commodities[0].skuId;
        console.log('Test SKU ID:', testSkuId);

        // Make raw F003 request to see exact response
        console.log('\n2. Making raw F003 request...');
        const requestBody = {
            tradeType: "F003",
            tradeTime: billionconnectService.getGMT8Time(),
            tradeData: {
                salesMethod: "5",
                language: "2",
                planList: [
                    {
                        skuId: testSkuId,
                        copies: 1
                    }
                ]
            }
        };

        console.log('Request body:', JSON.stringify(requestBody, null, 2));

        const response = await billionconnectService.makeRequest(requestBody);
        
        console.log('\n3. Raw F003 Response:');
        console.log('Response type:', typeof response);
        console.log('Response is array:', Array.isArray(response));
        console.log('Response length:', response?.length);
        console.log('Full response:', JSON.stringify(response, null, 2));

        if (Array.isArray(response) && response.length > 0) {
            console.log('\n4. First response item analysis:');
            const firstItem = response[0];
            console.log('First item keys:', Object.keys(firstItem));
            console.log('First item:', JSON.stringify(firstItem, null, 2));
        }

    } catch (error) {
        console.error('❌ Debug failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

debugF003Response();
