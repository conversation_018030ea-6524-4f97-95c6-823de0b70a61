const { Model, DataTypes } = require('sequelize');
const sequelize = require('../config/database');

class EsimPlanStockHistory extends Model {
    static associate(models) {
        // Associate with EsimPlan
        EsimPlanStockHistory.belongsTo(models.EsimPlan, {
            foreignKey: 'esimPlanId',
            as: 'plan'
        });

        // Associate with Order
        EsimPlanStockHistory.belongsTo(models.Order, {
            foreignKey: 'orderId',
            as: 'order'
        });

        // Associate with User (creator)
        EsimPlanStockHistory.belongsTo(models.User, {
            foreignKey: 'createdBy',
            as: 'creator'
        });
    }
}

EsimPlanStockHistory.init({
    id: {
        type: DataTypes.STRING,
        primaryKey: true,
    },
    esimPlanId: {
        type: DataTypes.STRING,
        allowNull: false,
        references: {
            model: 'esimplans',
            key: 'id'
        }
    },
    esimStockId: {
        type: DataTypes.STRING,
        allowNull: false
    },
    iccid: {
        type: DataTypes.STRING,
        allowNull: false
    },
    smdpAddress: {
        type: DataTypes.STRING,
        allowNull: false
    },
    lpaString: {
        type: DataTypes.TEXT,
        allowNull: false
    },
    accessPointName: {
        type: DataTypes.STRING,
        allowNull: true
    },
    activationCode: {
        type: DataTypes.STRING,
        allowNull: true
    },
    phoneNumber: {
        type: DataTypes.STRING,
        allowNull: true
    },
    orderId: {
        type: DataTypes.STRING(20),
        allowNull: true,
        references: {
            model: 'orders',
            key: 'id'
        }
    },
    orderDate: {
        type: DataTypes.DATE,
        allowNull: true
    },
    quantity: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1
    },
    status: {
        type: DataTypes.ENUM('assigned', 'activated', 'expired', 'cancelled'),
        allowNull: false,
        defaultValue: 'assigned'
    },
    reason: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: 'Order placement'
    },
    createdBy: {
        type: DataTypes.STRING,
        allowNull: false,
        references: {
            model: 'users',
            key: 'id'
        }
    },
    providerActivationDetails: {
        type: DataTypes.JSON,
        allowNull: true
    },
    lastProviderSync: {
        type: DataTypes.DATE,
        allowNull: true
    }
}, {
    sequelize,
    modelName: 'EsimPlanStockHistory',
    tableName: 'esimplanstockhistory',
    timestamps: true
});

module.exports = EsimPlanStockHistory;
