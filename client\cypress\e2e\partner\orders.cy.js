/// <reference types="cypress" />

describe('Order Placement Flow', () => {
  beforeEach(() => {
    const apiUrl = Cypress.env('apiUrl');
    cy.task('log', `Using API URL: ${apiUrl}`);

    // Clear localStorage
    cy.clearLocalStorage();

    // Set up API mocks before visiting the page
    cy.intercept('POST', `${apiUrl}/api/auth/login`, {
      statusCode: 200,
      body: {
        message: 'Login successful',
        tempToken: 'temp-token-123'
      }
    }).as('login');

    cy.intercept('POST', `${apiUrl}/api/auth/send-otp`, {
      statusCode: 200,
      body: {
        message: 'OTP sent successfully'
      }
    }).as('sendOtp');

    cy.intercept('POST', `${apiUrl}/api/auth/verify-otp`, {
      statusCode: 200,
      body: {
        user: {
          id: 'test-partner-id',
          email: '<EMAIL>',
          role: 'partner'
        },
        token: 'valid-token-123'
      }
    }).as('verifyOtp');

    cy.task('log', 'Starting login process');

    // Visit login page
    cy.visit('/login');
    cy.url().should('include', '/login');

    // Login form should be visible
    cy.get('input[name="email"]').should('be.visible').type('<EMAIL>');
    cy.get('input[name="password"]').should('be.visible').type('password123');
    cy.get('button[type="submit"]').should('be.visible').click();

    // Wait for login response
    cy.wait('@login').then((interception) => {
      expect(interception.response.statusCode).to.equal(200);
      cy.task('log', 'Login successful');
    });

    // OTP form should appear
    cy.contains(/Verification Required|OTP Verification/).should('be.visible');
    cy.contains('button', /Send OTP|Get OTP/).should('be.visible').click();

    // Wait for send OTP response
    cy.wait('@sendOtp').then((interception) => {
      expect(interception.response.statusCode).to.equal(200);
      cy.task('log', 'OTP sent successfully');
    });

    // Enter and submit OTP
    cy.get('input[name="otp"]').should('be.visible').type('123456');
    cy.get('button[type="submit"]').should('be.visible').click();

    // Wait for OTP verification
    cy.wait('@verifyOtp').then((interception) => {
      expect(interception.response.statusCode).to.equal(200);
      const token = interception.response.body.token;
      
      // Set token in localStorage
      cy.window().then((win) => {
        win.localStorage.setItem('token', token);
        cy.task('log', 'Token set in localStorage');
      });
    });

    // Ensure we're redirected to dashboard
    cy.url().should('include', '/dashboard');
    cy.task('log', 'Successfully logged in and redirected to dashboard');
  });

  it('should add plan to cart and place order successfully', () => {
    const apiUrl = Cypress.env('apiUrl');
    cy.task('log', 'Starting add plan to cart test');

    // Mock API endpoints before visiting the page
    cy.intercept('GET', `${apiUrl}/api/esim-plans/partner*`, {
      statusCode: 200,
      body: {
        plans: [{
          id: '12345',
          name: 'Test Plan',
          description: 'Test Description',
          planData: '5',
          planDataUnit: 'GB',
          validityDays: 30,
          sellingPrice: 50.00,
          planType: 'Fixed',
          planCategory: 'Data Only',
          status: 'published',
          provider: {
            name: 'Test Provider'
          }
        }],
        countries: [],
        regions: []
      }
    }).as('getPlans');

    cy.intercept('GET', `${apiUrl}/api/v1/wallet/balance`, {
      statusCode: 200,
      body: {
        balance: 1000.00
      }
    }).as('getWallet');

    cy.intercept('POST', `${apiUrl}/api/cart`, {
      statusCode: 201,
      body: { message: 'Added to cart successfully' }
    }).as('addToCart');

    cy.intercept('GET', `${apiUrl}/api/cart`, {
      statusCode: 200,
      body: {
        cartItems: [{
          id: '67890',
          quantity: 1,
          EsimPlan: {
            id: '12345',
            name: 'Test Plan',
            sellingPrice: 50.00
          }
        }]
      }
    }).as('getCart');

    cy.intercept('POST', `${apiUrl}/api/v1/orders/create`, {
      statusCode: 201,
      body: {
        message: 'Orders created successfully',
        orders: ['order123'],
        totalAmount: 50.00
      }
    }).as('createOrder');

    // Visit plans page
    cy.visit('/dashboard/plans');
    cy.wait('@getPlans').then(() => {
      cy.task('log', 'Plans loaded successfully');
    });

    // Wait for plans to be visible
    cy.contains('.card', 'Test Plan').should('be.visible').within(() => {
      cy.contains('button', 'Add to Cart').should('be.visible').click();
    });

    // Wait for add to cart response
    cy.wait('@addToCart').then(() => {
      cy.task('log', 'Plan added to cart');
    });

    // Verify success toast
    cy.contains('Added to cart successfully').should('be.visible');

    // Go to cart
    cy.visit('/dashboard/cart');
    cy.wait('@getCart').then(() => {
      cy.task('log', 'Cart loaded successfully');
    });

    // Verify cart items are visible
    cy.contains('Test Plan').should('be.visible');
    cy.contains('$50.00').should('be.visible');

    // Place order
    cy.contains('button', 'Place Order').should('be.visible').click();

    // Wait for order creation
    cy.wait('@createOrder').then(() => {
      cy.task('log', 'Order placed successfully');
    });

    // Verify success message
    cy.contains('Orders created successfully').should('be.visible');

    // Verify redirect to orders page
    cy.url().should('include', '/dashboard/orders');
  });

  it('should show error when wallet balance is insufficient', () => {
    const apiUrl = Cypress.env('apiUrl');
    cy.task('log', 'Starting wallet balance insufficient test');

    // Mock cart with expensive plan
    cy.intercept('GET', `${apiUrl}/api/cart`, {
      statusCode: 200,
      body: {
        cartItems: [{
          id: '67890',
          quantity: 1,
          EsimPlan: {
            id: '12345',
            name: 'Expensive Plan',
            sellingPrice: 2000.00
          }
        }]
      }
    }).as('getCart');

    // Mock wallet with low balance
    cy.intercept('GET', `${apiUrl}/api/v1/wallet/balance`, {
      statusCode: 200,
      body: {
        balance: 100.00
      }
    }).as('getWallet');

    // Mock order error
    cy.intercept('POST', `${apiUrl}/api/v1/orders/create`, {
      statusCode: 400,
      body: {
        message: 'Insufficient wallet balance',
        required: 2000.00,
        available: 100.00
      }
    }).as('createOrderError');

    // Go to cart
    cy.visit('/dashboard/cart');
    cy.wait('@getCart');
    cy.wait('@getWallet');

    // Try to place order
    cy.contains('button', 'Place Order').should('be.visible').click();
    cy.wait('@createOrderError');

    // Verify error message
    cy.contains('Insufficient wallet balance').should('be.visible');
  });

  it('should show error when stock is not available', () => {
    const apiUrl = Cypress.env('apiUrl');
    cy.task('log', 'Starting stock unavailable test');

    // Mock cart with out of stock plan
    cy.intercept('GET', `${apiUrl}/api/cart`, {
      statusCode: 200,
      body: {
        cartItems: [{
          id: '67890',
          quantity: 1,
          EsimPlan: {
            id: '12345',
            name: 'Out of Stock Plan',
            sellingPrice: 50.00
          }
        }]
      }
    }).as('getCart');

    // Mock wallet
    cy.intercept('GET', `${apiUrl}/api/v1/wallet/balance`, {
      statusCode: 200,
      body: {
        balance: 1000.00
      }
    }).as('getWallet');

    // Mock order error
    cy.intercept('POST', `${apiUrl}/api/v1/orders/create`, {
      statusCode: 400,
      body: {
        message: 'No stock available for plan: Out of Stock Plan'
      }
    }).as('createOrderError');

    // Go to cart
    cy.visit('/dashboard/cart');
    cy.wait('@getCart');
    cy.wait('@getWallet');

    // Try to place order
    cy.contains('button', 'Place Order').should('be.visible').click();
    cy.wait('@createOrderError');

    // Verify error message
    cy.contains('No stock available').should('be.visible');
  });

  it('should handle multiple plans in cart', () => {
    const apiUrl = Cypress.env('apiUrl');
    cy.task('log', 'Starting multiple plans in cart test');

    // Mock cart with multiple plans
    cy.intercept('GET', `${apiUrl}/api/cart`, {
      statusCode: 200,
      body: {
        cartItems: [
          {
            id: 'cart1',
            quantity: 1,
            EsimPlan: {
              id: '12345',
              name: 'Plan 1',
              sellingPrice: 50.00
            }
          },
          {
            id: 'cart2',
            quantity: 1,
            EsimPlan: {
              id: '67890',
              name: 'Plan 2',
              sellingPrice: 75.00
            }
          }
        ]
      }
    }).as('getCart');

    // Mock wallet
    cy.intercept('GET', `${apiUrl}/api/v1/wallet/balance`, {
      statusCode: 200,
      body: {
        balance: 1000.00
      }
    }).as('getWallet');

    // Mock successful order creation
    cy.intercept('POST', `${apiUrl}/api/v1/orders/create`, {
      statusCode: 201,
      body: {
        message: 'Orders created successfully',
        orders: ['order123', 'order456'],
        totalAmount: 125.00
      }
    }).as('createOrder');

    // Go to cart
    cy.visit('/dashboard/cart');
    cy.wait('@getCart');
    cy.wait('@getWallet');

    // Verify multiple plans
    cy.contains('Plan 1').should('be.visible');
    cy.contains('$50.00').should('be.visible');
    cy.contains('Plan 2').should('be.visible');
    cy.contains('$75.00').should('be.visible');

    // Place order
    cy.contains('button', 'Place Order').should('be.visible').click();

    // Wait for order creation
    cy.wait('@createOrder').then(() => {
      cy.task('log', 'Order placed successfully');
    });

    // Verify success message
    cy.contains('Orders created successfully').should('be.visible');

    // Verify redirect to orders page
    cy.url().should('include', '/dashboard/orders');
  });
});