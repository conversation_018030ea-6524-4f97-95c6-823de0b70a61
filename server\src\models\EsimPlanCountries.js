const { Model, DataTypes } = require('sequelize');
const sequelize = require('../config/database');

class EsimPlanCountries extends Model {
    static associate(models) {
        // Junction table doesn't need any direct associations
    }
}

EsimPlanCountries.init({
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    esimPlanId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
            model: 'esimplans',
            key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
    },
    countryId: {
        type: DataTypes.STRING,
        allowNull: false,
        references: {
            model: 'countries',
            key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
    },
    isDefault: {
        type: DataTypes.BOOLEAN,
        defaultValue: false
    }
}, {
    sequelize,
    modelName: 'EsimPlanCountries',
    tableName: 'esimplancountries',
    timestamps: true
});

module.exports = EsimPlanCountries;
