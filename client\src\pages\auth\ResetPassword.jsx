import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';
import axios from '@/lib/axios';

export default function ResetPassword() {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const { toast } = useToast();
    const [isLoading, setIsLoading] = useState(false);
    const [isVerifying, setIsVerifying] = useState(true);
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');

    const token = searchParams.get('token');

    useEffect(() => {
        verifyToken();
    }, []);

    const verifyToken = async () => {
        if (!token) {
            navigate('/login');
            return;
        }

        try {
            await axios.post('/api/auth/verify-reset-token', { token });
            setIsVerifying(false);
        } catch (error) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'Invalid or expired reset token. Please request a new password reset.'
            });
            navigate('/login');
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (password !== confirmPassword) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'Passwords do not match'
            });
            return;
        }

        if (password.length < 8) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'Password must be at least 8 characters long'
            });
            return;
        }

        setIsLoading(true);

        try {
            await axios.post('/api/auth/reset-password', {
                token,
                password
            });

            toast({
                title: 'Success',
                description: 'Your password has been reset successfully'
            });

            navigate('/login');
        } catch (error) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: error.response?.data?.message || 'Failed to reset password'
            });
        } finally {
            setIsLoading(false);
        }
    };

    if (isVerifying) {
        return (
            <div className="fixed inset-0 min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
                <div className="flex items-center gap-2">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                    <span>Verifying reset token...</span>
                </div>
            </div>
        );
    }

    return (
        <div className="fixed inset-0 min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
            <div className="w-full max-w-[420px] mx-auto">
                <div className="bg-white p-8 rounded-2xl shadow-lg">
                    <div className="text-center mb-8">
                        <h1 className="text-2xl font-bold text-gray-900">Reset Your Password</h1>
                        <p className="text-gray-600 mt-2">Enter your new password below</p>
                    </div>

                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="space-y-4">
                            <div>
                                <Input
                                    type="password"
                                    placeholder="New Password"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    required
                                    className="w-full py-2.5"
                                />
                            </div>
                            <div>
                                <Input
                                    type="password"
                                    placeholder="Confirm New Password"
                                    value={confirmPassword}
                                    onChange={(e) => setConfirmPassword(e.target.value)}
                                    required
                                    className="w-full py-2.5"
                                />
                            </div>
                        </div>

                        <Button
                            type="submit"
                            className="w-full py-2.5 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl transition-all duration-200 transform hover:scale-[1.02]"
                            disabled={isLoading}
                        >
                            {isLoading ? (
                                <div className="flex items-center justify-center gap-2">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span>Resetting Password...</span>
                                </div>
                            ) : (
                                'Reset Password'
                            )}
                        </Button>
                    </form>
                </div>
            </div>
        </div>
    );
}
