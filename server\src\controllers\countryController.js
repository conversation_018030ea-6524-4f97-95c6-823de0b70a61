const Country = require('../models/Country');
const { Sequelize } = require('sequelize');

// Get all countries
exports.getAllCountries = async (req, res) => {
    try {
        const countries = await Country.findAll({
            attributes: ['id', 'iso3', 'name', 'flagEmoji', 'flagUrl', 'phoneCode', 'currencyCode', 'currencySymbol'],
            order: [['name', 'ASC']]
        });

        res.json(countries);
    } catch (error) {
        console.error('Error fetching countries:', error);
        res.status(500).json({ message: 'Failed to fetch countries', error: error.message });
    }
};

// Get unique regions
exports.getRegions = async (req, res) => {
    try {
        const regions = await Country.findAll({
            attributes: [
                [Sequelize.fn('DISTINCT', Sequelize.col('region')), 'region']
            ],
            where: {
                region: {
                    [Sequelize.Op.not]: null
                }
            },
            order: [['region', 'ASC']]
        });

        res.json(regions.map(r => r.region));
    } catch (error) {
        console.error('Error fetching regions:', error);
        res.status(500).json({ message: 'Error fetching regions' });
    }
};

// Get country by id
exports.getCountryById = async (req, res) => {
    try {
        const country = await Country.findByPk(req.params.id, {
            attributes: ['id', 'iso3', 'name', 'flagEmoji', 'flagUrl', 'phoneCode', 'currencyCode', 'currencySymbol']
        });
        if (!country) {
            return res.status(404).json({ message: 'Country not found' });
        }
        res.json(country);
    } catch (error) {
        console.error('Error fetching country:', error);
        res.status(500).json({ message: 'Failed to fetch country', error: error.message });
    }
};
