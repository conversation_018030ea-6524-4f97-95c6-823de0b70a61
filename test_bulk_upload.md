# Bulk Upload Order Test

## Issue Description
When uploading bulk plans from Excel, the plans are not displaying in the correct order in the eSIM plans list. The plans should appear in the same order as they appear in the Excel file.

## Root Cause
1. **Frontend Sorting Mismatch**: The frontend was using `'newest'` as the default sort value, but the backend only handled `'oldest'` and defaulted to `DESC` order for everything else.

2. **Backend Sorting Logic**: The backend wasn't properly handling the `'newest'` sort parameter.

3. **Bulk Upload Timestamps**: When plans were created in a loop, they got sequential timestamps, causing the last plan in the Excel file to appear first (newest timestamp).

## Solution Applied

### Backend Changes (`server/src/controllers/esimPlanController.js`)

1. **Fixed sorting logic** (lines 299-305):
   ```javascript
   // Handle sorting
   let order = [['createdAt', 'DESC']]; // Default to newest first
   if (sortBy === 'oldest') {
       order = [['createdAt', 'ASC']];
   } else if (sortBy === 'newest' || sortBy === 'createdAt') {
       order = [['createdAt', 'DESC']];
   }
   ```

2. **Fixed bulk upload timestamps** (lines 602-603, 748-781):
   - Added base timestamp for maintaining order
   - Each plan gets an incremented timestamp (base + i * 1000ms)
   - This ensures plans maintain their Excel file order when sorted by createdAt DESC

3. **Fixed combined plans sorting** (lines 432-442):
   - Replaced undefined `sortOrder` with proper `sortBy` logic
   - Consistent sorting behavior across all plan sources

### Frontend Changes (`client/src/pages/admin/EsimPlans.jsx`)

1. **Fixed default sort value** (line 66):
   ```javascript
   return sessionStorage.getItem('adminEsimPlansSort') || 'createdAt';
   ```

2. **Fixed tab change handler** (line 998):
   ```javascript
   setSortBy("createdAt");
   ```

## Expected Behavior After Fix

1. **Default View**: Plans display with newest first (Latest First)
2. **Bulk Upload**: Plans appear in the same order as in the Excel file
3. **Sort Options**: 
   - "Latest First" (createdAt/newest) - newest plans at top
   - "Oldest First" (oldest) - oldest plans at top
4. **Consistent Sorting**: Same behavior across all plan sources (local + external)

## Test Steps

1. Create an Excel file with multiple plans in a specific order
2. Upload the Excel file using the bulk upload feature
3. Verify that plans appear in the same order as in the Excel file
4. Test both sort options to ensure they work correctly
5. Verify that the order is maintained across page refreshes and navigation

## Files Modified

- `server/src/controllers/esimPlanController.js`
- `client/src/pages/admin/EsimPlans.jsx`
