const express = require('express');
const router = express.Router();
const partnerController = require('../controllers/partnerController');
const { isAuthenticated, isAdmin } = require('../middleware/auth');

// Get partner by ID (accessible by the partner themselves and admins)
router.get('/:id', isAuthenticated, partnerController.getPartnerById);

// Admin-only routes
router.use(isAuthenticated, isAdmin);

// Get all partners
router.get('/', partnerController.getAllPartners);

// Create new partner
router.post('/', partnerController.createPartner);

// Update partner
router.put('/:id', partnerController.updatePartner);

// Delete partner
router.delete('/:id', partnerController.deletePartner);

// Toggle partner status
router.patch('/:id/toggle-status', partnerController.togglePartnerStatus);

module.exports = router;
