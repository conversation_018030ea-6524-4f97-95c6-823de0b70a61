const sequelize = require('../config/database');

/**
 * Generates a sequential order ID in the format VLZ2000, VLZ2001, etc.
 * @returns {Promise<string>} The next order ID in sequence
 */
async function generateOrderId() {
    try {
        // Get the latest order using raw query with error handling
        const [latestOrder] = await sequelize.query(
            'SELECT id FROM orders ORDER BY CAST(SUBSTRING(id, 4) AS UNSIGNED) DESC LIMIT 1',
            { 
                type: sequelize.QueryTypes.SELECT,
                raw: true
            }
        );

        // Start from 2000 if no orders exist
        const baseNumber = 2000;
        const prefix = 'VLZ';
        
        if (!latestOrder) {
            return `${prefix}${baseNumber}`;
        }

        // Extract the numeric part from the latest order ID with validation
        const match = latestOrder.id.match(/VLZ(\d+)/);
        if (!match) {
            console.warn('Invalid order ID format found:', latestOrder.id);
            return `${prefix}${baseNumber}`;
        }

        const currentNumber = parseInt(match[1], 10);
        if (isNaN(currentNumber)) {
            console.warn('Failed to parse order number:', match[1]);
            return `${prefix}${baseNumber}`;
        }
        
        // Generate the next number in sequence
        const nextNumber = currentNumber + 1;
        
        return `${prefix}${nextNumber}`;
    } catch (error) {
        console.error('Error generating order ID:', error);
        throw new Error('Failed to generate order ID');
    }
}

module.exports = generateOrderId; 