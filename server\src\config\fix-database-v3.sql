USE esim_platform;

-- Create temporary table
CREATE TABLE IF NOT EXISTS Countries_new (
    id CHAR(2) PRIMARY KEY,
    iso3 CHAR(3) NOT NULL,
    name VARCHAR(100) NOT NULL,
    phoneCode VARCHAR(10),
    currencyCode CHAR(3),
    currencySymbol VARCHAR(5),
    region VARCHAR(50),
    isActive BOOLEAN DEFAULT true,
    createdAt DATETIME NOT NULL,
    updatedAt DATETIME NOT NULL,
    UNIQUE KEY idx_country_iso3 (iso3),
    INDEX idx_country_search (name, region, isActive)
) ENGINE=InnoDB;

-- Insert Spain
INSERT INTO Countries_new (id, iso3, name, phoneCode, currencyCode, currencySymbol, region, isActive, createdAt, updatedAt)
VALUES ('ES', 'ESP', 'Spain', '+34', 'EUR', '€', 'Europe', true, NOW(), NOW())
ON DUPLICATE KEY UPDATE updatedAt = NOW();

-- Drop old table if it exists (this will automatically drop foreign keys)
DROP TABLE IF EXISTS country;

-- Rename new table to final name
ALTER TABLE Countries_new RENAME TO Countries;

-- Add foreign key constraints
ALTER TABLE Users
ADD CONSTRAINT fk_users_country
FOREIGN KEY (countryId) REFERENCES Countries(id)
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE Users
ADD CONSTRAINT fk_users_billing_country
FOREIGN KEY (billingCountryId) REFERENCES Countries(id)
ON DELETE SET NULL ON UPDATE CASCADE;
