// Database optimization configuration for eSIM platform
// This file contains optimizations for MySQL/MariaDB performance

const databaseOptimizations = {
    // Connection pool optimization
    pool: {
        max: 20,          // Maximum number of connections
        min: 5,           // Minimum number of connections
        acquire: 60000,   // Maximum time to get connection (60s)
        idle: 10000,      // Maximum time connection can be idle (10s)
        evict: 1000,      // Check for idle connections every 1s
        handleDisconnects: true
    },

    // Query optimization settings
    dialectOptions: {
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
        // Enable multiple statements for better performance
        multipleStatements: true,
        // Connection timeout
        connectTimeout: 60000,
        // Socket timeout
        acquireTimeout: 60000,
        timeout: 60000,
        // Enable compression
        compress: true,
        // SSL configuration (if needed)
        ssl: false
    },

    // Logging optimization
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    
    // Benchmark queries in development
    benchmark: process.env.NODE_ENV === 'development',

    // Query optimization
    define: {
        // Don't add the timestamp attributes (updatedAt, createdAt)
        timestamps: true,
        // Don't delete database entries but set the newly added attribute deletedAt
        paranoid: false,
        // Don't use camelcase for automatically added attributes
        underscored: true,
        // Disable the modification of table names
        freezeTableName: true
    }
};

// MySQL/MariaDB specific optimizations
const mysqlOptimizations = `
-- MySQL Configuration Optimizations for eSIM Platform
-- Add these to your MySQL configuration file (my.cnf or my.ini)

[mysqld]
# InnoDB optimizations
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = 1
innodb_open_files = 400

# Query cache (if using MySQL < 8.0)
query_cache_type = 1
query_cache_size = 256M
query_cache_limit = 2M

# Connection optimizations
max_connections = 200
max_connect_errors = 1000000
connect_timeout = 60
wait_timeout = 28800
interactive_timeout = 28800

# Buffer optimizations
key_buffer_size = 256M
sort_buffer_size = 4M
read_buffer_size = 2M
read_rnd_buffer_size = 8M
myisam_sort_buffer_size = 128M

# Table cache
table_open_cache = 4000
table_definition_cache = 2000

# Temporary tables
tmp_table_size = 256M
max_heap_table_size = 256M

# Binary logging (if using replication)
log_bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7

# Slow query log for optimization
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1
`;

// Performance monitoring queries
const performanceQueries = {
    // Check slow queries
    slowQueries: `
        SELECT 
            query_time,
            lock_time,
            rows_sent,
            rows_examined,
            sql_text
        FROM mysql.slow_log 
        ORDER BY query_time DESC 
        LIMIT 10;
    `,

    // Check index usage
    indexUsage: `
        SELECT 
            OBJECT_SCHEMA,
            OBJECT_NAME,
            INDEX_NAME,
            COUNT_FETCH,
            COUNT_INSERT,
            COUNT_UPDATE,
            COUNT_DELETE
        FROM performance_schema.table_io_waits_summary_by_index_usage
        WHERE OBJECT_SCHEMA = 'esim_demo'
        ORDER BY COUNT_FETCH DESC;
    `,

    // Check table sizes
    tableSizes: `
        SELECT 
            table_name AS 'Table',
            ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)',
            table_rows AS 'Rows'
        FROM information_schema.TABLES 
        WHERE table_schema = 'esim_demo'
        ORDER BY (data_length + index_length) DESC;
    `,

    // Check connection usage
    connections: `
        SHOW STATUS LIKE 'Threads_connected';
        SHOW STATUS LIKE 'Max_used_connections';
        SHOW VARIABLES LIKE 'max_connections';
    `
};

// Cache optimization strategies
const cacheStrategies = {
    // Default cache durations (in seconds)
    durations: {
        countries: 7200,        // 2 hours - rarely change
        regions: 7200,          // 2 hours - rarely change
        partners: 1800,         // 30 minutes - moderate change
        defaultPlans: 3600,     // 1 hour - default view
        filteredPlans: 300,     // 5 minutes - filtered results
        searchResults: 900,     // 15 minutes - search results
        planDetails: 1800       // 30 minutes - individual plans
    },

    // Cache keys patterns
    keyPatterns: {
        countries: 'all_countries',
        regions: 'all_regions',
        partner: 'partner_{partnerId}',
        plans: 'partner_{partnerId}_plans_{hash}',
        planDetail: 'plan_{planId}_{partnerId}'
    }
};

// Query optimization helpers
const queryOptimizations = {
    // Use LIMIT for large result sets
    usePagination: true,
    
    // Use indexes for WHERE clauses
    useIndexes: true,
    
    // Avoid SELECT * queries
    selectSpecificFields: true,
    
    // Use JOINs instead of subqueries when possible
    preferJoins: true,
    
    // Use EXPLAIN to analyze queries
    analyzeQueries: process.env.NODE_ENV === 'development'
};

module.exports = {
    databaseOptimizations,
    mysqlOptimizations,
    performanceQueries,
    cacheStrategies,
    queryOptimizations
};
