<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Mocha Tests" time="123.504" tests="4" failures="4">
  <testsuite name="Root Suite" timestamp="2025-03-24T08:05:43" tests="0" file="cypress\e2e\partner\orders.cy.js" time="0.000" failures="0">
  </testsuite>
  <testsuite name="Order Placement Flow" timestamp="2025-03-24T08:05:43" tests="4" time="123.454" failures="4">
    <testcase name="Order Placement Flow should add plan to cart and place order successfully" time="0.000" classname="should add plan to cart and place order successfully">
      <failure message="Timed out retrying after 10000ms: `cy.wait()` timed out waiting `10000ms` for the 1st request to the route: `getPlans`. No request ever occurred.

https://on.cypress.io/wait" type="CypressError"><![CDATA[CypressError: Timed out retrying after 10000ms: `cy.wait()` timed out waiting `10000ms` for the 1st request to the route: `getPlans`. No request ever occurred.

https://on.cypress.io/wait
    at cypressErr (http://localhost:5174/__cypress/runner/cypress_runner.js:75187:18)
    at Object.errByPath (http://localhost:5174/__cypress/runner/cypress_runner.js:75242:10)
    at checkForXhr (http://localhost:5174/__cypress/runner/cypress_runner.js:134821:84)
    at <unknown> (http://localhost:5174/__cypress/runner/cypress_runner.js:134844:28)
    at tryCatcher (http://localhost:5174/__cypress/runner/cypress_runner.js:1807:23)
    at Promise.attempt.Promise.try (http://localhost:5174/__cypress/runner/cypress_runner.js:4315:29)
    at whenStable (http://localhost:5174/__cypress/runner/cypress_runner.js:143192:68)
    at <unknown> (http://localhost:5174/__cypress/runner/cypress_runner.js:143133:14)
    at tryCatcher (http://localhost:5174/__cypress/runner/cypress_runner.js:1807:23)
    at Promise._settlePromiseFromHandler (http://localhost:5174/__cypress/runner/cypress_runner.js:1519:31)
    at Promise._settlePromise (http://localhost:5174/__cypress/runner/cypress_runner.js:1576:18)
    at Promise._settlePromise0 (http://localhost:5174/__cypress/runner/cypress_runner.js:1621:10)
    at Promise._settlePromises (http://localhost:5174/__cypress/runner/cypress_runner.js:1701:18)
    at Promise._fulfill (http://localhost:5174/__cypress/runner/cypress_runner.js:1645:18)
    at <unknown> (http://localhost:5174/__cypress/runner/cypress_runner.js:5450:46)
From Your Spec Code:
    at Context.eval (webpack://client/./cypress/e2e/partner/orders.cy.js:156:7)]]></failure>
    </testcase>
    <testcase name="Order Placement Flow should show error when wallet balance is insufficient" time="0.000" classname="should show error when wallet balance is insufficient">
      <failure message="Timed out retrying after 10000ms: `cy.wait()` timed out waiting `10000ms` for the 1st request to the route: `getCart`. No request ever occurred.

https://on.cypress.io/wait" type="CypressError"><![CDATA[CypressError: Timed out retrying after 10000ms: `cy.wait()` timed out waiting `10000ms` for the 1st request to the route: `getCart`. No request ever occurred.

https://on.cypress.io/wait
    at cypressErr (http://localhost:5174/__cypress/runner/cypress_runner.js:75187:18)
    at Object.errByPath (http://localhost:5174/__cypress/runner/cypress_runner.js:75242:10)
    at checkForXhr (http://localhost:5174/__cypress/runner/cypress_runner.js:134821:84)
    at <unknown> (http://localhost:5174/__cypress/runner/cypress_runner.js:134844:28)
    at tryCatcher (http://localhost:5174/__cypress/runner/cypress_runner.js:1807:23)
    at Promise.attempt.Promise.try (http://localhost:5174/__cypress/runner/cypress_runner.js:4315:29)
    at whenStable (http://localhost:5174/__cypress/runner/cypress_runner.js:143192:68)
    at <unknown> (http://localhost:5174/__cypress/runner/cypress_runner.js:143133:14)
    at tryCatcher (http://localhost:5174/__cypress/runner/cypress_runner.js:1807:23)
    at Promise._settlePromiseFromHandler (http://localhost:5174/__cypress/runner/cypress_runner.js:1519:31)
    at Promise._settlePromise (http://localhost:5174/__cypress/runner/cypress_runner.js:1576:18)
    at Promise._settlePromise0 (http://localhost:5174/__cypress/runner/cypress_runner.js:1621:10)
    at Promise._settlePromises (http://localhost:5174/__cypress/runner/cypress_runner.js:1701:18)
    at Promise._fulfill (http://localhost:5174/__cypress/runner/cypress_runner.js:1645:18)
    at <unknown> (http://localhost:5174/__cypress/runner/cypress_runner.js:5450:46)
From Your Spec Code:
    at Context.eval (webpack://client/./cypress/e2e/partner/orders.cy.js:232:7)]]></failure>
    </testcase>
    <testcase name="Order Placement Flow should show error when stock is not available" time="0.000" classname="should show error when stock is not available">
      <failure message="Timed out retrying after 10000ms: `cy.wait()` timed out waiting `10000ms` for the 1st request to the route: `getCart`. No request ever occurred.

https://on.cypress.io/wait" type="CypressError"><![CDATA[CypressError: Timed out retrying after 10000ms: `cy.wait()` timed out waiting `10000ms` for the 1st request to the route: `getCart`. No request ever occurred.

https://on.cypress.io/wait
    at cypressErr (http://localhost:5174/__cypress/runner/cypress_runner.js:75187:18)
    at Object.errByPath (http://localhost:5174/__cypress/runner/cypress_runner.js:75242:10)
    at checkForXhr (http://localhost:5174/__cypress/runner/cypress_runner.js:134821:84)
    at <unknown> (http://localhost:5174/__cypress/runner/cypress_runner.js:134844:28)
    at tryCatcher (http://localhost:5174/__cypress/runner/cypress_runner.js:1807:23)
    at Promise.attempt.Promise.try (http://localhost:5174/__cypress/runner/cypress_runner.js:4315:29)
    at whenStable (http://localhost:5174/__cypress/runner/cypress_runner.js:143192:68)
    at <unknown> (http://localhost:5174/__cypress/runner/cypress_runner.js:143133:14)
    at tryCatcher (http://localhost:5174/__cypress/runner/cypress_runner.js:1807:23)
    at Promise._settlePromiseFromHandler (http://localhost:5174/__cypress/runner/cypress_runner.js:1519:31)
    at Promise._settlePromise (http://localhost:5174/__cypress/runner/cypress_runner.js:1576:18)
    at Promise._settlePromise0 (http://localhost:5174/__cypress/runner/cypress_runner.js:1621:10)
    at Promise._settlePromises (http://localhost:5174/__cypress/runner/cypress_runner.js:1701:18)
    at Promise._fulfill (http://localhost:5174/__cypress/runner/cypress_runner.js:1645:18)
    at <unknown> (http://localhost:5174/__cypress/runner/cypress_runner.js:5450:46)
From Your Spec Code:
    at Context.eval (webpack://client/./cypress/e2e/partner/orders.cy.js:287:7)]]></failure>
    </testcase>
    <testcase name="Order Placement Flow should handle multiple plans in cart" time="0.000" classname="should handle multiple plans in cart">
      <failure message="Timed out retrying after 10000ms: `cy.wait()` timed out waiting `10000ms` for the 1st request to the route: `getCart`. No request ever occurred.

https://on.cypress.io/wait" type="CypressError"><![CDATA[CypressError: Timed out retrying after 10000ms: `cy.wait()` timed out waiting `10000ms` for the 1st request to the route: `getCart`. No request ever occurred.

https://on.cypress.io/wait
    at cypressErr (http://localhost:5174/__cypress/runner/cypress_runner.js:75187:18)
    at Object.errByPath (http://localhost:5174/__cypress/runner/cypress_runner.js:75242:10)
    at checkForXhr (http://localhost:5174/__cypress/runner/cypress_runner.js:134821:84)
    at <unknown> (http://localhost:5174/__cypress/runner/cypress_runner.js:134844:28)
    at tryCatcher (http://localhost:5174/__cypress/runner/cypress_runner.js:1807:23)
    at Promise.attempt.Promise.try (http://localhost:5174/__cypress/runner/cypress_runner.js:4315:29)
    at whenStable (http://localhost:5174/__cypress/runner/cypress_runner.js:143192:68)
    at <unknown> (http://localhost:5174/__cypress/runner/cypress_runner.js:143133:14)
    at tryCatcher (http://localhost:5174/__cypress/runner/cypress_runner.js:1807:23)
    at Promise._settlePromiseFromHandler (http://localhost:5174/__cypress/runner/cypress_runner.js:1519:31)
    at Promise._settlePromise (http://localhost:5174/__cypress/runner/cypress_runner.js:1576:18)
    at Promise._settlePromise0 (http://localhost:5174/__cypress/runner/cypress_runner.js:1621:10)
    at Promise._settlePromises (http://localhost:5174/__cypress/runner/cypress_runner.js:1701:18)
    at Promise._fulfill (http://localhost:5174/__cypress/runner/cypress_runner.js:1645:18)
    at <unknown> (http://localhost:5174/__cypress/runner/cypress_runner.js:5450:46)
From Your Spec Code:
    at Context.eval (webpack://client/./cypress/e2e/partner/orders.cy.js:355:7)]]></failure>
    </testcase>
  </testsuite>
</testsuites>