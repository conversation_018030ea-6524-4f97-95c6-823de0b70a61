# Deployment Guide for eSIM Platform Backend

This guide provides step-by-step instructions for deploying the eSIM Platform backend to AWS EC2 using GitHub Actions, Docker, Nginx, and SSL with auto-renewal, with AWS RDS for the database.

## Prerequisites

1. An AWS account with EC2 and RDS access
2. A domain name pointing to your EC2 instance
3. A GitHub repository with your code
4. Basic knowledge of AWS, Docker, and GitHub Actions
5. AWS RDS MySQL instance (see aws-rds-setup.md for setup instructions)

## Step 1: Set Up AWS EC2 Instance

1. Launch an EC2 instance (recommended: t2.medium or higher with at least 2GB RAM)
2. Choose Ubuntu Server 22.04 LTS as the operating system
3. Configure security groups to allow the following inbound traffic:
   - SSH (port 22)
   - HTTP (port 80)
   - HTTPS (port 443)
   - MySQL (port 3306) - optional, only if you need external DB access
4. Create or use an existing key pair for SSH access
5. Launch the instance and note its public IP address

## Step 2: Configure DNS

1. In your domain registrar's DNS settings, create an A record pointing your domain (e.g., api.yourdomain.com) to your EC2 instance's public IP address
2. Wait for DNS propagation (can take up to 24-48 hours, but often much faster)

## Step 3: Set Up the EC2 Instance

1. SSH into your EC2 instance:
   ```
   ssh -i your-key.pem ubuntu@your-ec2-public-ip
   ```

2. Copy the `ec2-setup.sh` script to your EC2 instance:
   ```
   scp -i your-key.pem ec2-setup.sh ubuntu@your-ec2-public-ip:~/
   ```

3. Make the script executable and run it:
   ```
   chmod +x ec2-setup.sh
   ./ec2-setup.sh
   ```

## Step 4: Configure GitHub Secrets

Add the following secrets to your GitHub repository:

1. `AWS_ACCESS_KEY_ID`: Your AWS access key
2. `AWS_SECRET_ACCESS_KEY`: Your AWS secret key
3. `AWS_REGION`: Your AWS region (e.g., us-east-1)
4. `EC2_HOST`: Your EC2 instance's public IP address
5. `EC2_USERNAME`: The username for SSH access (usually 'ubuntu')
6. `EC2_SSH_KEY`: The private SSH key for accessing your EC2 instance
7. `DOMAIN_NAME`: Your domain name (e.g., api.yourdomain.com)
8. `ADMIN_EMAIL`: Your email address for SSL certificate notifications

Add all environment variables from your `.env` file as GitHub secrets:

9. `PORT`: The port your app runs on (usually 3000)
10. `NODE_ENV`: Set to 'production'
11. `CLIENT_URL`: Your frontend URL
12. `DB_HOST`: Your RDS endpoint (e.g., 'your-database.abcdefghijkl.us-east-1.rds.amazonaws.com')
13. `DB_USER`: Your RDS master username
14. `DB_PASSWORD`: Your RDS master password
15. `DB_NAME`: Your RDS database name
16. `JWT_SECRET`: Secret for JWT tokens
17. `JWT_REFRESH_SECRET`: Secret for JWT refresh tokens
18. `JWT_EXPIRES_IN`: JWT expiration time
19. `JWT_REFRESH_EXPIRES_IN`: JWT refresh token expiration time
20. `ZEPTO_API_URL`: ZeptoMail API URL
21. `ZEPTO_API_KEY`: ZeptoMail API key
22. `ZEPTO_FROM_EMAIL`: ZeptoMail sender email
23. `MAX_LOGIN_ATTEMPTS`: Maximum login attempts
24. `LOGIN_TIMEOUT`: Login timeout duration
25. `MOBIMATTER_API_URL`: Mobimatter API URL
26. `MOBIMATTER_API_KEY`: Mobimatter API key
27. `MOBIMATTER_MERCHANT_ID`: Mobimatter merchant ID
28. `API_BASE_URL`: Base URL for your API
29. `DEV_API_URL`: Development API URL
30. `PROD_API_URL`: Production API URL

## Step 5: Deploy Your Application

1. Push your code to the main branch of your GitHub repository
2. GitHub Actions will automatically deploy your application to the EC2 instance
3. Monitor the GitHub Actions workflow to ensure successful deployment

## Step 6: Verify Deployment

1. Visit your domain in a web browser (https://api.yourdomain.com)
2. You should see the eSIM Management API running message
3. Check the logs on your EC2 instance:
   ```
   cd ~/esim-project
   docker-compose logs -f app
   ```

## Troubleshooting

### SSL Certificate Issues

If you encounter SSL certificate issues:

1. SSH into your EC2 instance
2. Navigate to the project directory:
   ```
   cd ~/esim-project
   ```
3. Run the certbot command manually:
   ```
   docker-compose run --rm certbot certonly --webroot --webroot-path=/var/www/certbot --email <EMAIL> --agree-tos --no-eff-email -d your-domain.com -d www.your-domain.com
   ```
4. Reload Nginx:
   ```
   docker exec nginx nginx -s reload
   ```

### Database Connection Issues

If the application cannot connect to the RDS database:

1. Check the application logs:
   ```
   docker-compose logs app
   ```
2. Verify the database environment variables in GitHub Secrets
3. Check RDS security group settings to ensure your EC2 instance has access
4. Try to connect to the database manually from your EC2 instance:
   ```
   mysql -h your-rds-endpoint -u your-username -p
   ```

### Application Crashes

If the application crashes:

1. Check the application logs:
   ```
   docker-compose logs app
   ```
2. Verify all environment variables are correctly set in GitHub Secrets
3. Restart the containers:
   ```
   docker-compose down
   docker-compose up -d
   ```

## Maintenance

### Updating the Application

To update your application:

1. Push changes to your GitHub repository
2. GitHub Actions will automatically deploy the changes

### Manual Deployment

If you need to deploy manually:

1. SSH into your EC2 instance
2. Navigate to the project directory:
   ```
   cd ~/esim-project
   ```
3. Pull the latest changes:
   ```
   git pull
   ```
4. Rebuild and restart the containers:
   ```
   docker-compose down
   docker-compose up -d --build
   ```

### Backing Up the RDS Database

To back up the RDS database:

1. **Automated Snapshots**: AWS RDS automatically creates daily snapshots based on your backup settings

2. **Manual Snapshots**:
   - Go to the RDS console
   - Select your database
   - Click "Actions" → "Take snapshot"
   - Enter a snapshot name and click "Take snapshot"

3. **Manual Export** (if you need a SQL dump):
   - SSH into your EC2 instance
   - Run the backup command:
     ```
     mysqldump -h your-rds-endpoint -u your-username -p --all-databases > backup.sql
     ```
   - Copy the backup file to your local machine:
     ```
     scp -i your-key.pem ubuntu@your-ec2-public-ip:~/backup.sql ./
     ```
