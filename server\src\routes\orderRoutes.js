const express = require('express');
const router = express.Router();
const orderController = require('../controllers/orderController');
const { isAuthenticated, isAdmin } = require('../middleware/auth');

// All routes require authentication
router.use(isAuthenticated);

// Admin routes
router.get('/admin/all', isAdmin, orderController.getAllOrders);
router.get('/admin/export', isAdmin, orderController.exportOrders);
router.get('/admin/user/:userId', isAdmin, orderController.getOrdersByUserId);
router.get('/admin/:id', isAdmin, orderController.getAllOrderById);

// Create a new order from cart items
router.post('/', orderController.createOrder);

// Get all orders for the current user
router.get('/', orderController.getUserOrders);

// Get a specific order by ID
router.get('/:id', orderController.getOrderById);

// Get usage data for an order
router.get('/:id/usage', orderController.getOrderUsage);

module.exports = router;
