const sequelize = require('../config/database');

async function updateMarkupColumn() {
    try {
        // First update NULL values to 0
        await sequelize.query(`
            UPDATE Users 
            SET markupPercentage = 0 
            WHERE markupPercentage IS NULL;
        `);
        console.log('Updated NULL values to 0');

        // Then modify the column to be NOT NULL with default
        await sequelize.query(`
            ALTER TABLE Users 
            MODIFY COLUMN markupPercentage DECIMAL(5,2) NOT NULL DEFAULT 0;
        `);
        console.log('Modified column to be NOT NULL with default value');

        process.exit(0);
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}

updateMarkupColumn();
