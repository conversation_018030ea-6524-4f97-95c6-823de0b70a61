Project Title: B2B eSIM Service Platform
Project Overview:
This project is a B2B eSIM service platform that enables brands to leverage eSIM technology by offering digital-first services. By eliminating the need for physical SIM cards, the platform helps reduce operational costs, enhance customer acquisition, and improve user experiences.

What is an eSIM?
An eSIM (Embedded SIM) is a programmable chip integrated into mobile devices, allowing service providers to remotely provision subscriber profiles without the need for a physical SIM card.

Key Benefits of eSIM:
✔ Cost Reduction: Eliminates logistics and distribution costs associated with physical SIM cards.
✔ Higher Conversions: Streamlined onboarding improves user adoption and retention.
✔ Enhanced Customer Experience: Digital activation reduces friction in the user journey.etc

Project Requirements
User Panel:
Authentication:

- Login page (Email, Password, "Remember Me," "Forgot Password")
- OTP verification (sent to registered email/SMS)
- Session management with JWT tokens

Dashboard:

Sticky Navbar & Sidebar (persistent across pages)
Widgets: Display key metrics
Graph Analysis: Visual representation of order trends
Pie Chart: Available eSIM plans overview
Recent Orders: List of latest transactions
eSIM Plans Page:

Search bar
API-fetched eSIM plan cards
Pagination
eSIM Orders Page:

Table format listing all eSIM orders
Search bar
Download button (Export orders in Excel format)
"View Order" option (Navigates to detailed order page)
Top-Up Orders Page:

Displays all top-up orders in table format
Search functionality
Export orders as Excel
Wallet Management:

Displays wallet balance in table format
Search functionality
Export wallet transactions as Excel
User Account Page:

Displays user details: First Name, Last Name, Username, Email, Phone, Billing Address, Wallet Threshold
Change password functionality
Shopping Cart & Purchase Flow:

Cart displays added products
"Purchase Now" button
Upon purchase, amount is deducted from Wallet
Redirects to Order Details Page
Order Details Page:

Order Summary:
Order ID, Plan Name, Total Cost, Start Date
Rechargeability Status, ICC ID, Activation Code
SMDP Address, Access Point Name
Wallet Transaction ID

Technical Architecture:
- **Cloud Infrastructure**: AWS EC2 (compute), RDS (MySQL), S3 (storage)
- **CI/CD**: GitHub Actions pipeline with automated testing
- **Security**: VPC isolation, KMS encryption, IAM role-based access
- **Monitoring**: CloudWatch metrics + Prometheus/Grafana dashboard

Admin Panel:
Authentication:

Shared login page (Role-Based Access Control - RBAC)
Redirects to Admin/User Dashboard based on role
Admin Dashboard:

Overview of platform analytics
Partner Management:

Displays all partners
CRUD operations
eSIM Plans Management:

Displays all eSIM plans
CRUD operations
eSIM Orders Management:

View and manage all orders
Knowledge Base Management:

CRUD operations for Knowledge Base articles
Technology Stack:
Frontend: React.js (Vite) jsx, Tailwind CSS, Shadcn
Backend: Node.js, Express.js
Database: MySQL with sequelize
Authentication: JWT (JSON Web Tokens)
Password Hashing: Bcrypt


This eSIM service platform is designed to offer a scalable, cost-efficient, and seamless digital onboarding experience, enabling brands to maximize the potential of eSIM technology.