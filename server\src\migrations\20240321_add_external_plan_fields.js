module.exports = {
    up: async (queryInterface, Sequelize) => {
        await queryInterface.addColumn('EsimPlans', 'externalProductId', {
            type: Sequelize.STRING(255),
            allowNull: true
        }).catch(e => console.log('Column already exists: externalProductId'));

        await queryInterface.addColumn('EsimPlans', 'externalSkuId', {
            type: Sequelize.STRING(255),
            allowNull: true
        }).catch(e => console.log('Column already exists: externalSkuId'));

        await queryInterface.addColumn('EsimPlans', 'supportedRegions', {
            type: Sequelize.JSON,
            allowNull: true
        }).catch(e => console.log('Column already exists: supportedRegions'));

        await queryInterface.addColumn('EsimPlans', 'features', {
            type: Sequelize.JSON,
            allowNull: true
        }).catch(e => console.log('Column already exists: features'));

        await queryInterface.addColumn('EsimPlans', 'providerMetadata', {
            type: Sequelize.JSON,
            allowNull: true
        }).catch(e => console.log('Column already exists: providerMetadata'));

        // Add indexes for better performance
        await queryInterface.addIndex('EsimPlans', ['externalProductId'], {
            name: 'idx_esimplans_external_product'
        }).catch(e => console.log('Index already exists: idx_esimplans_external_product'));

        await queryInterface.addIndex('EsimPlans', ['externalSkuId'], {
            name: 'idx_esimplans_external_sku'
        }).catch(e => console.log('Index already exists: idx_esimplans_external_sku'));
    },

    down: async (queryInterface, Sequelize) => {
        await queryInterface.removeColumn('EsimPlans', 'externalProductId')
            .catch(e => console.log('Column does not exist: externalProductId'));
        await queryInterface.removeColumn('EsimPlans', 'externalSkuId')
            .catch(e => console.log('Column does not exist: externalSkuId'));
        await queryInterface.removeColumn('EsimPlans', 'supportedRegions')
            .catch(e => console.log('Column does not exist: supportedRegions'));
        await queryInterface.removeColumn('EsimPlans', 'features')
            .catch(e => console.log('Column does not exist: features'));
        await queryInterface.removeColumn('EsimPlans', 'providerMetadata')
            .catch(e => console.log('Column does not exist: providerMetadata'));

        await queryInterface.removeIndex('EsimPlans', 'idx_esimplans_external_product')
            .catch(e => console.log('Index does not exist: idx_esimplans_external_product'));
        await queryInterface.removeIndex('EsimPlans', 'idx_esimplans_external_sku')
            .catch(e => console.log('Index does not exist: idx_esimplans_external_sku'));
    }
}; 