const jwt = require('jsonwebtoken');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');
const { User, Wallet } = require('../models');
const OTP = require('../models/OTP');
const { sendOTPEmail, sendPasswordResetEmail } = require('../utils/emailService');
const bcrypt = require('bcrypt');
const MAX_LOGIN_ATTEMPTS = 3;
const uuidv4 = require('uuid').v4;

const generateOTP = () => {
    return Math.floor(100000 + Math.random() * 900000).toString();
};

const generateTokens = (user) => {
    const token = jwt.sign(
        { id: user.id, email: user.email, role: user.role },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    const refreshToken = jwt.sign(
        { id: user.id },
        process.env.JWT_REFRESH_SECRET,
        { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN }
    );

    return { token, refreshToken };
};

const login = async (req, res) => {
    try {
        console.log('Login attempt:', {
            body: req.body,
            headers: req.headers,
            url: req.url,
            method: req.method
        });

        const { email, password } = req.body;

        // Find user by email
        const user = await User.findOne({ where: { email } });
        if (!user) {
            console.log('Login failed: User not found:', email);
            return res.status(401).json({ message: 'Invalid credentials' });
        }

        // Check if account is locked
        if (user.lockUntil && user.lockUntil > new Date()) {
            console.log('Login failed: Account locked:', email);
            return res.status(423).json({
                message: `After multiple failed attempts, Account is locked. Try again after 15 minutes`
            });
        }

        // Verify password
        // console.log('About to start bcrypt.compare...');
        // console.log('Password length:', password.length);
        // console.log('Hash length:', user.password.length);
        // console.log('Hash starts with:', user.password.substring(0, 10));

        const isMatch = await bcrypt.compare(password, user.password);
        console.log('Bcrypt.compare completed, result:', isMatch);
        if (!isMatch) {
            // Log the password details for debugging
            // console.log('Password verification failed:', {
            //     providedPassword: password,
            //     hashedPassword: user.password,
            //     isMatch
            // });

            // Increment login attempts
            user.loginAttempts = (user.loginAttempts || 0) + 1;

            // Lock account if max attempts reached
            if (user.loginAttempts >= MAX_LOGIN_ATTEMPTS) {
                user.lockUntil = new Date(Date.now() + 15 * 60 * 1000); // Lock for 15 minutes
            }

            await user.save();

            console.log('Login failed: Invalid password for user:', email);
            if (user.lockUntil) {
                return res.status(423).json({
                    message: `After multiple failed attempts, Account is locked. Try again after 15 minutes`
                });
            }

            return res.status(401).json({ message: 'Invalid credentials' });
        }

        // Reset login attempts on successful login
        user.loginAttempts = 0;
        user.lockUntil = null;
        await user.save();

        // Generate a temporary token for OTP verification
        const tempToken = jwt.sign(
            { id: user.id, email: user.email },
            process.env.JWT_SECRET,
            { expiresIn: '5m' }
        );

        console.log('Login successful:', {
            email: user.email,
            userId: user.id,
            tempTokenGenerated: true
        });

        res.json({
            message: 'Login successful',
            tempToken
        });
    } catch (error) {
        console.error('Login error:', {
            error: error.message,
            stack: error.stack,
            body: req.body
        });
        res.status(500).json({ message: 'Login failed' });
    }
};

const sendOTP = async (req, res) => {
    try {
        const { tempToken } = req.body;

        // Verify temp token
        const decoded = jwt.verify(tempToken, process.env.JWT_SECRET);
        const user = await User.findByPk(decoded.id);

        if (!user) {
            return res.status(401).json({ message: 'Invalid token' });
        }

        // Generate OTP
        const otpValue = Math.floor(100000 + Math.random() * 900000).toString();

        // Save OTP to database
        await OTP.create({
            userId: user.id,
            code: otpValue,
            expiresAt: new Date(Date.now() + 5 * 60000) // 5 minutes
        });

        // Send OTP email
        await sendOTPEmail(user.email, otpValue);

        res.json({ message: 'OTP sent successfully' });
    } catch (error) {
        console.error('Send OTP error:', error);
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({ message: 'Invalid token' });
        }
        res.status(500).json({ message: 'Failed to send OTP' });
    }
};

const verifyOTP = async (req, res) => {
    try {
        const { otp, tempToken } = req.body;

        // Verify temp token
        const decoded = jwt.verify(tempToken, process.env.JWT_SECRET);
        const user = await User.findByPk(decoded.id);

        if (!user) {
            return res.status(401).json({ message: 'Invalid token' });
        }

        // Find valid OTP
        const validOTP = await OTP.findOne({
            where: {
                userId: user.id,
                code: otp,
                isUsed: false,
                expiresAt: {
                    [Op.gt]: new Date()
                }
            }
        });

        if (!validOTP) {
            return res.status(401).json({ message: 'Invalid or expired OTP' });
        }

        // Mark OTP as used
        await validOTP.update({ isUsed: true });

        // Generate JWT token
        const token = jwt.sign(
            {
                id: user.id,
                email: user.email,
                role: user.role
            },
            process.env.JWT_SECRET,
            { expiresIn: '24h' }
        );

        // Send user data and token
        res.json({
            user: {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role
            },
            token
        });
    } catch (error) {
        console.error('Verify OTP error:', error);
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({ message: 'Invalid token' });
        }
        res.status(500).json({ message: 'Failed to verify OTP' });
    }
};

const verify = async (req, res) => {
    try {
        const token = req.headers.authorization?.split(' ')[1];

        if (!token) {
            return res.status(401).json({ message: 'No token provided' });
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findByPk(decoded.id);

        if (!user) {
            return res.status(401).json({ message: 'Invalid token' });
        }

        res.json({
            user: {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role
            }
        });
    } catch (error) {
        console.error('Token verification error:', error);
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({ message: 'Invalid token' });
        }
        res.status(500).json({ message: 'Failed to verify token' });
    }
};

const refreshToken = async (req, res) => {
    try {
        // Get token from Authorization header
        const token = req.headers.authorization?.split(' ')[1];

        if (!token) {
            return res.status(401).json({ message: 'No token provided' });
        }

        try {
            // Try to verify the expired token to get the user ID
            const decoded = jwt.decode(token);

            if (!decoded || !decoded.id) {
                return res.status(401).json({ message: 'Invalid token format' });
            }

            // Find the user
            const user = await User.findByPk(decoded.id);
            if (!user) {
                return res.status(404).json({ message: 'User not found' });
            }

            // Generate new token
            const newToken = jwt.sign(
                {
                    id: user.id,
                    email: user.email,
                    role: user.role
                },
                process.env.JWT_SECRET,
                { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
            );

            // Send new token
            res.json({ token: newToken });

        } catch (error) {
            console.error('Token refresh error:', error);
            return res.status(401).json({ message: 'Invalid token' });
        }
    } catch (error) {
        console.error('Token refresh error:', error);
        res.status(500).json({ message: 'Failed to refresh token' });
    }
};

const logout = async (req, res) => {
    res.clearCookie('jwt');
    res.clearCookie('refreshToken', { path: '/api/auth/refresh' });
    res.json({ message: 'Logged out successfully' });
};

const register = async (req, res) => {
    try {
        const {
            email,
            password,
            firstName,
            lastName,
            role = 'user',
            alternateEmail,
            countryId,
            phoneNumber,
            alternatePhoneNumber,
            businessName,
            businessEmail,
            billingAddressLine1,
            billingAddressLine2,
            billingCity,
            billingProvince,
            billingCountryId,
            billingPostalCode
        } = req.body;

        // Basic validation
        if (!email || !password || !firstName || !lastName) {
            return res.status(400).json({ message: 'Required fields missing' });
        }

        // Additional validation for partners
        if (role === 'partner') {
            if (!businessName || !businessEmail || !billingAddressLine1 || !billingCity || !billingCountryId) {
                return res.status(400).json({ message: 'Required partner fields missing' });
            }
        }

        // Check if user exists
        const existingUser = await User.findOne({ where: { email } });
        if (existingUser) {
            return res.status(400).json({ message: 'Email already registered' });
        }

        // Check business email if provided
        if (businessEmail) {
            const existingBusiness = await User.findOne({ where: { businessEmail } });
            if (existingBusiness) {
                return res.status(400).json({ message: 'Business email already registered' });
            }
        }

        // Create user
        const user = await User.create({
            email,
            password,
            firstName,
            lastName,
            role,
            alternateEmail,
            countryId,
            phoneNumber,
            alternatePhoneNumber,
            businessName,
            businessEmail,
            billingAddressLine1,
            billingAddressLine2,
            billingCity,
            billingProvince,
            billingCountryId,
            billingPostalCode
        });

        // Create wallet for partner
        if (role === 'partner') {
            await Wallet.create({
                userId: user.id,
                balance: 0,
                maxBalance: 1000,
                currencyCode: 'USD',
                isActive: true
            });
        }

        // Generate a temporary token for OTP verification
        const tempToken = jwt.sign(
            { id: user.id, email: user.email },
            process.env.JWT_SECRET,
            { expiresIn: '5m' }
        );

        res.json({
            message: 'Registration successful',
            tempToken
        });
    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({ message: 'Registration failed' });
    }
};

const forgotPassword = async (req, res) => {
    try {
        const { email } = req.body;

        // Find user by email
        const user = await User.findOne({ where: { email } });
        if (!user) {
            return res.status(404).json({ message: 'If an account exists with this email, you will receive password reset instructions.' });
        }

        // Generate reset token
        const resetToken = uuidv4();
        const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour from now

        // Save reset token to user
        await user.update({
            resetToken,
            resetTokenExpiry
        });

        // Send reset password email
        const resetLink = `${process.env.CLIENT_URL}/reset-password?token=${resetToken}`;
        await sendPasswordResetEmail(email, resetLink);

        // Always return success for security
        res.json({ message: 'If an account exists with this email, you will receive password reset instructions.' });
    } catch (error) {
        console.error('Error in forgotPassword:', error);
        res.status(500).json({ message: 'Failed to process password reset request' });
    }
};

const verifyResetToken = async (req, res) => {
    try {
        const { token } = req.body;

        const user = await User.findOne({
            where: {
                resetToken: token,
                resetTokenExpiry: {
                    [Op.gt]: new Date()
                }
            }
        });

        if (!user) {
            return res.status(400).json({ message: 'Invalid or expired reset token' });
        }

        res.json({ message: 'Token is valid' });
    } catch (error) {
        console.error('Error in verifyResetToken:', error);
        res.status(500).json({ message: 'Failed to verify reset token' });
    }
};

const resetPassword = async (req, res) => {
    try {
        const { token, password } = req.body;

        // Validate password
        if (!password || password.length < 8) {
            return res.status(400).json({ message: 'Password must be at least 8 characters long' });
        }

        const user = await User.findOne({
            where: {
                resetToken: token,
                resetTokenExpiry: {
                    [Op.gt]: new Date()
                }
            }
        });

        if (!user) {
            return res.status(400).json({ message: 'Invalid or expired reset token' });
        }

        // Update user password and clear reset token
        await user.update({
            password,  // The beforeSave hook in User model will hash this
            resetToken: null,
            resetTokenExpiry: null,
            loginAttempts: 0,  // Reset login attempts when password is changed
            lockUntil: null    // Remove any account locks
        });

        res.json({ message: 'Password has been reset successfully. Please login with your new password.' });
    } catch (error) {
        console.error('Error in resetPassword:', error);
        res.status(500).json({ message: 'Failed to reset password' });
    }
};

const changePassword = async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;
        const userId = req.user.id;

        // Find user with password
        const user = await User.findByPk(userId);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Verify current password using the model's method
        const isMatch = await user.validatePassword(currentPassword);
        if (!isMatch) {
            return res.status(401).json({ message: 'Current password is incorrect' });
        }

        // Update password (will be hashed by beforeSave hook)
        await user.update({
            password: newPassword,
            loginAttempts: 0,
            lockUntil: null
        });

        res.json({ message: 'Password changed successfully' });
    } catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({ message: 'Failed to change password' });
    }
};

module.exports = {
    login,
    verifyOTP,
    sendOTP,
    verify,
    refreshToken,
    logout,
    register,
    forgotPassword,
    verifyResetToken,
    resetPassword,
    changePassword
};
