const { KnowledgeBase, User } = require('../models');
const { Op } = require('sequelize');

// Get all knowledge base articles
exports.getArticles = async (req, res) => {
    try {
        const {
            search,
            category,
            visibility,
            status,
            page = 1,
            limit = 10
        } = req.query;

        const where = {};
        
        // Search filter
        if (search) {
            where[Op.or] = [
                { title: { [Op.like]: `%${search}%` } },
                { content: { [Op.like]: `%${search}%` } }
            ];
        }

        // Category filter
        if (category) {
            where.category = category;
        }

        // Visibility filter based on user role
        if (req.user.role === 'partner') {
            where.visibility = { [Op.in]: ['public', 'partner'] };
        } else if (!req.user.role === 'admin') {
            where.visibility = 'public';
        }
        if (visibility && req.user.role === 'admin') {
            where.visibility = visibility;
        }

        // Status filter (admin only)
        if (req.user.role === 'admin') {
            if (status) {
                where.status = status;
            }
        } else {
            where.status = 'published';
        }

        const { rows: articles, count } = await KnowledgeBase.findAndCountAll({
            where,
            include: [{
                model: User,
                as: 'author',
                attributes: ['firstName', 'lastName', 'email']
            }],
            order: [['updatedAt', 'DESC']],
            limit: parseInt(limit),
            offset: (parseInt(page) - 1) * parseInt(limit)
        });

        res.json({
            articles,
            totalItems: count,
            totalPages: Math.ceil(count / limit),
            currentPage: parseInt(page)
        });
    } catch (error) {
        console.error('Error fetching knowledge base articles:', error);
        res.status(500).json({ message: 'Failed to fetch articles' });
    }
};

// Get a single article
exports.getArticle = async (req, res) => {
    try {
        const article = await KnowledgeBase.findByPk(req.params.id, {
            include: [{
                model: User,
                as: 'author',
                attributes: ['firstName', 'lastName', 'email']
            }]
        });

        if (!article) {
            return res.status(404).json({ message: 'Article not found' });
        }

        // Check visibility permissions
        if (article.visibility === 'admin' && req.user.role !== 'admin') {
            return res.status(403).json({ message: 'Access denied' });
        }
        if (article.visibility === 'partner' && !['admin', 'partner'].includes(req.user.role)) {
            return res.status(403).json({ message: 'Access denied' });
        }

        res.json(article);
    } catch (error) {
        console.error('Error fetching article:', error);
        res.status(500).json({ message: 'Failed to fetch article' });
    }
};

// Create new article (admin only)
exports.createArticle = async (req, res) => {
    try {
        if (req.user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can create articles' });
        }

        const article = await KnowledgeBase.create({
            ...req.body,
            authorId: req.user.id
        });

        const createdArticle = await KnowledgeBase.findByPk(article.id, {
            include: [{
                model: User,
                as: 'author',
                attributes: ['firstName', 'lastName', 'email']
            }]
        });

        res.status(201).json(createdArticle);
    } catch (error) {
        console.error('Error creating article:', error);
        res.status(400).json({ 
            message: 'Failed to create article',
            error: error.message 
        });
    }
};

// Update article (admin only)
exports.updateArticle = async (req, res) => {
    try {
        if (req.user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can update articles' });
        }

        const article = await KnowledgeBase.findByPk(req.params.id);
        if (!article) {
            return res.status(404).json({ message: 'Article not found' });
        }

        await article.update(req.body);

        const updatedArticle = await KnowledgeBase.findByPk(article.id, {
            include: [{
                model: User,
                as: 'author',
                attributes: ['firstName', 'lastName', 'email']
            }]
        });

        res.json(updatedArticle);
    } catch (error) {
        console.error('Error updating article:', error);
        res.status(400).json({ 
            message: 'Failed to update article',
            error: error.message 
        });
    }
};

// Delete article (admin only)
exports.deleteArticle = async (req, res) => {
    try {
        if (req.user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can delete articles' });
        }

        const article = await KnowledgeBase.findByPk(req.params.id);
        if (!article) {
            return res.status(404).json({ message: 'Article not found' });
        }

        await article.destroy();
        res.json({ message: 'Article deleted successfully' });
    } catch (error) {
        console.error('Error deleting article:', error);
        res.status(500).json({ message: 'Failed to delete article' });
    }
};

// Update article status (admin only)
exports.updateArticleStatus = async (req, res) => {
    try {
        if (req.user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can update article status' });
        }

        const { status } = req.body;
        const article = await KnowledgeBase.findByPk(req.params.id);
        
        if (!article) {
            return res.status(404).json({ message: 'Article not found' });
        }

        await article.update({ status });
        res.json({ message: 'Article status updated successfully' });
    } catch (error) {
        console.error('Error updating article status:', error);
        res.status(400).json({ message: 'Failed to update article status' });
    }
};
