const axios = require('axios');
const config = require('../config/config');

class MobimatterService {
    constructor() {
        this.baseURL = process.env.MOBIMATTER_API_URL || 'https://api.mobimatter.com/mobimatter/api/v2';
        this.apiKey = process.env.MOBIMATTER_API_KEY;
        this.merchantId = process.env.MOBIMATTER_MERCHANT_ID;
        
        this.axiosInstance = axios.create({
            baseURL: this.baseURL,
            headers: {
                'api-key': this.apiKey,
                'merchantId': this.merchantId,
                'Content-Type': 'application/json'
            }
        });
    }

    // Helper method to handle API responses
    handleResponse(response) {
        if (!response.data) {
            throw new Error('No data received from Mobimatter API');
        }
        
        // If the response has statusCode field, validate it
        if ('statusCode' in response.data) {
        const { statusCode, result, message } = response.data;
        if (statusCode !== 200) {
            throw new Error(message || 'Invalid response from Mobimatter API');
            }
            return result;
        }
        
        // For usage data and other endpoints that might return direct data
        return response.data;
    }

    // Get all available products
    async getProducts(category = 'esim_realtime') {
        try {
            const response = await this.axiosInstance.get('/products', {
                params: {
                    category: category
                }
            });
            const products = this.handleResponse(response);
            
            if (!Array.isArray(products)) {
                console.error('Expected array of products but got:', typeof products);
                return [];
            }

            // Map Mobimatter products to our format
            return products.map(product => {
                // Extract data amount and validity from product details
                const dataDetails = product.productDetails?.find(detail => 
                    detail.name?.toLowerCase().includes('data') || 
                    detail.name?.toLowerCase().includes('gb') ||
                    detail.name?.toLowerCase().includes('mb')
                );
                const validityDetails = product.productDetails?.find(detail => 
                    detail.name?.toLowerCase().includes('validity') || 
                    detail.name?.toLowerCase().includes('days')
                );

                // Get data amount from display attributes if not in product details
                const dataAttribute = product.displayAttributes?.find(attr => 
                    attr.key?.toLowerCase().includes('data') || 
                    attr.key?.toLowerCase().includes('gb') ||
                    attr.key?.toLowerCase().includes('mb')
                );

                // Parse data amount
                let dataAmount = null;
                let dataUnit = 'GB';
                if (dataDetails?.value) {
                    const match = dataDetails.value.match(/(\d+)\s*(GB|MB|TB)/i);
                    if (match) {
                        dataAmount = parseFloat(match[1]);
                        dataUnit = match[2].toUpperCase();
                    }
                } else if (dataAttribute?.value) {
                    const match = dataAttribute.value.match(/(\d+)\s*(GB|MB|TB)/i);
                    if (match) {
                        dataAmount = parseFloat(match[1]);
                        dataUnit = match[2].toUpperCase();
                    }
                }

                // Parse validity days
                let validityDays = null;
                if (validityDetails?.value) {
                    const match = validityDetails.value.match(/(\d+)/);
                    if (match) {
                        validityDays = parseInt(match[1]);
                    }
                }

                // Format features from display attributes
                const features = product.displayAttributes?.map(attr => ({
                    name: attr.key,
                    value: attr.value
                })) || [];

                // Format custom data from product details
                const customData = product.productDetails?.map(detail => ({
                    name: detail.name,
                    value: detail.value
                })) || [];

                // Check if plan has voice features and extract voice minutes
                let voiceMinutes = null;
                const voiceDetails = product.productDetails?.find(detail => 
                    detail.name === 'PLAN_VOICE_LIMIT' ||
                    (detail.name?.toLowerCase().includes('voice') || detail.name?.toLowerCase().includes('minutes'))
                );
                
                if (voiceDetails?.value) {
                    const match = voiceDetails.value.match(/(\d+)\s*(min|minutes)/i);
                    if (match) {
                        voiceMinutes = parseInt(match[1]);
                    } else {
                        const numMatch = voiceDetails.value.match(/(\d+)/);
                        if (numMatch) {
                            voiceMinutes = parseInt(numMatch[1]);
                        }
                    }
                }

                const hasVoice = voiceMinutes !== null || product.productDetails?.some(detail => 
                    detail.name === 'HAS_CALLS' && detail.value === '1'
                );

                // Check if plan has SMS
                const hasSms = product.productDetails?.some(detail => 
                    detail.name === 'HAS_SMS' && detail.value === '1'
                );

                // Check if plan is unlimited
                const isUnlimited = product.productDetails?.some(detail => 
                    detail.value?.toLowerCase().includes('unlimited')
                ) || product.displayAttributes?.some(attr => 
                    attr.value?.toLowerCase().includes('unlimited')
                );

                return {
                    id: product.uniqueId,
                    skuId: product.uniqueId,
                    name: product.productFamilyName,
                    description: product.productFamilyName,
                    price: product.wholesalePrice,
                    dataAmount,
                    dataUnit,
                    validityDays: validityDays || parseInt(product.validityDays) || 30,
                    supportedCountries: product.countries || [],
                    region: product.regions || '',
                    networkName: product.providerName,
                    isUnlimited,
                    hasVoice,
                    hasSms,
                    voiceMinutes,
                    features,
                    customData,
                    networkType: product.network_short,
                    category: product.productCategory || category,
                    providerMetadata: {
                        productId: product.productId,
                        uniqueId: product.uniqueId,
                        productCategoryId: product.productCategoryId,
                        productFamilyId: product.productFamilyId,
                        networkListId: product.networkListId,
                        providerId: product.providerId,
                        providerLogo: product.providerLogo,
                        currencyCode: product.currencyCode,
                        rank: product.rank,
                        displayAttributes: product.displayAttributes || [],
                        productDetails: product.productDetails || []
                    }
                };
            });
        } catch (error) {
            console.error('Error fetching Mobimatter products:', error);
            return [];
        }
    }

    // Get specific product details
    async getProductDetails(productId) {
        try {
            const response = await this.axiosInstance.get(`/products/${productId}`);
            return this.handleResponse(response);
        } catch (error) {
            console.error(`Error fetching Mobimatter product ${productId}:`, error);
            throw error;
        }
    }

    // Create an order
    async createOrder(orderData) {
        try {
            // Ensure required fields are present
            if (!orderData.productId || !orderData.skuId) {
                throw new Error('Product ID and SKU ID are required');
            }

            // Format request payload according to MobiMatter API requirements
            const payload = {
                productId: orderData.productId,
                skuId: orderData.skuId,
                quantity: orderData.quantity || 1,
                customerReference: orderData.customerReference,
                callbackUrl: process.env.MOBIMATTER_CALLBACK_URL || null
            };

            console.log('Creating MobiMatter order with payload:', payload);

            const response = await this.axiosInstance.post('/order', payload);
            
            console.log('MobiMatter API Response:', response.data);
            
            if (!response.data || !response.data.result) {
                const errorMsg = response.data?.message || 'Unknown error';
                console.error('Order creation failed:', errorMsg);
                throw new Error(`Order creation failed: ${errorMsg}`);
            }

            return this.handleResponse(response);
        } catch (error) {
            console.error('Error creating Mobimatter order:', error.response?.data || error.message);
            if (error.response?.status === 404) {
                throw new Error('Invalid product ID or SKU ID');
            }
            throw new Error(error.response?.data?.message || error.message);
        }
    }

    // Get order status
    async getOrderStatus(orderId) {
        try {
            if (!orderId) {
                throw new Error('Order ID is required');
            }

            console.log('Fetching MobiMatter order status:', orderId);
            
            const response = await this.axiosInstance.get(`/order/${orderId}`);
            
            console.log('MobiMatter Order Status Response:', response.data);
            
            return this.handleResponse(response);
        } catch (error) {
            console.error(`Error fetching Mobimatter order ${orderId}:`, error.response?.data || error.message);
            throw new Error(error.response?.data?.message || error.message);
        }
    }

    // Get order by ICCID
    async getOrderByIccid(iccid) {
        try {
            const response = await this.axiosInstance.get(`/order/iccid/${iccid}`);
            return this.handleResponse(response);
        } catch (error) {
            console.error(`Error fetching Mobimatter order by ICCID ${iccid}:`, error);
            throw error;
        }
    }

    // Complete an order
    async completeOrder(orderId) {
        try {
            if (!orderId) {
                throw new Error('Order ID is required');
            }

            // First check the order status
            const orderStatus = await this.getOrderStatus(orderId);
            if (!orderStatus) {
                throw new Error('Could not fetch order status');
            }

            // Check if order is in a state that can be completed
            if (orderStatus.orderState !== 'Created') {
                throw new Error(`Order cannot be completed. Current state: ${orderStatus.orderState}`);
            }

            console.log('Completing MobiMatter order:', orderId);
            
            // Try to complete the order using PUT method and correct endpoint
            const response = await this.axiosInstance.put('/order/complete', {
                orderId: orderId,
                notes: null // Optional parameter from API spec
            });
            
            console.log('MobiMatter Order Completion Response:', response.data);
            
            return this.handleResponse(response);
        } catch (error) {
            console.error(`Error completing Mobimatter order ${orderId}:`, error.response?.data || error.message);
            throw new Error(error.response?.data?.message || error.message);
        }
    }

    // Cancel order
    async cancelOrder(orderId) {
        try {
            const response = await this.axiosInstance.post(`/order/${orderId}/cancel`);
            return this.handleResponse(response);
        } catch (error) {
            console.error(`Error cancelling Mobimatter order ${orderId}:`, error);
            throw error;
        }
    }

    // Get usage info
    async getUsageInfo(orderId) {
        try {
            if (!orderId) {
                throw new Error('Order ID is required');
            }

            console.log('Calling Mobimatter usage API for order:', orderId);
            
            const response = await this.axiosInstance.get(`/provider/usage/${orderId}`);
            console.log('Raw Mobimatter usage API response:', response.data);
            
            // Validate response structure
            if (!response.data) {
                return {
                    dataUsage: null,
                    dataAllowance: null,
                    status: 'No usage data available',
                    expiryDate: null,
                    lastUpdateTime: new Date().toISOString()
                };
            }

            // Handle error responses
            if (response.data.error) {
                return {
                    dataUsage: null,
                    dataAllowance: null,
                    status: 'Error fetching usage data',
                    expiryDate: null,
                    lastUpdateTime: new Date().toISOString(),
                    error: response.data.error
                };
            }

            // Extract provider info
            const providerInfo = response.data.providerInfo;
            if (!providerInfo) {
                return {
                    dataUsage: null,
                    dataAllowance: null,
                    status: 'No usage data available',
                    expiryDate: null,
                    lastUpdateTime: new Date().toISOString()
                };
            }

            // Parse the balance string to extract usage data
            // Example: "USA 5 GB used 2198MB out of 5120. Expires 5/25/2025 5:32:01 PM"
            const balanceInfo = providerInfo.data?.balance;
            let dataUsage = null;
            let dataAllowance = null;
            let expiryDate = null;

            if (balanceInfo) {
                // Extract usage numbers and convert to bytes
                const usageMatch = balanceInfo.match(/used\s+(\d+)(MB|GB)\s+out\s+of\s+(\d+)/i);
                if (usageMatch) {
                    const [, usedAmount, usedUnit, totalAmount] = usageMatch;
                    // Convert to bytes
                    const multiplier = usedUnit.toUpperCase() === 'GB' ? 1024 * 1024 * 1024 : 1024 * 1024;
                    dataUsage = parseInt(usedAmount) * multiplier;
                    dataAllowance = parseInt(totalAmount) * multiplier;
                }

                // Extract expiry date
                const expiryMatch = balanceInfo.match(/Expires\s+(\d{1,2}\/\d{1,2}\/\d{4}\s+\d{1,2}:\d{2}:\d{2}\s+[AP]M)/i);
                if (expiryMatch) {
                    expiryDate = new Date(expiryMatch[1]).toISOString();
                }
            }

            // Determine status based on expiry date and data usage
            let status = 'Unknown';
            const now = new Date();
            
            if (expiryDate) {
                const expiryDateTime = new Date(expiryDate);
                if (expiryDateTime < now) {
                    status = 'Expired';
                } else if (dataAllowance && dataUsage >= dataAllowance) {
                    status = 'Data Depleted';
                } else {
                    status = 'Active';
                }
            } else if (providerInfo.data?.status) {
                // Fallback to provider status if available
                status = providerInfo.data.status;
            }

            const transformedData = {
                dataUsage,
                dataAllowance,
                status,
                expiryDate,
                activationDate: providerInfo.data?.activationDate,
                providerName: providerInfo.providerName,
                providerLogo: providerInfo.providerLogo,
                phoneNumber: providerInfo.data?.phone,
                message: providerInfo.message,
                lastUpdateTime: new Date().toISOString()
            };

            console.log('Transformed usage data:', transformedData);
            return transformedData;
        } catch (error) {
            console.error(`Error fetching usage info for order ${orderId}:`, error);
            if (error.response?.status === 400 && error.response?.data === 'No record found') {
                return {
                    dataUsage: null,
                    dataAllowance: null,
                    status: 'Not activated',
                    expiryDate: null,
                    lastUpdateTime: new Date().toISOString(),
                    message: 'Usage data not available yet. The eSIM may not be activated.'
                };
            }
            
            if (error.response) {
                console.error('Error response status:', error.response.status);
                console.error('Error response data:', error.response.data);
            }
            
            // Return a structured error response instead of throwing
            return {
                dataUsage: null,
                dataAllowance: null,
                status: 'Error',
                expiryDate: null,
                lastUpdateTime: new Date().toISOString(),
                error: error.response?.data || error.message
            };
        }
    }

    // Search/filter products
    async searchProducts(params) {
        try {
            const response = await this.axiosInstance.get('/products/search', { params });
            return this.handleResponse(response);
        } catch (error) {
            console.error('Error searching Mobimatter products:', error);
            throw error;
        }
    }
}

module.exports = new MobimatterService(); 