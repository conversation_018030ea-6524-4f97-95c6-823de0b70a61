const User = require('../models/User');
const Country = require('../models/Country');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const bcrypt = require('bcrypt');
const emailService = require('../utils/emailService');

// Get all partners
exports.getAllPartners = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const search = req.query.search || '';
        const sortBy = req.query.sortBy || 'latest'; // 'latest' or 'oldest'
        const countryId = req.query.countryId;

        const whereClause = {
            role: 'partner',
            ...(countryId && { countryId }),
            [Op.or]: [
                { firstName: { [Op.like]: `%${search}%` } },
                { lastName: { [Op.like]: `%${search}%` } },
                { email: { [Op.like]: `%${search}%` } },
                { businessName: { [Op.like]: `%${search}%` } },
                { businessEmail: { [Op.like]: `%${search}%` } }
            ]
        };

        const { count, rows: partners } = await User.findAndCountAll({
            where: whereClause,
            attributes: [
                'id',
                'firstName',
                'lastName',
                'email',
                'businessEmail',
                'businessName',
                'phoneNumber',
                'countryId',
                'billingAddressLine1',
                'billingAddressLine2',
                'billingCity',
                'billingProvince',
                'billingCountryId',
                'billingPostalCode',
                'markupPercentage',
                'isActive',
                'createdAt',
                [
                    sequelize.literal('(SELECT IFNULL(balance, 0) FROM wallets WHERE wallets.userId = User.id)'),
                    'walletBalance'
                ]
            ],
            include: [
                {
                    model: Country,
                    as: 'country',
                    attributes: ['name'],
                    where: search ? {
                        name: { [Op.like]: `%${search}%` }
                    } : undefined,
                    required: false
                },
                {
                    model: sequelize.models.Wallet,
                    as: 'wallet',
                    attributes: ['balance'],
                    required: false
                }
            ],
            order: [
                ...(sortBy === 'balance' || sortBy === 'balance_desc' ? [
                    ['walletBalance', sortBy === 'balance_desc' ? 'DESC' : 'ASC']
                ] : [
                    ['createdAt', sortBy === 'latest' ? 'DESC' : 'ASC']
                ])
            ],
            limit,
            offset
        });

        // Get all countries for the filter dropdown
        const countries = await Country.findAll({
            attributes: ['id', 'name'],
            order: [['name', 'ASC']]
        });

        res.json({
            partners,
            totalPages: Math.ceil(count / limit),
            currentPage: page,
            totalItems: count,
            countries
        });
    } catch (error) {
        console.error('Error fetching partners:', error);
        res.status(500).json({ message: 'Error fetching partners' });
    }
};

// Get partner by ID
exports.getPartnerById = async (req, res) => {
    try {
        // Check if user is accessing their own details or is an admin
        if (req.user.role !== 'admin' && req.user.id !== req.params.id) {
            return res.status(403).json({ message: 'Access denied' });
        }

        const partner = await User.findOne({
            where: {
                id: req.params.id,
                role: 'partner'
            },
            attributes: [
                'id',
                'firstName',
                'lastName',
                'email',
                'alternateEmail',
                'countryId',
                'phoneNumber',
                'alternatePhoneNumber',
                'businessName',
                'businessEmail',
                'billingAddressLine1',
                'billingAddressLine2',
                'billingCity',
                'billingProvince',
                'billingCountryId',
                'billingPostalCode',
                'markupPercentage',
                'isActive',
                'createdAt'
            ],
            include: [
                {
                    model: Country,
                    as: 'country',
                    attributes: ['name']
                },
                {
                    model: Country,
                    as: 'billingCountry',
                    attributes: ['name']
                }
            ]
        });

        if (!partner) {
            return res.status(404).json({ message: 'Partner not found' });
        }

        res.json(partner);
    } catch (error) {
        console.error('Error fetching partner:', error);
        res.status(500).json({ message: 'Error fetching partner' });
    }
};

// Create new partner
exports.createPartner = async (req, res) => {
    try {
        // Check if email already exists
        const existingEmail = await User.findOne({ where: { email: req.body.email } });
        if (existingEmail) {
            return res.status(400).json({ message: 'Email already registered' });
        }

        // Check if business email already exists
        if (req.body.businessEmail) {
            const existingBusiness = await User.findOne({ where: { businessEmail: req.body.businessEmail } });
            if (existingBusiness) {
                return res.status(400).json({ message: 'Business email already registered' });
            }
        }

        // Validate markup percentage
        if (!req.body.markupPercentage) {
            return res.status(400).json({ message: 'Markup percentage is required' });
        }

        const markup = Number(req.body.markupPercentage);
        if (isNaN(markup)) {
            return res.status(400).json({ message: 'Invalid markup percentage format' });
        }
        if (markup < 0 || markup > 100) {
            return res.status(400).json({ message: 'Markup percentage must be between 0 and 100' });
        }
        req.body.markupPercentage = markup;

        // Store the plain text password before it gets hashed
        const plainTextPassword = req.body.password;

        // Create partner with role and provided password
        const partner = await User.create({
            ...req.body,
            role: 'partner'
        });

        // Send welcome email to the partner with credentials
        try {
            await emailService.sendPartnerWelcomeEmail(partner.email, partner, plainTextPassword);
            console.log(`Welcome email sent to partner: ${partner.email}`);
        } catch (emailError) {
            console.error('Failed to send welcome email to partner:', emailError);
            // Don't fail the partner creation if email fails
        }

        // Send notification email to admins
        try {
            // Get all admin users
            const admins = await User.findAll({
                where: { role: 'admin', isActive: true },
                attributes: ['email']
            });

            if (admins && admins.length > 0) {
                const adminEmails = admins.map(admin => admin.email);
                await emailService.sendAdminPartnerNotificationEmail(adminEmails, partner);
                console.log(`Admin notification sent to ${adminEmails.length} admins for new partner: ${partner.email}`);
            }
        } catch (emailError) {
            console.error('Failed to send admin notification email:', emailError);
            // Don't fail the partner creation if email fails
        }

        res.status(201).json({
            message: 'Partner created successfully',
            partner: {
                id: partner.id,
                firstName: partner.firstName,
                lastName: partner.lastName,
                email: partner.email,
                businessEmail: partner.businessEmail,
                markupPercentage: partner.markupPercentage
            }
        });
    } catch (error) {
        console.error('Error creating partner:', error);
        res.status(500).json({ message: 'Error creating partner', error: error.message });
    }
};

// Update partner
exports.updatePartner = async (req, res) => {
    try {
        console.log('Updating partner with data:', req.body);

        const partner = await User.findOne({
            where: {
                id: req.params.id,
                role: 'partner'
            }
        });

        if (!partner) {
            return res.status(404).json({ message: 'Partner not found' });
        }

        // Check email uniqueness if being updated
        if (req.body.email && req.body.email !== partner.email) {
            const existingEmail = await User.findOne({ where: { email: req.body.email } });
            if (existingEmail) {
                return res.status(400).json({ message: 'Email already registered' });
            }
        }

        // Check business email uniqueness if being updated
        if (req.body.businessEmail && req.body.businessEmail !== partner.businessEmail) {
            const existingBusiness = await User.findOne({ where: { businessEmail: req.body.businessEmail } });
            if (existingBusiness) {
                return res.status(400).json({ message: 'Business email already registered' });
            }
        }

        // Validate markup percentage
        if (req.body.markupPercentage !== undefined) {
            const markup = Number(req.body.markupPercentage);
            if (isNaN(markup)) {
                return res.status(400).json({ message: 'Invalid markup percentage format' });
            }
            if (markup !== null && (markup < 0 || markup > 100)) {
                return res.status(400).json({ message: 'Markup percentage must be between 0 and 100' });
            }
            req.body.markupPercentage = markup === '' ? null : markup;
        }

        console.log('Before update - Partner data:', partner.toJSON());
        await partner.update(req.body);
        console.log('After update - Partner data:', partner.toJSON());

        res.json({
            message: 'Partner updated successfully',
            partner: {
                id: partner.id,
                firstName: partner.firstName,
                lastName: partner.lastName,
                email: partner.email,
                businessEmail: partner.businessEmail,
                markupPercentage: partner.markupPercentage
            }
        });
    } catch (error) {
        console.error('Error updating partner:', error);
        res.status(500).json({ message: 'Error updating partner', error: error.message });
    }
};

// Delete partner
exports.deletePartner = async (req, res) => {
    try {
        const partner = await User.findOne({
            where: {
                id: req.params.id,
                role: 'partner'
            }
        });

        if (!partner) {
            return res.status(404).json({ message: 'Partner not found' });
        }

        await partner.destroy();

        res.json({ message: 'Partner deleted successfully' });
    } catch (error) {
        console.error('Error deleting partner:', error);
        res.status(500).json({ message: 'Error deleting partner' });
    }
};

// Toggle partner status
exports.togglePartnerStatus = async (req, res) => {
    try {
        const partner = await User.findOne({
            where: {
                id: req.params.id,
                role: 'partner'
            }
        });

        if (!partner) {
            return res.status(404).json({ message: 'Partner not found' });
        }

        await partner.update({ isActive: !partner.isActive });

        res.json({
            message: `Partner ${partner.isActive ? 'activated' : 'deactivated'} successfully`,
            isActive: partner.isActive
        });
    } catch (error) {
        console.error('Error toggling partner status:', error);
        res.status(500).json({ message: 'Error toggling partner status' });
    }
};
