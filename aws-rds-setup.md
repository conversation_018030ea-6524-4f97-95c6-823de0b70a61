# Setting Up AWS RDS for Your eSIM Platform

This guide will help you set up an AWS RDS MySQL instance for your eSIM Platform backend.

## Step 1: Create an RDS Instance

1. **Log in to the AWS Management Console**
   - Go to [aws.amazon.com](https://aws.amazon.com/) and sign in

2. **Navigate to RDS**
   - Go to Services → RDS

3. **Create a database**
   - Click "Create database"
   - Choose "Standard create"
   - Select "MySQL" as the engine type
   - Choose MySQL version 8.0 or later

4. **Configure settings**
   - **Templates**: Choose "Production" for high availability or "Dev/Test" for lower costs
   - **DB instance identifier**: Enter a name (e.g., `esim-db`)
   - **Credentials**:
     - **Master username**: Create a username (e.g., `admin`)
     - **Master password**: Create a strong password
   - **DB instance class**: 
     - For production: Choose at least db.t3.small
     - For development: db.t3.micro is sufficient
   - **Storage**:
     - Type: General Purpose SSD (gp2)
     - Allocated storage: 20 GB (increase as needed)
     - Enable storage autoscaling
   - **Availability & durability**:
     - For production: Choose "Multi-AZ deployment"
     - For development: Single AZ is sufficient
   - **Connectivity**:
     - VPC: Choose your default VPC
     - Subnet group: Default
     - Public access: No (for security)
     - VPC security group: Create new or use existing
     - Availability Zone: No preference
     - Database port: 3306 (default)
   - **Additional configuration**:
     - Initial database name: `esim_prod` (or your preferred name)
     - DB parameter group: default
     - Option group: default
     - Enable automated backups
     - Backup retention period: 7 days
     - Backup window: No preference
     - Enable encryption
     - Enable Performance Insights (optional)
     - Enable Enhanced monitoring (optional)
     - Enable auto minor version upgrade

5. **Create database**
   - Review your settings
   - Click "Create database"

## Step 2: Configure Security Group

1. **Navigate to the RDS dashboard**
   - Click on your newly created database
   - Note the VPC security group

2. **Edit the security group**
   - Click on the security group link
   - Go to "Inbound rules"
   - Click "Edit inbound rules"
   - Add a rule:
     - Type: MySQL/Aurora
     - Protocol: TCP
     - Port range: 3306
     - Source: Custom
     - Enter your EC2 instance's security group ID or private IP address
   - Click "Save rules"

## Step 3: Initialize the Database

1. **Connect to your EC2 instance**
   ```
   ssh -i your-key.pem ubuntu@your-ec2-public-ip
   ```

2. **Install MySQL client**
   ```
   sudo apt-get update
   sudo apt-get install -y mysql-client
   ```

3. **Connect to your RDS instance**
   ```
   mysql -h your-rds-endpoint -u admin -p
   ```
   Enter your password when prompted

4. **Create database schema**
   - You can either:
     - Run the SQL commands directly
     - Or use a SQL file:
       ```
       mysql -h your-rds-endpoint -u admin -p esim_prod < ~/esim-project/server/src/config/databaseSchema.sql
       ```

## Step 4: Update GitHub Secrets

1. **Go to your GitHub repository**
   - Click "Settings" → "Secrets and variables" → "Actions"

2. **Update database secrets**
   - `DB_HOST`: Your RDS endpoint (e.g., `esim-db.abcdefghijkl.us-east-1.rds.amazonaws.com`)
   - `DB_USER`: Your RDS master username (e.g., `admin`)
   - `DB_PASSWORD`: Your RDS master password
   - `DB_NAME`: Your database name (e.g., `esim_prod`)

## Step 5: Deploy Your Application

1. **Push to the main branch to trigger deployment**
   ```
   git push origin main
   ```

2. **Monitor the workflow execution**
   - Go to your GitHub repository
   - Click "Actions"
   - Click on the running workflow
   - Watch the logs to ensure everything is working correctly

## Benefits of Using AWS RDS

- **Managed Service**: AWS handles backups, patching, and high availability
- **Better Performance**: Dedicated database resources
- **Scalability**: Easy to scale up or down as needed
- **Reliability**: Multi-AZ deployments for high availability
- **Separation of Concerns**: Database and application can be scaled independently

## Troubleshooting

### Connection Issues

If your application cannot connect to the RDS instance:

1. **Check security group rules**
   - Ensure your EC2 instance's security group or IP is allowed

2. **Verify credentials**
   - Double-check the DB_HOST, DB_USER, DB_PASSWORD, and DB_NAME in GitHub Secrets

3. **Test connection from EC2**
   ```
   mysql -h your-rds-endpoint -u admin -p
   ```

### Performance Issues

If you experience slow database performance:

1. **Check instance class**
   - Consider upgrading to a larger instance class

2. **Monitor with Performance Insights**
   - Enable and use AWS Performance Insights to identify bottlenecks

3. **Optimize queries**
   - Review and optimize slow queries

### Backup and Restore

To create a manual backup:

1. **Create a snapshot in RDS console**
   - Go to RDS → Databases → Your DB → Actions → Take snapshot

To restore from a backup:

1. **Restore from snapshot**
   - Go to RDS → Snapshots → Select your snapshot → Actions → Restore snapshot
