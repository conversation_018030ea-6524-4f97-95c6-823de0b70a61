const express = require('express');
const router = express.Router();
const staffController = require('../controllers/staffController');
const { isAuthenticated, isAdmin } = require('../middleware/auth');

// All routes require authentication and admin role
router.use(isAuthenticated, isAdmin);

// Get all staff
router.get('/', staffController.getAllStaff);

// Get staff by ID
router.get('/:id', staffController.getStaffById);

// Create new staff
router.post('/', staffController.createStaff);

// Update staff
router.put('/:id', staffController.updateStaff);

// Delete staff
router.delete('/:id', staffController.deleteStaff);

// Toggle staff status
router.patch('/:id/toggle-status', staffController.toggleStaffStatus);

module.exports = router;
