'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('users', 'apiKey', {
      type: Sequelize.STRING(100),
      allowNull: true,
      unique: true
    });
    
    await queryInterface.addColumn('users', 'apiKeyHash', {
      type: Sequelize.STRING,
      allowNull: true
    });
    
    await queryInterface.addColumn('users', 'apiKeyLastReset', {
      type: Sequelize.DATE,
      allowNull: true
    });

    // Create index on apiKeyHash for faster lookups
    await queryInterface.addIndex('users', ['apiKeyHash'], {
      name: 'idx_users_api_key_hash'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex('users', 'idx_users_api_key_hash');
    await queryInterface.removeColumn('users', 'apiKeyLastReset');
    await queryInterface.removeColumn('users', 'apiKeyHash');
    await queryInterface.removeColumn('users', 'apiKey');
  }
}; 