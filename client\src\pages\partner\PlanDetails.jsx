import { useState, useEffect, useMemo, useCallback } from 'react';
import { useParams, useNavigate, useOutletContext } from 'react-router-dom';
import api from '../../lib/axios';
import { useToast } from '@/components/ui/use-toast';
import {
    Card,
    CardHeader,
    CardTitle,
    CardDescription,
    CardContent,
    CardFooter
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
    ArrowLeft,
    Globe,
    Clock,
    Wifi,
    Signal,
    MapPin,
    Database,
    ArrowDownUp,
    ArrowUpToLine,
    Gauge,
    Newspaper,
    ListChecks,
    PhoneCall,
    ClipboardPenLine,
    ScrollText,
    Inbox
} from "lucide-react";
import { Separator } from "@/components/ui/separator";

const PlanDetails = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const { toast } = useToast();
    const { fetchCartCount } = useOutletContext();
    const [plan, setPlan] = useState(null);
    const [loading, setLoading] = useState(true);
    const [countryMap, setCountryMap] = useState(() => {
        // Try to get from sessionStorage first
        const cached = sessionStorage.getItem('countryMap');
        return cached ? JSON.parse(cached) : {};
    });

    // Memoize the fetchCountries function
    const fetchCountries = useCallback(async () => {
        // Check if we already have countries in sessionStorage
        const cached = sessionStorage.getItem('countryMap');
        if (cached) {
            return JSON.parse(cached);
        }

        try {
            const response = await api.get('/api/countries');
            const mapping = {};
            response.data.forEach(country => {
                mapping[country.id.toLowerCase()] = country.name;
            });
            // Cache in sessionStorage
            sessionStorage.setItem('countryMap', JSON.stringify(mapping));
            return mapping;
        } catch (err) {
            console.error('Error fetching countries:', err);
            return {};
        }
    }, []);

    // Memoize the fetchPlanDetails function
    const fetchPlanDetails = useCallback(async () => {
        try {
            setLoading(true);
            // Check cache first with timestamp validation (30 minutes)
            const cachedData = sessionStorage.getItem(`plan_${id}`);
            if (cachedData) {
                try {
                    const { plan: cachedPlan, timestamp } = JSON.parse(cachedData);
                    const now = Date.now();
                    const thirtyMinutes = 30 * 60 * 1000;

                    // Use cache if it's less than 30 minutes old
                    if (now - timestamp < thirtyMinutes) {
                        setPlan(cachedPlan);
                        setLoading(false);
                        return;
                    }
                } catch (e) {
                    console.warn('Error parsing cached plan data:', e);
                    // Continue with fetching fresh data
                }
            }

            const response = await api.get(`/api/esim-plans/partner/${id}`);
            const planData = response.data;

            // Cache the plan data with timestamp
            try {
                sessionStorage.setItem(`plan_${id}`, JSON.stringify({
                    plan: planData,
                    timestamp: Date.now()
                }));
            } catch (storageError) {
                // console.warn('Error caching plan data:', storageError);
                // Try to clear some space first
                clearOldCache();
                try {
                    // Try again after clearing cache
                    sessionStorage.setItem(`plan_${id}`, JSON.stringify({
                        plan: planData,
                        timestamp: Date.now()
                    }));
                } catch (e) {
                    // console.error('Failed to cache plan data even after cleanup:', e);
                }
            }

            setPlan(planData);
        } catch (err) {
            toast({
                variant: "destructive",
                title: "Error",
                description: err.response?.data?.message || "Failed to fetch plan details"
            });
        } finally {
            setLoading(false);
        }
    }, [id, toast]);

    // Function to clear old cache items to make space
    const clearOldCache = useCallback(() => {
        try {
            // Get all keys in sessionStorage
            const keys = Object.keys(sessionStorage);

            // Look for old plan cache entries and remove them
            const oldPlanCacheKeys = keys.filter(key =>
                key.startsWith('plan_') && key !== `plan_${id}`
            );

            // Remove old plan cache entries
            oldPlanCacheKeys.forEach(key => {
                try {
                    sessionStorage.removeItem(key);
                } catch (e) {
                    console.warn(`Failed to remove old cache item: ${key}`, e);
                }
            });

            // If still not enough space, remove other types of cache
            if (oldPlanCacheKeys.length === 0) {
                // Remove old plans_ cache entries that start with 'plans_' but keep the most recent
                const plansCacheKeys = keys.filter(key => key.startsWith('plans_'))
                    .sort((a, b) => {
                        // Try to sort by timestamp if available
                        try {
                            const aData = JSON.parse(sessionStorage.getItem(a));
                            const bData = JSON.parse(sessionStorage.getItem(b));
                            return (bData.timestamp || 0) - (aData.timestamp || 0);
                        } catch (e) {
                            return 0; // Keep original order if parsing fails
                        }
                    });

                // Remove all but the most recent 5 entries
                if (plansCacheKeys.length > 5) {
                    plansCacheKeys.slice(5).forEach(key => {
                        try {
                            sessionStorage.removeItem(key);
                        } catch (e) {
                            console.warn(`Failed to remove plans cache item: ${key}`, e);
                        }
                    });
                }
            }
        } catch (e) {
            console.error('Error clearing old cache:', e);
        }
    }, [id]);

    useEffect(() => {
        // Only fetch countries if we don't have them in state
        if (Object.keys(countryMap).length === 0) {
            fetchCountries().then(setCountryMap);
        }
    }, [fetchCountries, countryMap]);

    useEffect(() => {
        if (id) {
            // Clear the cache when component mounts to ensure fresh data
            sessionStorage.removeItem(`plan_${id}`);
            fetchPlanDetails();
        }

        // Cleanup function to handle cache invalidation
        return () => {
            // Check if filters were cleared before navigating to details
            const filtersCleared = sessionStorage.getItem('filtersCleared');
            if (filtersCleared === 'true') {
                // If filters were cleared, don't set any navigation flags that would restore filters
                return;
            }

            // Set flag when component unmounts (when navigating back)
            sessionStorage.setItem('fromPlanDetails', 'true');

            // Save the current timestamp to validate when we return
            sessionStorage.setItem('lastDetailViewTime', Date.now().toString());
        };
    }, [id, fetchPlanDetails]);

    // Memoize the handleAddToCart function
    const handleAddToCart = useCallback(async () => {
        try {
            await api.post('/api/cart', { esimPlanId: plan.id });
            await fetchCartCount();
            toast({
                title: 'Success',
                description: 'Plan added to cart'
            });
            navigate('/dashboard/cart');
        } catch (err) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: err.response?.data?.message || 'Failed to add plan to cart'
            });
        }
    }, [plan?.id, fetchCartCount, toast, navigate]);

    // Memoize the coverage section data
    const coverageSection = useMemo(() => {
        if (!plan) return null;

        return plan.provider?.name === 'Mobimatter' ? (
            // For Mobimatter plans, use the supported countries from provider metadata
            plan.providerMetadata?.originalData?.supportedCountries?.map((countryCode, index) => {
                const countryName = countryMap[countryCode.toLowerCase()] || countryCode;
                return (
                    <div
                        key={index}
                        className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg"
                    >
                        <img
                            src={`https://flagcdn.com/w20/${countryCode.toLowerCase()}.png`}
                            alt={`${countryName} flag`}
                            className="w-6 h-5 object-cover rounded-sm"
                            title={countryName}
                            loading="lazy"
                            onError={(e) => {
                                e.target.onerror = null;
                                e.target.src = 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/No_flag.svg/32px-No_flag.svg.png';
                            }}
                        />
                        <span className="text-sm font-medium">{countryName}</span>
                    </div>
                );
            })
        ) : (
            // For local plans, use the countries array
            plan.countries?.map((country) => (
                <div
                    key={country.id}
                    className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg"
                >
                    <img
                        src={country.flagUrl}
                        alt={`${country.name} flag`}
                        className="w-6 h-5 object-cover rounded-sm"
                        title={country.name}
                        loading="lazy"
                        onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/No_flag.svg/32px-No_flag.svg.png';
                        }}
                    />
                    <span className="text-sm font-medium">{country.name}</span>
                </div>
            ))
        );
    }, [plan, countryMap]);

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
        );
    }

    if (!plan) {
        return (
            <div className="container mx-auto p-6 text-center">
                <h2 className="text-2xl font-bold text-gray-800">Plan not found</h2>
                <p className="text-gray-600 mt-2">The plan you&apos;re looking for doesn&apos;t exist or has been removed.</p>
                <Button
                    variant="outline"
                    onClick={() => navigate(-1)}
                    className="mt-4"
                >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back
                </Button>
            </div>
        );
    }

    // console.log('Rendering with plan:', {
    //     providerName: plan.provider?.name,
    //     isMobimatter: plan.provider?.name === 'Mobimatter',
    //     stockCount: plan.stockCount,
    //     buttonDisabled: plan.provider?.name !== 'Mobimatter' && !plan.stockCount,
    //     buttonText: plan.provider?.name === 'Mobimatter' ? 'Add to Cart' : (plan.stockCount > 0 ? 'Add to Cart' : 'Out of Stock')
    // });

    return (
        <div className="container mx-auto p-6">
            <div className="mb-6 flex items-center justify-between">
                <Button
                    variant="outline"
                    onClick={() => navigate(-1)}
                >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back
                </Button>
            </div>

            <div className="grid gap-6 md:grid-cols-3">
                {/* Main Plan Information */}
                <Card className="md:col-span-2">
                    <CardHeader>
                        <div className="flex justify-between items-start">
                            <div>
                                <CardTitle className="text-2xl font-bold mb-2 ">{plan.name}</CardTitle>
                            </div>
                            <div className="text-right">
                                <div className="text-3xl font-bold text-primary">
                                    ${plan.sellingPrice}
                                </div>
                                <div className="text-sm text-gray-500 mt-1">
                                    Valid for {plan.validityDays} days
                                </div>
                            </div>
                        </div>
                    </CardHeader>

                    <CardContent className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                                <Database className="w-5 h-5 text-primary" />
                                <div>
                                    <div className="text-sm text-gray-500">Data Allowance</div>
                                    <div className="font-medium">
                                        {plan.planType === 'Unlimited' ? (
                                            'Unlimited Data'
                                        ) : plan.planType === 'Custom' ? (
                                            <span className="text-purple-600">{plan.customPlanData}</span>
                                        ) : plan.planType === 'Fixed' ? (
                                            `${plan.planData} ${plan.planDataUnit}`
                                        ) : (
                                            'Data plan not specified'
                                        )}
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                                <Clock className="w-5 h-5 text-primary" />
                                <div>
                                    <div className="text-sm text-gray-500">Validity</div>
                                    <div className="font-medium">{plan.validityDays} Day{plan.validityDays > 1 ? 's' : ''} </div>
                                </div>
                            </div>
                            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                                <PhoneCall className="w-5 h-5 text-primary" />
                                <div>
                                    <div className="text-sm text-gray-500">Calls</div>
                                    <div className="font-medium">
                                        {plan.is_voice} {plan.voiceMin && plan.voiceMin !== 0 ? `${plan.voiceMin} ${plan.voiceMinUnit || ''}` : ''}
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                                <Inbox  className="w-5 h-5 text-primary" />
                                <div>
                                    <div className="text-sm text-gray-500">SMS</div>
                                    <div className="font-medium">
                                        {plan.is_sms || 'N/A'}
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                                <MapPin className="w-5 h-5 text-primary" />
                                <div>
                                    <div className="text-sm text-gray-500">Region</div>
                                    <div className="font-medium">
                                        {plan.region ? (
                                            typeof plan.region === 'string'
                                                ? plan.region.split(',').map(r => r.trim()).join(', ')
                                                : Array.isArray(plan.region)
                                                    ? plan.region.join(', ')
                                                    : 'Global'
                                        ) : 'Global'}
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                                <Signal className="w-5 h-5 text-primary" />
                                <div>
                                    <div className="text-sm text-gray-500">Network Type</div>
                                    <div className="font-medium">{plan.networkType || '4G/5G, 4G/LTE'}</div>
                                </div>
                            </div>
                            {/* <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                                <Signal className="w-5 h-5 text-primary" />
                                <div>
                                    <div className="text-sm text-gray-500">Network Name</div>
                                    <div className="font-medium">{plan.networkName || 'N/A'}</div>
                                </div>
                            </div> */}
                            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                                <ArrowUpToLine className="w-5 h-5 text-primary" />
                                <div>
                                    <div className="text-sm text-gray-500">Top Up</div>
                                    <div className="font-medium">{plan.top_up || 'Not Available'}</div>
                                </div>
                            </div>
                            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                                <ArrowDownUp className="w-5 h-5 text-primary" />
                                <div>
                                    <div className="text-sm text-gray-500"> Profile</div>
                                    <div className="font-medium">{plan.profile || 'N/A'}</div>
                                </div>
                            </div>
                            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                                <ListChecks className="w-5 h-5 text-primary" />
                                <div>
                                    <div className="text-sm text-gray-500">Plan Category</div>
                                    <div className="font-medium">{plan.planCategory || 'N/A'}</div>
                                </div>
                            </div>
                            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                                <Wifi className="w-5 h-5 text-primary" />
                                <div>
                                    <div className="text-sm text-gray-500">Hotspot</div>
                                    <div className="font-medium">{plan.hotspot}</div>
                                </div>
                            </div>
                            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                                <Newspaper className="w-5 h-5 text-primary" />
                                <div>
                                    <div className="text-sm text-gray-500">Activation Policy</div>
                                    <div className="font-medium">{plan.activationPolicy}</div>
                                </div>
                            </div>
                            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                                <Gauge className="w-5 h-5 text-primary" />
                                <div>
                                    <div className="text-sm text-gray-500">Speed</div>
                                    <div className="font-medium">{plan.speed}</div>
                                </div>
                            </div>
                            {/* Usage Tracking - Only show for Mobimatter plans */}
                        {plan.provider?.name === 'Mobimatter' && plan.providerMetadata?.usageTracking && (
                            <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                            <Gauge className="w-5 h-5 text-primary" />
                            <div>
                                <div className="text-sm text-gray-500">Usage Tracking</div>
                                <div className="font-medium">{plan.providerMetadata.usageTracking}</div>
                            </div>
                        </div>
                        )}
                        </div>

                        <Separator />

                        {/* Coverage */}
                        <div>
                            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                                <Globe className="w-5 h-5" />
                                Coverage
                            </h3>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                                {coverageSection}
                            </div>
                        </div>

                        {/* Instructions */}
                        {plan.instructions && (
                            <div className="space-y-4 mt-6">
                                <h3 className="text-lg font-semibold flex items-center gap-2">
                                <ClipboardPenLine className="w-5 h-5" />
                                Instructions
                                </h3>
                                <div
                                    className="prose max-w-none bg-gray-50 p-4 rounded-lg"
                                    dangerouslySetInnerHTML={{ __html: plan.instructions }}
                                />
                            </div>
                        )}


                    </CardContent>
                    <CardFooter className="flex justify-end space-x-4">
                        <Button variant="outline" onClick={() => navigate(-1, { replace: true })}>
                            Back to Plans
                        </Button>
                        <Button
                            onClick={handleAddToCart}
                            disabled={!plan.provider || (
                                plan.provider.name !== 'Mobimatter' &&
                                plan.provider.name !== 'Billionconnect' &&
                                plan.provider.type !== 'API' &&
                                plan.stockCount !== 'Unlimited' &&
                                !plan.stockCount
                            )}
                        >
                            {!plan.provider ? 'Add to Cart' :
                             plan.provider.name === 'Mobimatter' ||
                             plan.provider.name === 'BillionConnect' ||
                             plan.provider.name === 'billionconnect' ||
                             plan.provider.name === 'Billionconnect' ||
                             plan.provider.type === 'API' ||
                             plan.stockCount === 'Unlimited' ? 'Add to Cart' :
                             plan.stockCount > 0 ? 'Add to Cart' : 'Out of Stock'}
                        </Button>
                    </CardFooter>
                </Card>

                {/* Side Information */}
                <div className="space-y-6">
                    {/* Purchase Card */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Purchase Plan</CardTitle>
                            <CardDescription>
                                Get instant access to mobile data
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-500">Price</span>
                                    <span className="text-lg font-bold">
                                        ${plan.sellingPrice}
                                    </span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-500">Validity</span>
                                    <span>{plan.validityDays} Day{plan.validityDays > 1 ? 's' : ''}</span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-500">Data</span>
                                    <span>
                                        {plan.planType === 'Unlimited' ? (
                                            'Unlimited Data'
                                        ) : plan.planType === 'Custom' ? (
                                            <span className="text-purple-600">{plan.customPlanData}</span>
                                        ) : plan.planType === 'Fixed' ? (
                                            `${plan.planData} ${plan.planDataUnit}`
                                        ) : (
                                            'Data plan not specified'
                                        )}
                                    </span>
                                </div>
                                <Button
                                    onClick={handleAddToCart}
                                    className="w-full"
                                    disabled={!plan.provider || (
                                        plan.provider.name !== 'Mobimatter' &&
                                        plan.provider.name !== 'Billionconnect' &&
                                        plan.provider.type !== 'API' &&
                                        plan.stockCount !== 'Unlimited' &&
                                        !plan.stockCount
                                    )}
                                >
                                    {!plan.provider ? 'Purchase Now' :
                                     plan.provider.name === 'Mobimatter' ||
                                     plan.provider.name === 'Billionconnect' ||
                                     plan.provider.type === 'API' ||
                                     plan.stockCount === 'Unlimited' ? 'Purchase Now' :
                                     plan.stockCount > 0 ? 'Purchase Now' : 'Out of Stock'}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Additional Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Additional Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-2">
                                <div className="w-full">
                                    <div className="text-lg text-black-500 font-bold flex items-center gap-2">
                                        <ScrollText className="w-5 h-5" />
                                        Description</div>
                                    <div
                                        className="prose prose-sm max-w-none mt-2 whitespace-pre-wrap"
                                        dangerouslySetInnerHTML={{
                                            __html: plan.provider?.name === 'Mobimatter' ? null : (plan.description || plan.providerMetadata?.productDetails?.find(detail => detail.name === "heading")?.value)
                                        }}
                                    />
                                    {/* Display planInfo if available */}
                                    {plan.planInfo && (
                                        <div
                                            className="prose prose-sm max-w-none mt-4"
                                            dangerouslySetInnerHTML={{ __html: plan.planInfo }}
                                        />
                                    )}
                                    {/* If no planInfo, fall back to metadata extraction (for backward compatibility) */}
                                    {!plan.planInfo && plan.providerMetadata?.customData && (() => {
                                        const planDetails = plan.providerMetadata.customData.find(d => d.name.trim() === 'PLAN_DETAILS');
                                        if (planDetails?.value) {
                                            try {
                                                const parsed = JSON.parse(planDetails.value);
                                                return (
                                                    <div className={plan.provider?.name !== 'Mobimatter' ? "mt-4" : ""}>
                                                        {parsed.description && plan.provider?.name === 'Mobimatter' && (
                                                            <div
                                                                className="mb-4"
                                                                dangerouslySetInnerHTML={{ __html: parsed.description }}
                                                            />
                                                        )}
                                                        {parsed.items?.length > 0 && (
                                                            <>
                                                                <h4 className="text-sm font-semibold mb-2">Key Features:</h4>
                                                                <ul className="list-disc list-inside space-y-1">
                                                                    {parsed.items.map((item, index) => (
                                                                        <li
                                                                            key={index}
                                                                            className="text-sm"
                                                                            dangerouslySetInnerHTML={{ __html: item }}
                                                                        />
                                                                    ))}
                                                                </ul>
                                                            </>
                                                        )}
                                                    </div>
                                                );
                                            } catch (e) {
                                                console.error('Error parsing items from customData:', e);
                                                return null;
                                            }
                                        }
                                        return null;
                                    })()}

                                    {/* Display additionalInfo if available */}
                                    {/* {plan.additionalInfo && (
                                        <div
                                            className="prose prose-sm max-w-none mt-4 bg-gray-50 p-4 rounded-lg"
                                            dangerouslySetInnerHTML={{ __html: plan.additionalInfo }}
                                        />
                                    )} */}
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
};

export default PlanDetails;
