const sequelize = require('../config/database');

async function simpleMarkupFix() {
    try {
        // First update any NULL values to 0
        await sequelize.query(`
            UPDATE Users 
            SET markupPercentage = 0 
            WHERE markupPercentage IS NULL OR markupPercentage = ''
        `);
        console.log('Updated NULL values to 0');

        // Then modify the column directly
        await sequelize.query(`
            ALTER TABLE Users 
            MODIFY markupPercentage DECIMAL(5,2) NOT NULL DEFAULT 0
        `);
        console.log('Modified column to NOT NULL with default 0');

        process.exit(0);
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}

simpleMarkupFix();
