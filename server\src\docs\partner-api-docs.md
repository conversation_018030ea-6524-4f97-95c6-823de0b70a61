# Partner API Documentation

This API allows authorized partners to fetch product data, place and track orders, and view usage information.

## API Information

- **Base URL (Development):** `http://localhost:5000/api/v1`
- **Base URL (Production):** `https://partner-api.your-domain.com/api/v1`
- **API Version:** v1
- **Response Format:** All responses are in JSON

## Authentication

All API requests require the following HTTP headers:

- `Authorization: Bearer {API_KEY}`
- `X-Partner-ID: {partner_id}`

You can obtain your API key and Partner ID from the Partner Portal.

## Error Codes

| Code | Description |
|------|-------------|
| 401 Unauthorized | Invalid API key or partner ID |
| 404 Not Found | Resource not found |
| 500 Internal Server Error | General server error |

## Endpoints

### 1. Get All Products

Returns a list of all available products.

**Request:**
```
GET /products
```

**Headers:**
```
Authorization: Bearer {API_KEY}
X-Partner-ID: {partner_id}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "productId": "ABCDEF123456",
        "name": "Europe 5GB / 30 Days",
        "description": "5GB data valid for 30 days across Europe",
        "instructions": null,
        "price": 29.99,
        "validityDays": 30,
        "countries": ["FR", "DE", "IT", "ES", "NL"],
        "region": ["Europe", "Asia"],
        "dataAmount": 5,
        "dataUnit": "GB",
        "customPlanData": "2gb 460 kb/sec",
        "voiceMin": 50,
        "voiceMinUnit": "Min",
        "speed": "Unrestricted",
        "planType": "Fixed",
        "category": "esim_realtime",
        "networkType": "4G/LTE",
        "isVoiceAvailable": true,
        "isSmsAvailable": false,
        "hotspotAvailable": true,
        "profile": "local",
        "activationPolicy": "Activation upon purchase",
        "startDateEnabled": false,
        "features": ["5G Support", "Unlimited Calls"]
      }
    ]
  }
}
```

**Example Request:**
```bash
curl -X GET "https://partner-api.your-domain.com/api/v1/products" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id"
```

### 2. Get Order Details

Retrieve order details using order ID.

**Request:**
```
GET /order/{orderId}
```

**Headers:**
```
Authorization: Bearer {API_KEY}
X-Partner-ID: {partner_id}
```

**Path Parameters:**
- `orderId` (string): The ID of the order to retrieve

**Response:**
```json
{
  "success": true,
  "data": {
    "orderId": "VLZ123456",
    "status": "completed",
    "product": {
      "productId": "ABCDEF123456",
      "name": "Europe 5GB / 30 Days"
    },
    "orderTotal": 29.99,
    "quantity": 1,
    "startDate": "2023-11-01",
    "expiryDate": "2023-12-01",
    "iccid": "8991000123456789012",
    "smdpAddress": "trl.prod.ondemandconnectivity.com",
    "accessPointName": "mbb",
    "lpaString": "LPA:1$trl.prod.ondemandconnectivity.com$AAA22",
    "activationCode": "LPA:1$smdp.example.com$123456789-abcdef-123456",
    "status": "completed",
    "top_up": "Available",
    "qrCodeUrl": "https://example.com/qr/VLZ123456.png",
    "createdAt": "2023-10-25T14:30:45Z"
  }
}
```

**Example Request:**
```bash
curl -X GET "https://partner-api.your-domain.com/api/v1/order/VLZ123456" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id"
```

### 3. Place a New Order

Places a new order for an eSIM product.

**Request:**
```
POST /order
```

**Headers:**
```
Authorization: Bearer {API_KEY}
X-Partner-ID: {partner_id}
Content-Type: application/json
```

**Request Body:**
```json
{
  "productId": "ABCDEF123456",
  "startDate": "2023-11-01",
  "category": "esim_realtime"
}
```

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| productId | string | Yes | The ID of the product to order |
| startDate | string (date) | Conditional | Required if the plan has startDateEnabled=true |
| category | string | No | Optional category |

**Response:**
```json
{
  "success": true,
  "data": {
    "orderId": "VLZ123456",
    "status": "completed",
    "qrCodeUrl": "https://example.com/qr/VLZ123456.png",
    "iccid": "8991000123456789012",
    "smdpAddress": "trl.prod.ondemandconnectivity.com",
    "accessPointName": "mbb",
    "lpaString": "LPA:1$trl.prod.ondemandconnectivity.com$AAA22",
    "activationCode": "LPA:1$smdp.example.com$123456789-abcdef-123456",
    "expiryDate": "2023-12-01",
    "orderTotal": 29.99,
    "product": {
      "productId": "ABCDEF123456",
      "name": "Europe 5GB / 30 Days"
    }
  }
}
```

**Example Request:**
```bash
curl -X POST "https://partner-api.your-domain.com/api/v1/order" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id" \
  -H "Content-Type: application/json" \
  -d '{
    "productId": "ABCDEF123456",
    "startDate": "2023-11-01"
  }'
```

### 4. Get Usage Data

Returns usage details for a specific order.

**Request:**
```
GET /usage/{orderId}
```

**Headers:**
```
Authorization: Bearer {API_KEY}
X-Partner-ID: {partner_id}
```

**Path Parameters:**
- `orderId` (string): The ID of the order to retrieve usage for

**Response:**
```json
{
  "success": true,
  "data": {
    "orderId": "VLZ123456",
    "totalData": 5368709120,
    "usedData": 2147483648,
    "remainingData": 3221225472,
    "status": "Active",
    "activationDate": "2023-11-01T00:00:00Z",
    "expiryDate": "2023-12-01T00:00:00Z",
    "lastUpdated": "2023-11-15T09:45:22Z",
    "usageMessage": "The operator does not yet support package status check, please refer to your device settings to check your data usage"
  }
}
```

**Example Request:**
```bash
curl -X GET "https://partner-api.your-domain.com/api/v1/usage/VLZ123456" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id"
```

## Data Models

### Product

| Field | Type | Description |
|-------|------|-------------|
| productId | string | Unique identifier for the product |
| name | string | Product name |
| description | string | Detailed product description |
| instructions | string | Optional instructions for using the product |
| price | number | Product price in USD |
| validityDays | integer | Number of days the product is valid for |
| countries | array of strings | List of country codes where the product is valid |
| region | array of strings | Regions where the product is valid |
| dataAmount | number | Amount of data included (if planType is 'Fixed') |
| dataUnit | string | Unit of data measurement (if planType is 'Fixed') |
| customPlanData | string | Custom data plan description (if applicable) |
| voiceMin | integer | Voice minutes included (if applicable) |
| voiceMinUnit | string | Unit for voice minutes (if applicable) |
| speed | string | Data speed description |
| planType | string | Type of plan (Fixed, Unlimited, or Custom) |
| category | string | Product category |
| networkType | string | Network type supported |
| isVoiceAvailable | boolean | Whether voice calls are available |
| isSmsAvailable | boolean | Whether SMS is available |
| hotspotAvailable | boolean | Whether hotspot functionality is available |
| profile | string | Profile type |
| activationPolicy | string | When the eSIM is activated |
| startDateEnabled | boolean | Whether custom activation date is supported |
| features | array of strings | Additional features of the product |

### Order

| Field | Type | Description |
|-------|------|-------------|
| orderId | string | Unique identifier for the order |
| status | string | Order status |
| product | object | Contains productId and name of the ordered product |
| orderTotal | number | Total amount charged for the order |
| quantity | integer | Number of items in the order |
| startDate | string (date) | Start date for the eSIM (if applicable) |
| expiryDate | string (date) | Expiry date for the eSIM |
| iccid | string | ICCID of the eSIM |
| smdpAddress | string | SMDP address for eSIM installation |
| accessPointName | string | Access point name for the eSIM |
| lpaString | string | LPA string for eSIM installation |
| activationCode | string | Activation code for the eSIM |
| qrCodeUrl | string | URL to the QR code for easy eSIM installation |
| createdAt | string (date-time) | Date and time when the order was created |
| top_up | string | Whether top-up is available for this eSIM |

### Usage

| Field | Type | Description |
|-------|------|-------------|
| orderId | string | Unique identifier for the order |
| totalData | integer | Total data allowance in bytes |
| usedData | integer | Used data in bytes |
| remainingData | integer | Remaining data in bytes |
| status | string | Usage status |
| activationDate | string (date-time) | Date and time when the eSIM was activated |
| expiryDate | string (date-time) | Date and time when the eSIM expires |
| lastUpdated | string (date-time) | Date and time when the usage data was last updated |
| usageMessage | string | Message about usage data availability |

## Notes

- All data sizes (totalData, usedData, remainingData) are provided in bytes.
- The activation and expiry dates are in ISO 8601 format.
- For security reasons, only orders created by the authenticated partner can be accessed.
- The API is rate-limited to prevent abuse. 