#!/bin/bash

# Script to set up SSL certificates after deployment
# Usage: ./setup-ssl.sh <domain-name> <email>

set -e  # Exit on any error

# Check if domain name is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <domain-name> <email>"
  echo "Example: $0 api.yourdomain.com <EMAIL>"
  exit 1
fi

# Check if email is provided
if [ -z "$2" ]; then
  echo "Usage: $0 <domain-name> <email>"
  echo "Example: $0 api.yourdomain.com <EMAIL>"
  exit 1
fi

DOMAIN=$1
EMAIL=$2

echo "Setting up SSL certificates for $DOMAIN..."

# Navigate to project directory
cd ~/esim-project

# Check if containers are running
if ! docker ps | grep -q nginx; then
  echo "❌ Nginx container is not running. Please deploy the application first."
  exit 1
fi

# Create required directories
mkdir -p nginx/certbot/conf
mkdir -p nginx/certbot/www

# Check if we're using custom Dockerfile.nginx
if [ -f "Dockerfile.nginx" ]; then
    echo "✅ Using custom Nginx Dockerfile - configuration is already set up"
    echo "Verifying ACME challenge configuration..."

    # Test if ACME challenge endpoint is accessible
    if docker exec nginx test -f /var/www/certbot/.well-known/acme-challenge/test-file; then
        echo "✅ ACME challenge directory is accessible"
    else
        echo "❌ ACME challenge directory not found. Rebuilding Nginx container..."
        docker-compose build nginx
        docker-compose up -d nginx
        sleep 10
    fi
else
    # Fallback: Ensure Nginx is configured for the ACME challenge
    echo "Setting up Nginx for ACME challenge..."
    mkdir -p nginx/conf
    cat > nginx/conf/app.conf << EOL
server {
    listen 80;
    server_name ${DOMAIN};

    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files \$uri =404;
    }

    location / {
        proxy_pass http://app:3000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOL

    # Reload Nginx to apply the configuration
    docker exec nginx nginx -s reload || docker-compose restart nginx
    sleep 5
fi

# Request SSL certificate
echo "Requesting SSL certificate from Let's Encrypt..."

# Check if certbot is installed on host, if not install it
if ! command -v certbot &> /dev/null; then
    echo "Installing certbot on host system..."
    sudo apt update && sudo apt install certbot -y
fi

# Get the Docker volume path for certbot webroot
VOLUME_PATH=$(docker volume inspect esim-project_certbot_webroot --format '{{ .Mountpoint }}' 2>/dev/null)

if [ -n "$VOLUME_PATH" ] && sudo [ -d "$VOLUME_PATH" ]; then
    echo "Using Docker volume path: $VOLUME_PATH"
    # Use host-based certbot with the actual Docker volume path
    sudo certbot certonly --webroot --webroot-path="$VOLUME_PATH" \
      --email "$EMAIL" --agree-tos --no-eff-email \
      -d "$DOMAIN" --verbose
else
    echo "Docker volume not found, using container-based approach..."
    # Fallback to container-based approach
    docker-compose run --rm certbot certonly --webroot --webroot-path=/var/www/certbot \
      --email "$EMAIL" --agree-tos --no-eff-email \
      -d "$DOMAIN" --verbose
fi

if [ $? -ne 0 ]; then
  echo "❌ Failed to obtain SSL certificate. Please check the logs."
  echo "You can try again later with: ./setup-ssl.sh $DOMAIN $EMAIL"
  exit 1
fi

echo "✅ SSL certificate obtained successfully!"

# Copy certificates to Docker volume location (always do this after successful certificate generation)
echo "Copying certificates to Docker volume location..."
sudo mkdir -p nginx/certbot/conf/live nginx/certbot/conf/archive nginx/certbot/conf/renewal

# Wait a moment for filesystem to sync
sleep 2

# Copy the specific domain certificates with verbose output
echo "Checking certificate locations..."
sudo ls -la /etc/letsencrypt/live/ 2>/dev/null || echo "Live directory not accessible"
sudo ls -la /etc/letsencrypt/archive/ 2>/dev/null || echo "Archive directory not accessible"

if sudo [ -d "/etc/letsencrypt/live/$DOMAIN" ]; then
    echo "Found live certificates for $DOMAIN, copying..."
    sudo cp -r /etc/letsencrypt/live/$DOMAIN nginx/certbot/conf/live/
    echo "✅ Live certificates copied"
else
    echo "❌ Live certificates not found for $DOMAIN"
    sudo ls -la /etc/letsencrypt/live/ 2>/dev/null
fi

if sudo [ -d "/etc/letsencrypt/archive/$DOMAIN" ]; then
    echo "Found archive certificates for $DOMAIN, copying..."
    sudo cp -r /etc/letsencrypt/archive/$DOMAIN nginx/certbot/conf/archive/
    echo "✅ Archive certificates copied"
else
    echo "❌ Archive certificates not found for $DOMAIN"
    sudo ls -la /etc/letsencrypt/archive/ 2>/dev/null
fi

if sudo [ -f "/etc/letsencrypt/renewal/$DOMAIN.conf" ]; then
    echo "Found renewal configuration for $DOMAIN, copying..."
    sudo cp /etc/letsencrypt/renewal/$DOMAIN.conf nginx/certbot/conf/renewal/
    echo "✅ Renewal configuration copied"
else
    echo "❌ Renewal configuration not found for $DOMAIN"
    sudo ls -la /etc/letsencrypt/renewal/ 2>/dev/null
fi

sudo chown -R $USER:$USER nginx/certbot/conf/
echo "✅ Certificate ownership fixed"

# Verify the copy worked
echo "Verifying copied certificates:"
ls -la nginx/certbot/conf/live/ 2>/dev/null || echo "Live directory empty"
ls -la nginx/certbot/conf/archive/ 2>/dev/null || echo "Archive directory empty"
ls -la nginx/certbot/conf/renewal/ 2>/dev/null || echo "Renewal directory empty"

# Create SSL Nginx configuration
if [ -f "Dockerfile.nginx" ]; then
    echo "Creating SSL-enabled Nginx Dockerfile..."

    # Create a new Dockerfile with SSL configuration
    cat > Dockerfile.nginx << EOL
FROM nginx:1.21-alpine

# Remove default Nginx configuration
RUN rm /etc/nginx/conf.d/default.conf

# Create SSL-enabled configuration
RUN echo 'server { \\
    listen 80; \\
    server_name ${DOMAIN}; \\
    \\
    # For Let'"'"'s Encrypt HTTP-01 challenge \\
    location ^~ /.well-known/acme-challenge/ { \\
        root /var/www/certbot; \\
        try_files \$uri =404; \\
    } \\
    \\
    # Redirect to HTTPS \\
    location / { \\
        return 301 https://\$host\$request_uri; \\
    } \\
} \\
\\
server { \\
    listen 443 ssl http2; \\
    server_name ${DOMAIN}; \\
    \\
    ssl_certificate /etc/letsencrypt/live/${DOMAIN}/fullchain.pem; \\
    ssl_certificate_key /etc/letsencrypt/live/${DOMAIN}/privkey.pem; \\
    \\
    # SSL parameters \\
    ssl_protocols TLSv1.2 TLSv1.3; \\
    ssl_prefer_server_ciphers on; \\
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384; \\
    ssl_session_timeout 1d; \\
    ssl_session_cache shared:SSL:10m; \\
    ssl_session_tickets off; \\
    \\
    # Security headers \\
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always; \\
    add_header X-Content-Type-Options nosniff; \\
    add_header X-Frame-Options SAMEORIGIN; \\
    add_header X-XSS-Protection "1; mode=block"; \\
    \\
    # Proxy to Node.js application \\
    location / { \\
        proxy_pass http://app:3000; \\
        proxy_http_version 1.1; \\
        proxy_set_header Upgrade \$http_upgrade; \\
        proxy_set_header Connection '"'"'upgrade'"'"'; \\
        proxy_set_header Host \$host; \\
        proxy_set_header X-Real-IP \$remote_addr; \\
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for; \\
        proxy_set_header X-Forwarded-Proto \$scheme; \\
        proxy_cache_bypass \$http_upgrade; \\
    } \\
}' > /etc/nginx/conf.d/app.conf

# Create ACME challenge directory
RUN mkdir -p /var/www/certbot/.well-known/acme-challenge/

# Set proper permissions
RUN chmod -R 755 /var/www/certbot

# Expose ports
EXPOSE 80 443

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]
EOL

    echo "Rebuilding Nginx container with SSL configuration..."
    docker-compose build nginx
    docker-compose up -d nginx

else
    echo "Creating SSL Nginx configuration..."
    cat > nginx/conf/app.conf << EOL
server {
    listen 80;
    server_name ${DOMAIN};

    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files \$uri =404;
    }

    location / {
        return 301 https://\$host\$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name ${DOMAIN};

    ssl_certificate /etc/letsencrypt/live/${DOMAIN}/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/${DOMAIN}/privkey.pem;

    # SSL parameters
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";

    # Proxy to Node.js application
    location / {
        proxy_pass http://app:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOL
fi

# Reload Nginx to apply the new configuration
echo "Reloading Nginx to apply SSL configuration..."
docker exec nginx nginx -s reload

echo "✅ SSL setup completed successfully!"
echo "Your application is now accessible at:"
echo "  HTTPS: https://${DOMAIN}"

# Set up automatic renewal
echo "Setting up automatic SSL renewal..."
cat > ~/renew-ssl.sh << EOL
#!/bin/bash
cd ~/esim-project
docker-compose run --rm certbot renew
docker exec nginx nginx -s reload
EOL

chmod +x ~/renew-ssl.sh

# Add to crontab to run twice daily (standard for Let's Encrypt)
(crontab -l 2>/dev/null | grep -v "renew-ssl.sh"; echo "0 0,12 * * * ~/renew-ssl.sh > ~/ssl-renewal.log 2>&1") | crontab -

echo "✅ Automatic SSL renewal configured to run twice daily"
