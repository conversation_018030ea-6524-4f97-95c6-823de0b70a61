const { Country } = require('../models');
const { Op } = require('sequelize');

/**
 * Maps a list of country ISO codes to their regions and determines the coverage region
 * @param {string[]} isoCodes - Array of country ISO codes (e.g., ["IN", "FR", "US"])
 * @returns {Promise<string>} - Returns a string representing the coverage region (e.g., "Asia", "Asia, Europe", or "Global")
 */
async function mapCountriesToRegion(isoCodes) {
    try {
        if (!Array.isArray(isoCodes) || isoCodes.length === 0) {
            return 'Global';
        }

        // Get all regions for the provided ISO codes
        const countries = await Country.findAll({
            attributes: ['region'],
            where: {
                id: {
                    [Op.in]: isoCodes.map(code => code.toUpperCase())
                },
                region: {
                    [Op.not]: null
                }
            },
            raw: true
        });

        if (!countries.length) {
            return 'Global';
        }

        // Extract unique regions
        const uniqueRegions = [...new Set(countries.map(country => country.region))];

        // Get total count of defined regions in the system
        const totalRegionsCount = await Country.count({
            distinct: true,
            col: 'region',
            where: {
                region: {
                    [Op.not]: null
                }
            }
        });

        // If all defined regions are present, return "Global"
        if (uniqueRegions.length === totalRegionsCount) {
            return 'Global';
        }

        // If only one region, return that region
        if (uniqueRegions.length === 1) {
            return uniqueRegions[0];
        }

        // Otherwise, return comma-separated list of regions
        return uniqueRegions.sort().join(', ');
    } catch (error) {
        console.error('Error mapping countries to region:', error);
        return 'Global'; // Default to Global in case of error
    }
}

module.exports = mapCountriesToRegion; 