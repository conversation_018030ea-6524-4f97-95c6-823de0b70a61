describe('Reset Password Flow', () => {
  beforeEach(() => {
    // Mock a valid reset token
    cy.visit('/reset-password?token=valid-reset-token');
  });

  it('should display reset password form', () => {
    cy.get('form').within(() => {
      cy.get('input[name="password"]').should('exist');
      cy.get('input[name="confirmPassword"]').should('exist');
      cy.get('button[type="submit"]').should('exist');
    });
  });

  it('should validate password requirements', () => {
    cy.get('input[name="password"]').type('12345');
    cy.get('input[name="confirmPassword"]').type('12345');
    cy.get('button[type="submit"]').click();

    cy.contains('Password must be at least 6 characters').should('be.visible');
  });

  it('should validate password match', () => {
    cy.get('input[name="password"]').type('password123');
    cy.get('input[name="confirmPassword"]').type('password456');
    cy.get('button[type="submit"]').click();

    cy.contains('Passwords must match').should('be.visible');
  });

  it('should handle successful password reset', () => {
    cy.intercept('POST', '/api/auth/reset-password', {
      statusCode: 200,
      body: { message: 'Password reset successful' }
    }).as('resetPassword');

    cy.get('input[name="password"]').type('newpassword123');
    cy.get('input[name="confirmPassword"]').type('newpassword123');
    cy.get('button[type="submit"]').click();

    cy.wait('@resetPassword');
    cy.contains('Password reset successful').should('be.visible');
    cy.url().should('include', '/login');
  });

  it('should handle invalid or expired token', () => {
    cy.visit('/reset-password?token=invalid-token');

    cy.intercept('POST', '/api/auth/reset-password', {
      statusCode: 400,
      body: { message: 'Invalid or expired reset token' }
    }).as('resetPasswordError');

    cy.get('input[name="password"]').type('newpassword123');
    cy.get('input[name="confirmPassword"]').type('newpassword123');
    cy.get('button[type="submit"]').click();

    cy.wait('@resetPasswordError');
    cy.contains('Invalid or expired reset token').should('be.visible');
  });

  it('should handle missing token', () => {
    cy.visit('/reset-password');
    cy.contains('Invalid reset link').should('be.visible');
    cy.get('a').contains('Return to login').should('be.visible');
  });

  it('should navigate back to login', () => {
    cy.get('a').contains('Return to login').click();
    cy.url().should('include', '/login');
  });
});
