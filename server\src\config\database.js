const { Sequelize } = require('sequelize');
require('dotenv').config();

// Configure connection pool based on environment
const getPoolConfig = () => {
    if (process.env.NODE_ENV === 'test') {
        return {
            max: 200, // Maximum number of connection in pool
            min: 50, // Minimum number of connection in pool
            acquire: 120000, // Maximum time, in milliseconds, that pool will try to get connection
            idle: 60000, // Maximum time, in milliseconds, that a connection can be idle
            evict: 30000, // Time interval in milliseconds to check for idle connections
            validate: true // Validate connection before using it
        };
    }
    return {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
    };
};

const sequelize = new Sequelize(
    process.env.DB_NAME || 'esim_demo',
    process.env.DB_USER || 'root',
    process.env.DB_PASSWORD || '',
    {
        host: process.env.DB_HOST || 'localhost',
        dialect: 'mysql',
        logging: process.env.NODE_ENV === 'development' ? console.log : false,
        pool: getPoolConfig(),
        define: {
            // Use underscored naming convention
            underscored: false,
            // Don't add timestamps
            timestamps: true,
            // Force table names to match model names exactly
            freezeTableName: false,
            // Disable automatic pluralization of table names
            pluralize: false
        },
        // Make table names case-insensitive on Windows
        dialectOptions: {
            insecureAuth: true,
            // Make SQL case-insensitive on Windows
            charset: 'utf8mb4',
            // Removed the collate option to avoid the warning
        }
    }
);

module.exports = sequelize;
