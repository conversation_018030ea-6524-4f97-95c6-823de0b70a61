import React, { useState, useEffect } from 'react';
import axios from '@/lib/axios';
import { Plus, Search, FileText, Folder, Edit, Trash2, Eye, Archive } from 'lucide-react';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from '@/components/ui/dialog';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Card } from '@/components/ui/card';

const CATEGORIES = [
    { value: 'general', label: 'General' },
    { value: 'technical', label: 'Technical' },
    { value: 'billing', label: 'Billing' },
    { value: 'support', label: 'Support' },
    { value: 'faq', label: 'FAQ' }
];

const VISIBILITY_OPTIONS = [
    { value: 'public', label: 'Public' },
    { value: 'partner', label: 'Partner Only' },
    { value: 'admin', label: 'Admin Only' }
];

const STATUS_OPTIONS = [
    { value: 'draft', label: 'Draft' },
    { value: 'published', label: 'Published' },
    { value: 'archived', label: 'Archived' }
];

const STATUSES = [
    { value: 'published', label: 'Published' },
    { value: 'draft', label: 'Draft' },
    { value: 'archived', label: 'Archived' }
];

export default function KnowledgeBase() {
    const { toast } = useToast();
    const [articles, setArticles] = useState([]);
    const [totalPages, setTotalPages] = useState(1);
    const [currentPage, setCurrentPage] = useState(1);
    const [search, setSearch] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [selectedStatus, setSelectedStatus] = useState('all');
    const [isLoading, setIsLoading] = useState(false);
    const [showDialog, setShowDialog] = useState(false);
    const [editingArticle, setEditingArticle] = useState(null);
    const [formData, setFormData] = useState({
        title: '',
        content: '',
        category: 'general',
        visibility: 'public',
        status: 'draft'
    });

    const fetchArticles = async () => {
        try {
            setIsLoading(true);
            const params = new URLSearchParams({
                page: currentPage,
                limit: 9,
                ...(search && { search }),
                ...(selectedCategory !== 'all' && { category: selectedCategory }),
                ...(selectedStatus !== 'all' && { status: selectedStatus })
            });

            const response = await axios.get(`/api/knowledge-base?${params}`);
            setArticles(response.data.articles);
            setTotalPages(response.data.totalPages);
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to fetch articles",
                variant: "destructive"
            });
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchArticles();
    }, [currentPage, search, selectedCategory, selectedStatus]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            if (editingArticle) {
                await axios.put(`/api/knowledge-base/${editingArticle.id}`, formData);
                toast({
                    title: "Success",
                    description: "Article updated successfully"
                });
            } else {
                await axios.post('/api/knowledge-base', formData);
                toast({
                    title: "Success",
                    description: "Article created successfully"
                });
            }
            setShowDialog(false);
            setEditingArticle(null);
            resetForm();
            fetchArticles();
        } catch (error) {
            toast({
                title: "Error",
                description: error.response?.data?.message || "Operation failed",
                variant: "destructive"
            });
        }
    };

    const handleEdit = (article) => {
        setEditingArticle(article);
        setFormData({
            title: article.title,
            content: article.content,
            category: article.category,
            visibility: article.visibility,
            status: article.status
        });
        setShowDialog(true);
    };

    const handleDelete = async (id) => {
        if (!window.confirm('Are you sure you want to delete this article?')) return;
        
        try {
            await axios.delete(`/api/knowledge-base/${id}`);
            toast({
                title: "Success",
                description: "Article deleted successfully"
            });
            fetchArticles();
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to delete article",
                variant: "destructive"
            });
        }
    };

    const resetForm = () => {
        setFormData({
            title: '',
            content: '',
            category: 'general',
            visibility: 'public',
            status: 'draft'
        });
    };

    const handleNewArticle = () => {
        setEditingArticle(null);
        resetForm();
        setShowDialog(true);
    };

    const getCategoryLabel = (category) => {
        const cat = CATEGORIES.find((cat) => cat.value === category);
        return cat ? cat.label : 'Unknown';
    };

    const getStatusVariant = (status) => {
        switch (status) {
            case 'published':
                return 'default';
            case 'draft':
                return 'outline';
            case 'archived':
                return 'secondary';
            default:
                return 'default';
        }
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'published':
                return 'text-green-600 bg-green-50 hover:bg-green-100';
            case 'draft':
                return 'text-gray-600 bg-gray-50 hover:bg-gray-100';
            case 'archived':
                return 'text-red-600 bg-red-50 hover:bg-red-100';
            default:
                return '';
        }
    };

    return (
        <div className="container mx-auto p-4 sm:p-6">
            <div className="mb-6 sm:mb-8">
                <div className="bg-gradient-to-r from-blue-800 to-blue-600 text-white p-6 rounded-t-lg">
                    <h1 className="text-xl sm:text-2xl font-bold">Knowledge Base</h1>
                    <p className="text-white/80 mt-1">Manage your help articles and documentation</p>
                </div>
            </div>

            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center w-full sm:w-auto">
                    <div className="flex flex-col xs:flex-row gap-4 w-full sm:w-auto">
                        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                            <SelectTrigger className="w-full xs:w-[180px]">
                                <SelectValue placeholder="All Categories" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Categories</SelectItem>
                                {CATEGORIES.map(cat => (
                                    <SelectItem key={cat.value} value={cat.value}>
                                        {cat.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                            <SelectTrigger className="w-full xs:w-[180px]">
                                <SelectValue placeholder="All Statuses" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Statuses</SelectItem>
                                {STATUSES.map(status => (
                                    <SelectItem key={status.value} value={status.value}>
                                        {status.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="w-full sm:w-auto">
                        <Input
                            type="search"
                            placeholder="Search articles..."
                            value={search}
                            onChange={(e) => setSearch(e.target.value)}
                            className="w-full sm:w-[300px]"
                        />
                    </div>
                </div>
                <Button onClick={handleNewArticle} className="flex items-center gap-2 w-full sm:w-auto justify-center bg-blue-700 text-white">
                    <Plus className="w-4 h-4" />
                    New Article
                </Button>
            </div>

            {isLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    {[...Array(6)].map((_, index) => (
                        <Card key={index} className="p-6">
                            <div className="flex items-start gap-4">
                                <div className="bg-blue-500/10 p-3 rounded-lg">
                                    <div className="w-6 h-6 bg-blue-200 rounded animate-pulse" />
                                </div>
                                <div className="flex-1 space-y-3">
                                    <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse" />
                                    <div className="space-y-2">
                                        <div className="h-3 bg-gray-200 rounded animate-pulse" />
                                        <div className="h-3 bg-gray-200 rounded w-5/6 animate-pulse" />
                                    </div>
                                </div>
                            </div>
                        </Card>
                    ))}
                </div>
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-8">
                    {articles.length === 0 ? (
                        <div className="col-span-full text-center py-12">
                            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900">No articles found</h3>
                            <p className="mt-2 text-gray-500">Get started by creating a new article.</p>
                            <Button onClick={handleNewArticle} className="mt-4 bg-blue-700 text-white">
                                <Plus className="w-4 h-4 mr-2" />
                                Create Article
                            </Button>
                        </div>
                    ) : (
                        articles.map((article) => (
                            <Card key={article.id} className="p-4 sm:p-6 hover:shadow-lg transition-shadow">
                                <div className="flex items-start gap-4">
                                    <div className="bg-blue-500/10 p-3 rounded-lg shrink-0">
                                        <FileText className="w-6 h-6 text-blue-500" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <h3 className="font-semibold text-base sm:text-lg truncate">{article.title}</h3>
                                        <p className="text-sm text-gray-600 mt-1 line-clamp-3 whitespace-pre-wrap">
                                            {article.content.substring(0, 100)}
                                            {article.content.length > 100 && '...'}
                                        </p>
                                        <div className="flex flex-wrap items-center gap-2 mt-2">
                                            <span className="text-xs sm:text-sm text-gray-500">
                                                {getCategoryLabel(article.category)}
                                            </span>
                                            <span className="text-xs sm:text-sm text-gray-500">•</span>
                                            <Badge 
                                                variant={getStatusVariant(article.status)}
                                                className={`${getStatusColor(article.status)} capitalize text-xs`}
                                            >
                                                {article.status}
                                            </Badge>
                                        </div>
                                        <div className="flex gap-2 mt-4">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleEdit(article)}
                                                className="flex-1 sm:flex-none"
                                            >
                                                <Edit className="w-4 h-4 sm:mr-2" />
                                                <span className="hidden sm:inline">Edit</span>
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="flex-1 sm:flex-none text-red-600 hover:text-red-700"
                                                onClick={() => handleDelete(article.id)}
                                            >
                                                <Trash2 className="w-4 h-4 sm:mr-2" />
                                                <span className="hidden sm:inline">Delete</span>
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </Card>
                        ))
                    )}
                </div>
            )}

            {totalPages > 1 && (
                <div className="flex justify-center gap-2 mt-6 flex-wrap">
                    {[...Array(totalPages)].map((_, i) => (
                        <Button
                            key={i}
                            variant={currentPage === i + 1 ? "default" : "outline"}
                            size="sm"
                            onClick={() => setCurrentPage(i + 1)}
                            className="min-w-[40px]"
                        >
                            {i + 1}
                        </Button>
                    ))}
                </div>
            )}

            <Dialog open={showDialog} onOpenChange={setShowDialog}>
                <DialogContent className="max-w-[95vw] sm:max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>
                            {editingArticle ? 'Edit Article' : 'New Article'}
                        </DialogTitle>
                        <DialogDescription>
                            Fill out the details below to {editingArticle ? 'edit' : 'create'} an article.
                        </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div>
                            <Input
                                placeholder="Article Title"
                                value={formData.title}
                                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                                required
                            />
                        </div>
                        <div>
                            <Textarea
                                placeholder="Article Content"
                                value={formData.content}
                                onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                                required
                                className="min-h-[200px] font-mono text-sm"
                                style={{ whiteSpace: 'pre-wrap' }}
                            />
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                            <Select
                                value={formData.category}
                                onValueChange={(value) => setFormData({ ...formData, category: value })}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Category" />
                                </SelectTrigger>
                                <SelectContent>
                                    {CATEGORIES.map(cat => (
                                        <SelectItem key={cat.value} value={cat.value}>
                                            {cat.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <Select
                                value={formData.visibility}
                                onValueChange={(value) => setFormData({ ...formData, visibility: value })}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Visibility" />
                                </SelectTrigger>
                                <SelectContent>
                                    {VISIBILITY_OPTIONS.map(opt => (
                                        <SelectItem key={opt.value} value={opt.value}>
                                            {opt.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                            <Select
                                value={formData.status}
                                onValueChange={(value) => setFormData({ ...formData, status: value })}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    {STATUS_OPTIONS.map(opt => (
                                        <SelectItem key={opt.value} value={opt.value}>
                                            {opt.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <DialogFooter>
                            <Button type="submit" className="w-full sm:w-auto">
                                {editingArticle ? 'Update Article' : 'Create Article'}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
}
