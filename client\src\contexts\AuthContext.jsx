import { createContext, useContext, useState, useEffect } from 'react';
import api from '../lib/axios';
import PropTypes from 'prop-types';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);

    // Using centralized axios instance from lib/axios.js
    // This ensures consistent error handling and interceptors

    const verifyToken = async (token) => {
        try {
            const response = await api.get('/api/auth/verify', {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
            setUser(response.data.user);
            return true;
        } catch (error) {
            // console.error('Token verification failed:', error);
            localStorage.removeItem('token');
            delete api.defaults.headers.common['Authorization'];
            return false;
        }
    };

    useEffect(() => {
        const initializeAuth = async () => {
            const token = localStorage.getItem('token');
            if (token) {
                api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
                await verifyToken(token);
            }
            setLoading(false);
        };

        initializeAuth();
    }, []);

    const login = async (email, password) => {
        try {
            // console.log('Attempting login with URL:', `${apiBaseURL}/api/auth/login`);
            // console.log('Login request details:', {
            //     url: `${apiBaseURL}/api/auth/login`,
            //     method: 'POST',
            //     data: { email },
            //     headers: api.defaults.headers
            // });

            const response = await api.post('/api/auth/login', {
                email,
                password
            });

            // console.log('Login response:', {
            //     status: response.status,
            //     data: response.data
            // });

            return response.data;
        } catch (error) {
            // console.error('Login error details:', {
            //     error: error,
            //     response: error.response,
            //     config: error.config,
            //     message: error.message,
            //     stack: error.stack
            // });
            throw new Error(error.response?.data?.message || 'Login failed');
        }
    };

    const sendOTP = async (tempToken) => {
        try {
            const response = await api.post('/api/auth/send-otp', { 
                tempToken 
            });
            return response.data;
        } catch (error) {
            throw new Error(error.response?.data?.message || 'Failed to send OTP');
        }
    };

    const verifyOTP = async (otp, tempToken) => {
        try {
            const response = await api.post('/api/auth/verify-otp', {
                otp,
                tempToken
            });
            
            const { user: userData, token } = response.data;
            setUser(userData);
            
            // Store the token in localStorage
            localStorage.setItem('token', token);
            
            // Set the token in axios defaults for future requests
            api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
            
            return response.data;
        } catch (error) {
            // console.error('Verification error:', error.response?.data);
            throw new Error(error.response?.data?.message || 'Verification failed');
        }
    };

    const logout = async () => {
        try {
            await api.post('/api/auth/logout');
        } catch (error) {
            // console.error('Logout error:', error);
        } finally {
            setUser(null);
            localStorage.removeItem('token');
            delete api.defaults.headers.common['Authorization'];
            window.location.href = '/login';
        }
    };

    const value = {
        user,
        loading,
        login,
        sendOTP,
        verifyOTP,
        logout,
        isAuthenticated: !!user,
        isAdmin: user?.role === 'admin'
    };

    return (
        <AuthContext.Provider value={value}>
            {!loading && children}
        </AuthContext.Provider>
    );
};

AuthProvider.propTypes = {
    children: PropTypes.node.isRequired
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
