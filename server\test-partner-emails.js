/**
 * Manual test script to verify partner creation email functionality
 * Run this script to test the email notifications without creating actual partners
 */

const emailService = require('./src/utils/emailService');

// Mock partner data for testing
const mockPartnerData = {
    id: 'test-partner-123',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    businessName: 'Test Business Inc.',
    businessEmail: '<EMAIL>',
    phoneNumber: '+1234567890',
    markupPercentage: 15,
    billingAddressLine1: '123 Main Street',
    billingAddressLine2: 'Suite 100',
    billingCity: 'New York',
    billingProvince: 'NY',
    billingPostalCode: '10001',
    createdAt: new Date()
};

const mockPassword = 'TempPassword123!';
const mockAdminEmails = ['<EMAIL>', '<EMAIL>'];

async function testPartnerWelcomeEmail() {
    console.log('Testing partner welcome email...');
    try {
        await emailService.sendPartnerWelcomeEmail(
            mockPartnerData.email,
            mockPartnerData,
            mockPassword
        );
        console.log('✅ Partner welcome email test passed');
    } catch (error) {
        console.error('❌ Partner welcome email test failed:', error.message);
    }
}

async function testAdminNotificationEmail() {
    console.log('Testing admin notification email...');
    try {
        await emailService.sendAdminPartnerNotificationEmail(
            mockAdminEmails,
            mockPartnerData
        );
        console.log('✅ Admin notification email test passed');
    } catch (error) {
        console.error('❌ Admin notification email test failed:', error.message);
    }
}

async function runTests() {
    console.log('🚀 Starting partner email notification tests...\n');
    
    // Check if required environment variables are set
    if (!process.env.ZEPTO_API_KEY || !process.env.ZEPTO_FROM_EMAIL) {
        console.error('❌ Missing required environment variables:');
        console.error('   - ZEPTO_API_KEY');
        console.error('   - ZEPTO_FROM_EMAIL');
        console.error('Please set these in your .env file');
        process.exit(1);
    }
    
    await testPartnerWelcomeEmail();
    console.log('');
    await testAdminNotificationEmail();
    
    console.log('\n🎉 All tests completed!');
    console.log('\nNote: If tests passed, check the email inboxes to verify the emails were sent correctly.');
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = {
    testPartnerWelcomeEmail,
    testAdminNotificationEmail,
    runTests
};
