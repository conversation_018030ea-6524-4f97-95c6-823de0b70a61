import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format, formatDistanceToNow } from 'date-fns';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Users,
    ShoppingBag,
    DollarSign,
    Globe,
    ArrowRight,
    TrendingUp,
    Calendar,
    Clock,
    BarChart3,
    LineChart as LineChartIcon,
    Activity,
    Download,
    Maximize2,
    RefreshCw,
    Zap,
    CheckCircle,
    Grid3X3,
    Settings
} from 'lucide-react';
import {
    LineChart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer,
    Area,
    AreaChart,
    BarChart,
    Bar,
    ComposedChart
} from 'recharts';
import api from '@/lib/axios';

// Animated Counter Component
const AnimatedCounter = ({ value, duration = 2000 }) => {
    const [count, setCount] = useState(0);

    useEffect(() => {
        if (typeof value !== 'number') return;

        const startTime = Date.now();
        const startValue = 0;
        const endValue = value;

        const updateCount = () => {
            const now = Date.now();
            const progress = Math.min((now - startTime) / duration, 1);
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.floor(startValue + (endValue - startValue) * easeOutQuart);

            setCount(currentValue);

            if (progress < 1) {
                requestAnimationFrame(updateCount);
            }
        };

        requestAnimationFrame(updateCount);
    }, [value, duration]);

    return <span>{typeof value === 'number' ? count : value}</span>;
};

// Enhanced StatCard with modern design and animations
const StatCard = ({ title, value, icon: Icon, trend, loading, color, onClick, subtitle, isVisible = true }) => {
    const [isHovered, setIsHovered] = useState(false);

    // Define gradient colors for each card type
    const gradients = {
        purple: 'from-purple-500 via-purple-600 to-indigo-600',
        blue: 'from-blue-500 via-blue-600 to-cyan-600',
        green: 'from-emerald-500 via-emerald-600 to-teal-600',
        orange: 'from-orange-500 via-orange-600 to-amber-600',
        red: 'from-red-500 via-red-600 to-pink-600',
        indigo: 'from-indigo-500 via-indigo-600 to-purple-600',
    };

    const selectedGradient = gradients[color] || gradients.blue;

    if (!isVisible) return null;

    return (
        <Card
            className="group overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 cursor-pointer border-0 relative"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            {/* Animated background overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

            <div className={`bg-gradient-to-br ${selectedGradient} p-6 text-white h-full relative overflow-hidden`} onClick={onClick}>
                {/* Animated background pattern */}
                <div className="absolute inset-0 opacity-10">
                    <div className={`absolute -top-4 -right-4 w-24 h-24 rounded-full bg-white/20 transform transition-transform duration-700 ${isHovered ? 'scale-150 rotate-45' : 'scale-100'}`} />
                    <div className={`absolute -bottom-4 -left-4 w-16 h-16 rounded-full bg-white/10 transform transition-transform duration-700 ${isHovered ? 'scale-125 -rotate-45' : 'scale-100'}`} />
                </div>

                <div className="flex items-start justify-between relative z-10">
                    <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                            <p className="text-sm font-medium text-white/90">{title}</p>
                            {subtitle && (
                                <Badge variant="secondary" className="bg-white/20 text-white text-xs border-0">
                                    {subtitle}
                                </Badge>
                            )}
                        </div>
                        {loading ? (
                            <Skeleton className="h-10 w-28 mt-2 bg-white/30 rounded-lg" />
                        ) : (
                            <>
                                <h3 className="text-3xl font-bold mt-2 tracking-tight">
                                    <AnimatedCounter value={typeof value === 'string' && value.startsWith('$') ? value : value} />
                                </h3>
                                {trend && (
                                    <div className="flex items-center mt-3 text-white/90">
                                        <div className={`flex items-center transition-transform duration-300 ${isHovered ? 'scale-110' : 'scale-100'}`}>
                                            {trend.type === 'up' ? (
                                                <TrendingUp className="w-4 h-4 mr-1" />
                                            ) : (
                                                <TrendingUp className="w-4 h-4 mr-1 transform rotate-180" />
                                            )}
                                            <p className="text-sm font-medium">{trend.value}</p>
                                        </div>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                    <div className={`bg-white/20 p-3 rounded-xl backdrop-blur-sm transition-all duration-300 ${isHovered ? 'bg-white/30 scale-110 rotate-6' : 'scale-100'}`}>
                        <Icon className="w-6 h-6" />
                    </div>
                </div>

                {/* Pulse effect on hover */}
                <div className={`absolute inset-0 bg-white/5 rounded-lg transition-opacity duration-300 ${isHovered ? 'opacity-100' : 'opacity-0'}`} />
            </div>
        </Card>
    );
};

const TopPartners = ({ loading, partners, navigate }) => (
    <Card className="shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden h-full bg-white/50 backdrop-blur-sm">
        <div className="bg-gradient-to-r from-violet-100 to-indigo-100 p-6 border-b">
            <div className="flex items-center justify-between mb-2">
                <div>
                    <h3 className="text-lg font-bold text-slate-800">Top Partners</h3>
                    <p className="text-sm text-slate-600">Partners with highest order volume</p>
                </div>
                <Button 
                    variant="outline" 
                    className="bg-gradient-to-r from-violet-500 to-indigo-500 text-white hover:from-violet-600 hover:to-indigo-600 transition-all duration-300"
                    onClick={() => navigate('/admin/partners')}
                >
                    View All
                    <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
            </div>
        </div>
        <div className="p-4 space-y-3">
            {loading ? (
                Array(5).fill(0).map((_, i) => (
                    <div key={i} className="flex items-center justify-between p-4 rounded-xl bg-slate-50/50 border border-slate-200">
                        <div>
                            <Skeleton className="h-4 w-32 mb-2" />
                            <Skeleton className="h-3 w-24" />
                        </div>
                        <Skeleton className="h-4 w-20" />
                    </div>
                ))
            ) : (
                partners.map((partner, index) => (
                    <div key={index} className="flex items-center justify-between p-4 rounded-xl bg-white border border-slate-200 hover:bg-slate-50/80 transition-all duration-300">
                        <div>
                            <p className="font-medium text-slate-800">{partner.businessName || partner.email}</p>
                            <p className="text-sm text-slate-500 flex items-center mt-1">
                                <ShoppingBag className="w-3 h-3 mr-1" />
                                {partner.orderCount} orders
                            </p>
                        </div>
                        <div className="text-right">
                            <p className="font-semibold text-slate-800">${partner.totalSpent.toFixed(2)}</p>
                            <p className="text-xs text-slate-500 mt-1">Total spent</p>
                        </div>
                    </div>
                ))
            )}
        </div>
    </Card>
);

const RecentSignups = ({ loading, signups, navigate }) => (
    <Card className="shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden h-full bg-white/50 backdrop-blur-sm">
        <div className="bg-gradient-to-r from-sky-100 to-blue-100 p-6 border-b">
            <div className="flex items-center justify-between mb-2">
                <div>
                    <h3 className="text-lg font-bold text-slate-800">Recent Partner Signups</h3>
                    <p className="text-sm text-slate-600">Latest partners to join the platform</p>
                </div>
                <Button 
                    variant="outline" 
                    className="bg-gradient-to-r from-violet-500 to-indigo-500 text-white hover:from-sky-600 hover:to-blue-600 transition-all duration-300"
                    onClick={() => navigate('/admin/partners')}
                >
                    View All
                    <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
            </div>
        </div>
        <div className="p-4 space-y-3">
            {loading ? (
                Array(5).fill(0).map((_, i) => (
                    <div key={i} className="flex items-center justify-between p-4 rounded-xl bg-slate-50/50 border border-slate-200">
                        <div>
                            <Skeleton className="h-4 w-32 mb-2" />
                            <Skeleton className="h-3 w-24" />
                        </div>
                        <Skeleton className="h-4 w-20" />
                    </div>
                ))
            ) : (
                signups.map((signup, index) => (
                    <div key={index} className="flex items-center justify-between p-4 rounded-xl bg-white border border-slate-200 hover:bg-slate-50/80 transition-all duration-300">
                        <div className="flex-1">
                            <p className="font-medium text-slate-800">{signup.businessName || signup.email}</p>
                            <p className="text-sm text-slate-500 flex items-center mt-1">
                                <Clock className="w-3 h-3 mr-1" />
                                Joined {formatDistanceToNow(new Date(signup.createdAt))} ago
                            </p>
                        </div>
                        <Button 
                            variant="outline" 
                            size="sm"
                            className="ml-2 bg-gradient-to-r from-slate-500 to-slate-600 text-white hover:from-slate-600 hover:to-slate-700"
                            onClick={() => navigate(`/admin/partners/${signup.id}`)}
                        >
                            Details
                        </Button>
                    </div>
                ))
            )}
        </div>
    </Card>
);

const RecentOrders = ({ loading, orders, navigate }) => (
    <Card className="shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden bg-white/50 backdrop-blur-sm">
        <div className="bg-gradient-to-r from-emerald-100 to-green-100 p-6 border-b">
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-lg font-bold text-slate-800">Recent Orders</h2>
                    <p className="text-sm text-slate-600">Latest eSIM purchases across all partners</p>
                </div>
                <Button 
                    variant="outline" 
                    className="bg-gradient-to-r from-emerald-500 to-green-500 text-white hover:from-emerald-600 hover:to-green-600 transition-all duration-300"
                    onClick={() => navigate('/admin/orders')}
                >
                    View All
                    <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
            </div>
        </div>
        <div className="overflow-x-auto">
            <Table>
                <TableHeader>
                    <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100"> 
                        <TableHead className="font-semibold text-slate-700">Order ID</TableHead>
                        <TableHead className="font-semibold text-slate-700">Partner</TableHead>
                        <TableHead className="font-semibold text-slate-700">Plan</TableHead>
                        <TableHead className="font-semibold text-slate-700">Plan Details</TableHead>
                        <TableHead className="font-semibold text-slate-700">Quantity</TableHead>
                        <TableHead className="font-semibold text-slate-700">Amount</TableHead>
                        <TableHead className="font-semibold text-slate-700">Status</TableHead>
                        <TableHead className="font-semibold text-slate-700">Date</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {loading ? (
                        Array(5).fill(0).map((_, i) => (
                            <TableRow key={i} className="hover:bg-slate-50/80 transition-colors">
                                <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                                <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                                <TableCell><Skeleton className="h-4 w-48" /></TableCell>
                                <TableCell><Skeleton className="h-4 w-48" /></TableCell>
                                <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                                <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                                <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                                <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                            </TableRow>
                        ))
                    ) : (
                        orders.map((order) => (
                            <TableRow key={order.id} className="hover:bg-slate-50/80 cursor-pointer transition-all duration-300">
                                <TableCell className="font-medium text-slate-800">{order.id}</TableCell>
                                <TableCell>{order.partnerName}</TableCell>
                                <TableCell className="font-medium">{order.planName}</TableCell>
                                <TableCell>
                                   <p className="flex items-center"> 
                                    {order.planType === 'Unlimited' ? (
                                        <span className="font-medium text-blue-600 bg-blue-50 px-3 py-1 rounded-full text-xs">Unlimited</span>
                                    ) : order.planType === 'Custom' ? (
                                        <span className="font-medium text-violet-600 bg-violet-50 px-3 py-1 rounded-full text-xs">
                                            {order.customPlanData || 'Custom Plan'}
                                        </span>
                                    ) : order.planData && order.planDataUnit ? (
                                        <span className="font-medium text-slate-600 bg-slate-100 px-3 py-1 rounded-full text-xs">
                                            {order.planData} {order.planDataUnit}
                                        </span>
                                    ) : (
                                        '-'
                                    )} 
                                    <span className="ml-2 flex items-center text-slate-500 text-xs">
                                        <Calendar className="w-3 h-3 mr-1" />
                                        {order.validityDays} Day{order.validityDays > 1 ? 's' : ''}
                                    </span>
                                    </p>
                                </TableCell>
                                <TableCell className="font-medium">{order.quantity}</TableCell>
                                <TableCell className="font-semibold text-slate-800">${order.amount?.toFixed(2)}</TableCell>
                                <TableCell>
                                    <span className={`px-3 py-1 rounded-full text-xs ${
                                        order.status === 'completed' ? 'bg-emerald-100 text-emerald-800' :
                                        order.status === 'pending' ? 'bg-amber-100 text-amber-800' :
                                        'bg-slate-100 text-slate-800'
                                    }`}>
                                        {order.status}
                                    </span>
                                </TableCell>    
                                <TableCell className="text-slate-600">{format(new Date(order.createdAt), 'MMM dd, yyyy')}</TableCell>
                            </TableRow>
                        ))
                    )}
                </TableBody>
            </Table>
        </div>
    </Card>
);



export default function AdminDashboard() {
    const navigate = useNavigate();
    const { toast } = useToast();
    const [loading, setLoading] = useState(true);
    const [chartType, setChartType] = useState('area'); // 'area', 'line', 'bar', 'composed'
    const [timePeriod, setTimePeriod] = useState('6months'); // '3months', '6months', '1year'
    const [showMetrics, setShowMetrics] = useState({
        orders: true,
        revenue: true,
        partners: true,
        plans: true,
        performance: true,
        recentActivity: true
    });

    const [lastRefresh, setLastRefresh] = useState(new Date());
    const [stats, setStats] = useState({
        totalPartners: 0,
        totalOrders: 0,
        totalRevenue: 0,
        totalPlans: 0,
        last30DaysOrders: 0,
        monthlyTrends: [],
        topPartners: [],
        recentOrders: [],
        recentSignups: [],
        dailyTrends: [],
        weeklyTrends: [],
        ordersByStatus: [],
        revenueByPlan: []
    });

    useEffect(() => {
        fetchDashboardStats();
    }, []);

    const fetchDashboardStats = async () => {
        try {
            setLoading(true);
            const response = await api.get('/api/admin/dashboard/stats');
            setStats(response.data);
            setLastRefresh(new Date());
        } catch (error) {
            // console.error('Error fetching dashboard stats:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to fetch dashboard statistics"
            });
        } finally {
            setLoading(false);
        }
    };

    const handleRefresh = () => {
        fetchDashboardStats();
        toast({
            title: "Dashboard Refreshed",
            description: "Data has been updated successfully"
        });
    };

    return (
        <div className="h-full flex flex-col gap-6 p-6 rounded-lg transition-all duration-300 bg-gradient-to-br from-slate-50 to-gray-100">
            {/* Enhanced Header with Controls */}
            <div className="p-6 rounded-xl shadow-sm mb-2 relative bg-gradient-to-r from-blue-800 to-blue-600">
                {/* Animated background elements */}
                <div className="absolute inset-0 opacity-10">
                    <div className="absolute top-0 right-0 w-32 h-32 rounded-full bg-white/20 transform translate-x-16 -translate-y-16" />
                    <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full bg-white/10 transform -translate-x-12 translate-y-12" />
                </div>

                <div className="relative z-10 flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-white">Admin Dashboard</h1>
                        <p className="text-white/80 mt-1">Overview of your eSIM platform performance</p>
                        <div className="flex items-center gap-4 mt-2">
                            <Badge variant="secondary" className="bg-white/20 text-white border-0">
                                <Activity className="w-3 h-3 mr-1" />
                                Live Data
                            </Badge>
                            <span className="text-white/60 text-sm">
                                Last updated: {format(lastRefresh, 'MMM dd, HH:mm')}
                            </span>
                        </div>
                    </div>

                    <div className="flex items-center gap-3">
                        <Button
                            variant="outline"
                            size="sm"
                            className="bg-white/20 border-white/30 text-white hover:bg-white/30"
                            onClick={handleRefresh}
                            disabled={loading}
                        >
                            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                            Refresh
                        </Button>
                    </div>
                </div>
            </div>

            {/* Enhanced Quick Stats */}
            {showMetrics.partners && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <StatCard
                        title="Total Partners"
                        value={stats.totalPartners}
                        icon={Users}
                        loading={loading}
                        color="purple"
                        subtitle="Active"
                        onClick={() => navigate('/admin/partners')}
                        isVisible={showMetrics.partners}
                    />
                    <StatCard
                        title="Total Orders"
                        value={stats.totalOrders}
                        icon={ShoppingBag}
                        trend={{
                            type: 'up',
                            value: `${stats.last30DaysOrders} new this month`
                        }}
                        loading={loading}
                        color="blue"
                        subtitle="All Time"
                        onClick={() => navigate('/admin/orders')}
                        isVisible={showMetrics.orders}
                    />
                    <StatCard
                        title="Total Revenue"
                        value={`$${stats.totalRevenue.toFixed(2)}`}
                        icon={DollarSign}
                        loading={loading}
                        color="green"
                        subtitle="Lifetime"
                        onClick={() => navigate('/admin/central-wallet')}
                        isVisible={showMetrics.revenue}
                    />
                    <StatCard
                        title="Active Plans"
                        value={stats.totalPlans}
                        icon={Globe}
                        loading={loading}
                        color="orange"
                        subtitle="Available"
                        onClick={() => navigate('/admin/esim-plans')}
                        isVisible={showMetrics.plans}
                    />
                </div>
            )}

            {/* Key Performance Indicators */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 hover:shadow-md transition-all duration-300">
                    <div className="flex items-center gap-3">
                        <div className="p-2 bg-green-100 rounded-lg">
                            <CheckCircle className="w-5 h-5 text-green-600" />
                        </div>
                        <div>
                            <p className="text-sm font-medium text-green-800">System Status</p>
                            <p className="text-xs text-green-600">All systems operational</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-4 bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-200 hover:shadow-md transition-all duration-300">
                    <div className="flex items-center gap-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                            <Zap className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                            <p className="text-sm font-medium text-blue-800">API Performance</p>
                            <p className="text-xs text-blue-600">Response time: 120ms</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-4 bg-gradient-to-r from-purple-50 to-indigo-50 border-purple-200 hover:shadow-md transition-all duration-300">
                    <div className="flex items-center gap-3">
                        <div className="p-2 bg-purple-100 rounded-lg">
                            <TrendingUp className="w-5 h-5 text-purple-600" />
                        </div>
                        <div>
                            <p className="text-sm font-medium text-purple-800">Growth Rate</p>
                            <p className="text-xs text-purple-600">+12.5% this month</p>
                        </div>
                    </div>
                </Card>

                <Card className="p-4 bg-gradient-to-r from-rose-50 to-pink-50 border-rose-200 hover:shadow-md transition-all duration-300">
                    <div className="flex items-center gap-3">
                        <div className="p-2 bg-rose-100 rounded-lg">
                            <Activity className="w-5 h-5 text-rose-600" />
                        </div>
                        <div>
                            <p className="text-sm font-medium text-rose-800">Active Sessions</p>
                            <p className="text-xs text-rose-600">{stats.totalPartners * 0.3 | 0} online now</p>
                        </div>
                    </div>
                </Card>
            </div>

            {/* Enhanced Platform Performance Section */}
            <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
                {/* Main Chart */}
                <div className="xl:col-span-3">
                    <Card className="shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden bg-white/50 backdrop-blur-sm">
                        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-6 border-b">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h2 className="text-xl font-bold text-white">Platform Performance</h2>
                                    <p className="text-indigo-100 text-sm">Advanced analytics and trends</p>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="bg-white/20 border-white/30 text-white hover:bg-white/30"
                                        onClick={() => {/* Download functionality */}}
                                    >
                                        <Download className="w-4 h-4 mr-2" />
                                        Export
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="bg-white/20 border-white/30 text-white hover:bg-white/30"
                                        onClick={() => {/* Fullscreen functionality */}}
                                    >
                                        <Maximize2 className="w-4 h-4" />
                                    </Button>
                                </div>
                            </div>

                            {/* Chart Controls */}
                            <div className="flex items-center justify-between mt-4">
                                <div className="flex items-center gap-2">
                                    <span className="text-indigo-100 text-sm font-medium">Chart Type:</span>
                                    <div className="flex bg-white/20 rounded-lg p-1">
                                        <Button
                                            variant={chartType === 'area' ? 'default' : 'ghost'}
                                            size="sm"
                                            className={`h-8 px-3 ${chartType === 'area' ? 'bg-white text-indigo-600' : 'text-white hover:bg-white/20'}`}
                                            onClick={() => setChartType('area')}
                                        >
                                            <Activity className="w-3 h-3 mr-1" />
                                            Area
                                        </Button>
                                        <Button
                                            variant={chartType === 'line' ? 'default' : 'ghost'}
                                            size="sm"
                                            className={`h-8 px-3 ${chartType === 'line' ? 'bg-white text-indigo-600' : 'text-white hover:bg-white/20'}`}
                                            onClick={() => setChartType('line')}
                                        >
                                            <LineChartIcon className="w-3 h-3 mr-1" />
                                            Line
                                        </Button>
                                        <Button
                                            variant={chartType === 'bar' ? 'default' : 'ghost'}
                                            size="sm"
                                            className={`h-8 px-3 ${chartType === 'bar' ? 'bg-white text-indigo-600' : 'text-white hover:bg-white/20'}`}
                                            onClick={() => setChartType('bar')}
                                        >
                                            <BarChart3 className="w-3 h-3 mr-1" />
                                            Bar
                                        </Button>
                                        <Button
                                            variant={chartType === 'composed' ? 'default' : 'ghost'}
                                            size="sm"
                                            className={`h-8 px-3 ${chartType === 'composed' ? 'bg-white text-indigo-600' : 'text-white hover:bg-white/20'}`}
                                            onClick={() => setChartType('composed')}
                                        >
                                            <TrendingUp className="w-3 h-3 mr-1" />
                                            Mixed
                                        </Button>
                                    </div>
                                </div>

                                <div className="flex items-center gap-2">
                                    <span className="text-indigo-100 text-sm font-medium">Period:</span>
                                    <div className="flex bg-white/20 rounded-lg p-1">
                                        <Button
                                            variant={timePeriod === '3months' ? 'default' : 'ghost'}
                                            size="sm"
                                            className={`h-8 px-3 ${timePeriod === '3months' ? 'bg-white text-indigo-600' : 'text-white hover:bg-white/20'}`}
                                            onClick={() => setTimePeriod('3months')}
                                        >
                                            3M
                                        </Button>
                                        <Button
                                            variant={timePeriod === '6months' ? 'default' : 'ghost'}
                                            size="sm"
                                            className={`h-8 px-3 ${timePeriod === '6months' ? 'bg-white text-indigo-600' : 'text-white hover:bg-white/20'}`}
                                            onClick={() => setTimePeriod('6months')}
                                        >
                                            6M
                                        </Button>
                                        <Button
                                            variant={timePeriod === '1year' ? 'default' : 'ghost'}
                                            size="sm"
                                            className={`h-8 px-3 ${timePeriod === '1year' ? 'bg-white text-indigo-600' : 'text-white hover:bg-white/20'}`}
                                            onClick={() => setTimePeriod('1year')}
                                        >
                                            1Y
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="p-6 bg-white/80">
                            <div className="h-[400px]">
                                {loading ? (
                                    <div className="w-full h-full flex items-center justify-center">
                                        <div className="text-center">
                                            <div className="w-12 h-12 mx-auto mb-4 bg-indigo-100 rounded-full flex items-center justify-center">
                                                <TrendingUp className="w-6 h-6 animate-pulse text-indigo-600" />
                                            </div>
                                            <p className="text-gray-600">Loading analytics...</p>
                                        </div>
                                    </div>
                                ) : stats.monthlyTrends.length === 0 ? (
                                    <div className="w-full h-full flex items-center justify-center text-slate-500">
                                        <div className="text-center">
                                            <TrendingUp className="w-16 h-16 text-slate-300 mx-auto mb-4" />
                                            <h3 className="text-lg font-medium text-slate-600 mb-2">No Data Available</h3>
                                            <p className="text-slate-500">Start processing orders to see analytics</p>
                                        </div>
                                    </div>
                                ) : (
                                    <ResponsiveContainer width="100%" height="100%">
                                        {chartType === 'area' && (
                                            <AreaChart data={stats.monthlyTrends} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                                                <defs>
                                                    <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                                                        <stop offset="5%" stopColor="#10B981" stopOpacity={0.8}/>
                                                        <stop offset="95%" stopColor="#10B981" stopOpacity={0.1}/>
                                                    </linearGradient>
                                                    <linearGradient id="colorOrders" x1="0" y1="0" x2="0" y2="1">
                                                        <stop offset="5%" stopColor="#6366F1" stopOpacity={0.8}/>
                                                        <stop offset="95%" stopColor="#6366F1" stopOpacity={0.1}/>
                                                    </linearGradient>
                                                    <linearGradient id="colorPartners" x1="0" y1="0" x2="0" y2="1">
                                                        <stop offset="5%" stopColor="#F59E0B" stopOpacity={0.8}/>
                                                        <stop offset="95%" stopColor="#F59E0B" stopOpacity={0.1}/>
                                                    </linearGradient>
                                                </defs>
                                                <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" strokeOpacity={0.5} />
                                                <XAxis
                                                    dataKey="month"
                                                    tickFormatter={(value) => format(new Date(value), 'MMM yyyy')}
                                                    interval="preserveStartEnd"
                                                    style={{ fontSize: '12px', fill: '#64748B' }}
                                                    axisLine={{ stroke: '#E2E8F0' }}
                                                    tickLine={{ stroke: '#E2E8F0' }}
                                                />
                                                <YAxis
                                                    yAxisId="left"
                                                    orientation="left"
                                                    stroke="#6366F1"
                                                    style={{ fontSize: '12px', fill: '#64748B' }}
                                                    axisLine={{ stroke: '#E2E8F0' }}
                                                    tickLine={{ stroke: '#E2E8F0' }}
                                                />
                                                <YAxis
                                                    yAxisId="right"
                                                    orientation="right"
                                                    stroke="#10B981"
                                                    style={{ fontSize: '12px', fill: '#64748B' }}
                                                    axisLine={{ stroke: '#E2E8F0' }}
                                                    tickLine={{ stroke: '#E2E8F0' }}
                                                />
                                                <Tooltip
                                                    labelFormatter={(value) => format(new Date(value), 'MMMM yyyy')}
                                                    formatter={(value, name) => [
                                                        name === 'Revenue' ? `$${value.toFixed(2)}` : value,
                                                        name
                                                    ]}
                                                    contentStyle={{
                                                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                                        border: '1px solid #e2e8f0',
                                                        borderRadius: '12px',
                                                        padding: '12px',
                                                        boxShadow: '0 10px 25px rgba(0,0,0,0.1)',
                                                        backdropFilter: 'blur(10px)'
                                                    }}
                                                />
                                                <Legend
                                                    wrapperStyle={{
                                                        paddingTop: '20px',
                                                        fontSize: '13px',
                                                        color: '#64748B'
                                                    }}
                                                />
                                                {showMetrics.orders && (
                                                    <Area
                                                        yAxisId="left"
                                                        type="monotone"
                                                        dataKey="count"
                                                        name="Orders"
                                                        stroke="#6366F1"
                                                        strokeWidth={3}
                                                        fillOpacity={1}
                                                        fill="url(#colorOrders)"
                                                        dot={{ r: 5, strokeWidth: 2, fill: '#6366F1' }}
                                                        activeDot={{ r: 8, strokeWidth: 2, fill: '#6366F1' }}
                                                    />
                                                )}
                                                {showMetrics.revenue && (
                                                    <Area
                                                        yAxisId="right"
                                                        type="monotone"
                                                        dataKey="revenue"
                                                        name="Revenue"
                                                        stroke="#10B981"
                                                        strokeWidth={3}
                                                        fillOpacity={1}
                                                        fill="url(#colorRevenue)"
                                                        dot={{ r: 5, strokeWidth: 2, fill: '#10B981' }}
                                                        activeDot={{ r: 8, strokeWidth: 2, fill: '#10B981' }}
                                                    />
                                                )}
                                            </AreaChart>
                                        )}

                                        {chartType === 'line' && (
                                            <LineChart data={stats.monthlyTrends} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                                                <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" strokeOpacity={0.5} />
                                                <XAxis
                                                    dataKey="month"
                                                    tickFormatter={(value) => format(new Date(value), 'MMM yyyy')}
                                                    interval="preserveStartEnd"
                                                    style={{ fontSize: '12px', fill: '#64748B' }}
                                                />
                                                <YAxis
                                                    yAxisId="left"
                                                    orientation="left"
                                                    stroke="#6366F1"
                                                    style={{ fontSize: '12px', fill: '#64748B' }}
                                                />
                                                <YAxis
                                                    yAxisId="right"
                                                    orientation="right"
                                                    stroke="#10B981"
                                                    style={{ fontSize: '12px', fill: '#64748B' }}
                                                />
                                                <Tooltip
                                                    labelFormatter={(value) => format(new Date(value), 'MMMM yyyy')}
                                                    formatter={(value, name) => [
                                                        name === 'Revenue' ? `$${value.toFixed(2)}` : value,
                                                        name
                                                    ]}
                                                    contentStyle={{
                                                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                                        border: '1px solid #e2e8f0',
                                                        borderRadius: '12px',
                                                        padding: '12px',
                                                        boxShadow: '0 10px 25px rgba(0,0,0,0.1)',
                                                        backdropFilter: 'blur(10px)'
                                                    }}
                                                />
                                                <Legend />
                                                {showMetrics.orders && (
                                                    <Line
                                                        yAxisId="left"
                                                        type="monotone"
                                                        dataKey="count"
                                                        name="Orders"
                                                        stroke="#6366F1"
                                                        strokeWidth={3}
                                                        dot={{ r: 5, strokeWidth: 2, fill: '#6366F1' }}
                                                        activeDot={{ r: 8, strokeWidth: 2, fill: '#6366F1' }}
                                                    />
                                                )}
                                                {showMetrics.revenue && (
                                                    <Line
                                                        yAxisId="right"
                                                        type="monotone"
                                                        dataKey="revenue"
                                                        name="Revenue"
                                                        stroke="#10B981"
                                                        strokeWidth={3}
                                                        dot={{ r: 5, strokeWidth: 2, fill: '#10B981' }}
                                                        activeDot={{ r: 8, strokeWidth: 2, fill: '#10B981' }}
                                                    />
                                                )}
                                            </LineChart>
                                        )}

                                        {chartType === 'bar' && (
                                            <BarChart data={stats.monthlyTrends} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                                                <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" strokeOpacity={0.5} />
                                                <XAxis
                                                    dataKey="month"
                                                    tickFormatter={(value) => format(new Date(value), 'MMM yyyy')}
                                                    interval="preserveStartEnd"
                                                    style={{ fontSize: '12px', fill: '#64748B' }}
                                                />
                                                <YAxis
                                                    yAxisId="left"
                                                    orientation="left"
                                                    stroke="#6366F1"
                                                    style={{ fontSize: '12px', fill: '#64748B' }}
                                                />
                                                <YAxis
                                                    yAxisId="right"
                                                    orientation="right"
                                                    stroke="#10B981"
                                                    style={{ fontSize: '12px', fill: '#64748B' }}
                                                />
                                                <Tooltip
                                                    labelFormatter={(value) => format(new Date(value), 'MMMM yyyy')}
                                                    formatter={(value, name) => [
                                                        name === 'Revenue' ? `$${value.toFixed(2)}` : value,
                                                        name
                                                    ]}
                                                    contentStyle={{
                                                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                                        border: '1px solid #e2e8f0',
                                                        borderRadius: '12px',
                                                        padding: '12px',
                                                        boxShadow: '0 10px 25px rgba(0,0,0,0.1)',
                                                        backdropFilter: 'blur(10px)'
                                                    }}
                                                />
                                                <Legend />
                                                {showMetrics.orders && (
                                                    <Bar
                                                        yAxisId="left"
                                                        dataKey="count"
                                                        name="Orders"
                                                        fill="#6366F1"
                                                        radius={[4, 4, 0, 0]}
                                                    />
                                                )}
                                                {showMetrics.revenue && (
                                                    <Bar
                                                        yAxisId="right"
                                                        dataKey="revenue"
                                                        name="Revenue"
                                                        fill="#10B981"
                                                        radius={[4, 4, 0, 0]}
                                                    />
                                                )}
                                            </BarChart>
                                        )}

                                        {chartType === 'composed' && (
                                            <ComposedChart data={stats.monthlyTrends} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                                                <defs>
                                                    <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                                                        <stop offset="5%" stopColor="#10B981" stopOpacity={0.8}/>
                                                        <stop offset="95%" stopColor="#10B981" stopOpacity={0.1}/>
                                                    </linearGradient>
                                                </defs>
                                                <CartesianGrid strokeDasharray="3 3" stroke="#E2E8F0" strokeOpacity={0.5} />
                                                <XAxis
                                                    dataKey="month"
                                                    tickFormatter={(value) => format(new Date(value), 'MMM yyyy')}
                                                    interval="preserveStartEnd"
                                                    style={{ fontSize: '12px', fill: '#64748B' }}
                                                />
                                                <YAxis
                                                    yAxisId="left"
                                                    orientation="left"
                                                    stroke="#6366F1"
                                                    style={{ fontSize: '12px', fill: '#64748B' }}
                                                />
                                                <YAxis
                                                    yAxisId="right"
                                                    orientation="right"
                                                    stroke="#10B981"
                                                    style={{ fontSize: '12px', fill: '#64748B' }}
                                                />
                                                <Tooltip
                                                    labelFormatter={(value) => format(new Date(value), 'MMMM yyyy')}
                                                    formatter={(value, name) => [
                                                        name === 'Revenue' ? `$${value.toFixed(2)}` : value,
                                                        name
                                                    ]}
                                                    contentStyle={{
                                                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                                        border: '1px solid #e2e8f0',
                                                        borderRadius: '12px',
                                                        padding: '12px',
                                                        boxShadow: '0 10px 25px rgba(0,0,0,0.1)',
                                                        backdropFilter: 'blur(10px)'
                                                    }}
                                                />
                                                <Legend />
                                                {showMetrics.orders && (
                                                    <Bar
                                                        yAxisId="left"
                                                        dataKey="count"
                                                        name="Orders"
                                                        fill="#6366F1"
                                                        radius={[4, 4, 0, 0]}
                                                    />
                                                )}
                                                {showMetrics.revenue && (
                                                    <Area
                                                        yAxisId="right"
                                                        type="monotone"
                                                        dataKey="revenue"
                                                        name="Revenue"
                                                        stroke="#10B981"
                                                        strokeWidth={3}
                                                        fillOpacity={1}
                                                        fill="url(#colorRevenue)"
                                                        dot={{ r: 5, strokeWidth: 2, fill: '#10B981' }}
                                                        activeDot={{ r: 8, strokeWidth: 2, fill: '#10B981' }}
                                                    />
                                                )}
                                            </ComposedChart>
                                        )}
                                    </ResponsiveContainer>
                                )}
                            </div>

                            {/* Metrics Toggle */}
                            <div className="mt-4 flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                                <span className="text-sm font-medium text-gray-700">Show Metrics:</span>
                                <div className="flex items-center gap-4">
                                    <label className="flex items-center gap-2 cursor-pointer">
                                        <input
                                            type="checkbox"
                                            checked={showMetrics.orders}
                                            onChange={(e) => setShowMetrics(prev => ({ ...prev, orders: e.target.checked }))}
                                            className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        />
                                        <span className="text-sm text-gray-700">Orders</span>
                                        <div className="w-3 h-3 bg-indigo-600 rounded"></div>
                                    </label>
                                    <label className="flex items-center gap-2 cursor-pointer">
                                        <input
                                            type="checkbox"
                                            checked={showMetrics.revenue}
                                            onChange={(e) => setShowMetrics(prev => ({ ...prev, revenue: e.target.checked }))}
                                            className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                                        />
                                        <span className="text-sm text-gray-700">Revenue</span>
                                        <div className="w-3 h-3 bg-green-600 rounded"></div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </Card>
                </div>
            </div>

            {/* Quick Actions Panel */}
            <Card className="shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden bg-gradient-to-r from-slate-50 to-gray-50 border-0">
                <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                        <div>
                            <h3 className="text-lg font-bold text-slate-800">Quick Actions</h3>
                            <p className="text-sm text-slate-600">Frequently used admin functions</p>
                        </div>
                        <Grid3X3 className="w-5 h-5 text-slate-400" />
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        <Button
                            variant="outline"
                            className="h-20 flex-col gap-2 bg-white hover:bg-blue-50 border-blue-200 hover:border-blue-300"
                            onClick={() => navigate('/admin/partners/add')}
                        >
                            <Users className="w-5 h-5 text-blue-600" />
                            <span className="text-xs font-medium text-blue-600">Add Partner</span>
                        </Button>

                        <Button
                            variant="outline"
                            className="h-20 flex-col gap-2 bg-white hover:bg-green-50 border-green-200 hover:border-green-300"
                            onClick={() => navigate('/admin/esim-plans/add')}
                        >
                            <Globe className="w-5 h-5 text-green-600" />
                            <span className="text-xs font-medium text-green-600">Add Plan</span>
                        </Button>

                        <Button
                            variant="outline"
                            className="h-20 flex-col gap-2 bg-white hover:bg-purple-50 border-purple-200 hover:border-purple-300"
                            onClick={() => navigate('/admin/orders')}
                        >
                            <ShoppingBag className="w-5 h-5 text-purple-600" />
                            <span className="text-xs font-medium text-purple-600">View Orders</span>
                        </Button>

                        <Button
                            variant="outline"
                            className="h-20 flex-col gap-2 bg-white hover:bg-amber-50 border-amber-200 hover:border-amber-300"
                            onClick={() => navigate('/admin/central-wallet')}
                        >
                            <DollarSign className="w-5 h-5 text-amber-600" />
                            <span className="text-xs font-medium text-amber-600">Wallet</span>
                        </Button>

                        <Button
                            variant="outline"
                            className="h-20 flex-col gap-2 bg-white hover:bg-indigo-50 border-indigo-200 hover:border-indigo-300"
                            onClick={() => navigate('/admin/staffs')}
                        >
                            <Settings className="w-5 h-5 text-indigo-600" />
                            <span className="text-xs font-medium text-indigo-600">Staff</span>
                        </Button>

                        <Button
                            variant="outline"
                            className="h-20 flex-col gap-2 bg-white hover:bg-red-50 border-red-200 hover:border-red-300"
                            onClick={handleRefresh}
                        >
                            <RefreshCw className="w-5 h-5 text-red-600" />
                            <span className="text-xs font-medium text-red-600">Refresh</span>
                        </Button>
                    </div>
                </div>
            </Card>

            {/* Enhanced Bottom Section */}
            {showMetrics.recentActivity && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <TopPartners loading={loading} partners={stats.topPartners} navigate={navigate} />
                    <RecentSignups loading={loading} signups={stats.recentSignups} navigate={navigate} />
                </div>
            )}

            {/* Recent Orders */}
            {showMetrics.recentActivity && (
                <RecentOrders loading={loading} orders={stats.recentOrders} navigate={navigate} />
            )}

            {/* Footer with System Info */}
            <Card className="bg-gradient-to-r from-slate-100 to-gray-100 border-0">
                <div className="p-4">
                    <div className="flex items-center justify-between text-sm text-slate-600">
                        <div className="flex items-center gap-4">
                            <span>Dashboard v2.0</span>
                            <span>•</span>
                            <span>Last updated: {format(lastRefresh, 'HH:mm:ss')}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <span>System Online</span>
                        </div>
                    </div>
                </div>
            </Card>
        </div>
    );
}