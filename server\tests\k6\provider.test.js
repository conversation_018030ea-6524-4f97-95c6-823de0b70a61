import http from 'k6/http';
import { sleep } from 'k6';
import { CONFIG } from './config.js';
import { checkResponse, getAuthToken } from './helpers.js';

export const options = {
    thresholds: CONFIG.thresholds,
    scenarios: {
        provider_flow: CONFIG.scenarios.load
    }
};

export function setup() {
    const token = getAuthToken(http);
    return { 
        baseUrl: CONFIG.baseUrl,
        token: token
    };
}

export default function (data) {
    const baseUrl = data.baseUrl;
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${data.token}`
    };

    // Get all providers
    const providersResponse = http.get(`${baseUrl}/api/providers`, {
        headers: headers
    });
    checkResponse(providersResponse);

    sleep(1);

    // Get provider details
    const providerId = providersResponse.json('providers')[0]?.id || 'test-provider-id';
    const providerDetailsResponse = http.get(`${baseUrl}/api/providers/${providerId}`, {
        headers: headers
    });
    checkResponse(providerDetailsResponse);

    sleep(1);

    // Get provider plans
    const providerPlansResponse = http.get(`${baseUrl}/api/providers/${providerId}/plans`, {
        headers: headers
    });
    checkResponse(providerPlansResponse);

    sleep(1);

    // Get provider statistics
    const providerStatsResponse = http.get(`${baseUrl}/api/providers/${providerId}/statistics`, {
        headers: headers
    });
    checkResponse(providerStatsResponse);

    sleep(1);
}
