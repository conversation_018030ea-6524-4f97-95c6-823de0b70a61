const { v4: uuidv4 } = require('uuid');

module.exports = {
    up: async (queryInterface, Sequelize) => {
        await queryInterface.bulkInsert('Providers', [{
            id: uuidv4(),
            name: 'mobimatter',
            type: 'API',
            country: 'Global',
            apiEndpoint: process.env.MOBIMATTER_API_URL || 'https://api.mobimatter.com/mobimatter/api/v2',
            apiKey: process.env.MOBIMATTER_API_KEY,
            status: 'active',
            description: 'Mobimatter eSIM Provider',
            createdAt: new Date(),
            updatedAt: new Date()
        }], {});
    },

    down: async (queryInterface, Sequelize) => {
        await queryInterface.bulkDelete('Providers', { name: 'mobimatter' }, {});
    }
}; 