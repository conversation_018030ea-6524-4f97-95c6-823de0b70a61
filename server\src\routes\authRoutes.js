const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { isAuthenticated } = require('../middleware/auth');
const { loginValidation, otpValidation } = require('../middleware/validation');
const { loginLimiter } = require('../middleware/rateLimiter');
const { User, OTP } = require('../models');
const { Op } = require('sequelize');

// Auth routes
router.post('/login', loginLimiter, loginValidation, (req, res, next) => {
    console.log('Login request received:', {
        body: req.body,
        headers: req.headers,
        url: req.url,
        method: req.method
    });
    authController.login(req, res, next);
});
router.post('/send-otp', authController.sendOTP);
router.post('/verify-otp', otpValidation, authController.verifyOTP);
router.post('/forgot-password', authController.forgotPassword);
router.post('/reset-password', authController.resetPassword);
router.post('/verify-reset-token', authController.verifyResetToken);

// Protected routes
router.get('/verify', isAuthenticated, authController.verify);
router.post('/logout', isAuthenticated, authController.logout);

// Change password
router.post('/change-password', isAuthenticated, authController.changePassword);

// Test endpoint for load testing
if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
    router.get('/test-otp', async (req, res) => {
        try {
            const { email } = req.query;
            console.log('Test OTP request for email:', email);

            if (!email) {
                console.log('Email missing in request');
                return res.status(400).json({ message: 'Email is required' });
            }

            const user = await User.findOne({ where: { email } });
            if (!user) {
                console.log('User not found for email:', email);
                return res.status(404).json({ message: 'User not found' });
            }

            console.log('Found user:', user.id);

            const latestOTP = await OTP.findOne({
                where: {
                    userId: user.id,
                    isUsed: false,
                    expiresAt: {
                        [Op.gt]: new Date()
                    }
                },
                order: [['createdAt', 'DESC']]
            });

            if (!latestOTP) {
                console.log('No valid OTP found for user:', user.id);
                return res.status(404).json({ message: 'No valid OTP found' });
            }

            console.log('Found OTP:', latestOTP.code, 'for user:', user.id);

            res.json({ otp: latestOTP.code });
        } catch (error) {
            console.error('Test OTP error:', error);
            res.status(500).json({ message: 'Error retrieving OTP', error: error.message });
        }
    });
}

module.exports = router;
