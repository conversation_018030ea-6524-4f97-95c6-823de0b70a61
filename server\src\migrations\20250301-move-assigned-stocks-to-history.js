const { v4: uuidv4 } = require('uuid');

module.exports = {
    up: async (queryInterface, Sequelize) => {
        const transaction = await queryInterface.sequelize.transaction();

        try {
            // Get all assigned stocks
            const stocks = await queryInterface.sequelize.query(
                `SELECT * FROM EsimStocks WHERE status != 'available'`,
                { type: Sequelize.QueryTypes.SELECT, transaction }
            );

            // Move each stock to history
            for (const stock of stocks) {
                await queryInterface.sequelize.query(
                    `INSERT INTO EsimPlanStockHistory (
                        id, esimPlanId, esimStockId, iccid, smdpAddress, lpaString, 
                        accessPointName, activationCode, phoneNumber, orderId, orderDate,
                        quantity, status, createdBy, createdAt, updatedAt
                    ) VALUES (
                        :id, :esimPlanId, :esimStockId, :iccid, :smdpAddress, :lpaString,
                        :accessPointName, :activationCode, :phoneNumber, :orderId, :orderDate,
                        1, :status, :createdBy, :createdAt, :updatedAt
                    )`,
                    {
                        replacements: {
                            id: uuidv4(),
                            esimPlanId: stock.esimPlanId,
                            esimStockId: stock.id,
                            iccid: stock.iccid,
                            smdpAddress: stock.smdpAddress,
                            lpaString: stock.lpaString,
                            accessPointName: stock.accessPointName,
                            activationCode: stock.activationCode,
                            phoneNumber: stock.phoneNumber,
                            orderId: stock.orderId,
                            orderDate: stock.orderDate,
                            status: stock.status,
                            createdBy: stock.createdBy || stock.userId, // Fallback to userId if createdBy is not available
                            createdAt: stock.createdAt,
                            updatedAt: stock.updatedAt
                        },
                        type: Sequelize.QueryTypes.INSERT,
                        transaction
                    }
                );

                // Delete the stock from EsimStocks
                await queryInterface.sequelize.query(
                    'DELETE FROM EsimStocks WHERE id = :id',
                    {
                        replacements: { id: stock.id },
                        type: Sequelize.QueryTypes.DELETE,
                        transaction
                    }
                );
            }

            await transaction.commit();
        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    },

    down: async (queryInterface, Sequelize) => {
        const transaction = await queryInterface.sequelize.transaction();

        try {
            // Get all stocks from history
            const historicalStocks = await queryInterface.sequelize.query(
                'SELECT * FROM EsimPlanStockHistory',
                { type: Sequelize.QueryTypes.SELECT, transaction }
            );

            // Move each stock back to EsimStocks
            for (const stock of historicalStocks) {
                await queryInterface.sequelize.query(
                    `INSERT INTO EsimStocks (
                        id, esimPlanId, iccid, smdpAddress, lpaString,
                        accessPointName, activationCode, phoneNumber,
                        orderId, orderDate, status, createdBy, createdAt, updatedAt
                    ) VALUES (
                        :esimStockId, :esimPlanId, :iccid, :smdpAddress, :lpaString,
                        :accessPointName, :activationCode, :phoneNumber,
                        :orderId, :orderDate, :status, :createdBy, :createdAt, :updatedAt
                    )`,
                    {
                        replacements: {
                            esimStockId: stock.esimStockId,
                            esimPlanId: stock.esimPlanId,
                            iccid: stock.iccid,
                            smdpAddress: stock.smdpAddress,
                            lpaString: stock.lpaString,
                            accessPointName: stock.accessPointName,
                            activationCode: stock.activationCode,
                            phoneNumber: stock.phoneNumber,
                            orderId: stock.orderId,
                            orderDate: stock.orderDate,
                            status: stock.status,
                            createdBy: stock.createdBy,
                            createdAt: stock.createdAt,
                            updatedAt: stock.updatedAt
                        },
                        type: Sequelize.QueryTypes.INSERT,
                        transaction
                    }
                );
            }

            // Clear the history table
            await queryInterface.sequelize.query(
                'DELETE FROM EsimPlanStockHistory',
                { type: Sequelize.QueryTypes.DELETE, transaction }
            );

            await transaction.commit();
        } catch (error) {
            await transaction.rollback();
            throw error;
        }
    }
};
