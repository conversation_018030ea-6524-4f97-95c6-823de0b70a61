const { Wallet, WalletTransaction, User } = require('../models');
const sequelize = require('../config/database');
const { v4: uuidv4 } = require('uuid');
const { Op } = require('sequelize');
const { sendLowBalanceEmail, sendWalletCreditEmail } = require('../utils/emailService');

// Get wallet details for a user
exports.getWallet = async (req, res) => {
    try {
        const requestedUserId = req.params.userId;
        
        // Check if user is accessing their own wallet or is an admin
        if (req.user.role !== 'admin' && req.user.id !== requestedUserId) {
            return res.status(403).json({ message: 'Access denied' });
        }

        // Use the requested user ID if provided (admin access), otherwise use the current user's ID
        const userId = requestedUserId || req.user.id;
        
        let wallet = await Wallet.findOne({
            where: { userId },
            include: [{
                model: WalletTransaction,
                as: 'transactions',
                limit: 5,
                order: [['createdAt', 'DESC']]
            }]
        });

        // If wallet doesn't exist, create one
        if (!wallet) {
            wallet = await Wallet.create({
                userId,
                balance: 0,
                maxBalance: 1000,
                balanceThreshold: 500,
                currencyCode: 'USD',
                isActive: true
            });
            
            wallet = await Wallet.findOne({
                where: { id: wallet.id },
                include: [{
                    model: WalletTransaction,
                    as: 'transactions',
                    limit: 5,
                    order: [['createdAt', 'DESC']]
                }]
            });
        }

        // Get all completed transactions to calculate totals
        const allTransactions = await WalletTransaction.findAll({
            where: {
                walletId: wallet.id,
                status: 'completed'
            }
        });

        // Calculate cumulative totals
        const totalTopup = allTransactions
            .filter(t => t.type === 'credit')
            .reduce((sum, t) => sum + Number(t.amount), 0);

        const totalSpent = allTransactions
            .filter(t => t.type === 'debit')
            .reduce((sum, t) => sum + Number(t.amount), 0);

        // Convert balance and maxBalance to numbers before sending
        const response = wallet.toJSON();
        response.balance = Number(response.balance);
        response.maxBalance = Number(response.maxBalance);
        response.balanceThreshold = Number(response.balanceThreshold);
        response.stats = {
            totalTopup,
            totalSpent
        };
        
        // Emit wallet update event
        const io = req.app.get('io');
        if (io) {
            io.to(`user_${userId}`).emit('walletUpdate', {
                balance: response.balance,
                maxBalance: response.maxBalance,
                balanceThreshold: response.balanceThreshold
            });
        }
        
        res.json(response);
    } catch (error) {
        console.error('Error fetching wallet:', error);
        res.status(500).json({ message: 'Error fetching wallet' });
    }
};

// Set balance threshold for a wallet
exports.setBalanceThreshold = async (req, res) => {
    const t = await sequelize.transaction();
    
    try {
        const userId = req.user.id;
        const { threshold } = req.body;

        // Convert threshold to number and validate
        const newThreshold = Number(threshold);
        if (isNaN(newThreshold) || newThreshold < 0) {
            await t.rollback();
            return res.status(400).json({ message: 'Invalid threshold value' });
        }

        // Find wallet and include user email for notification
        const wallet = await Wallet.findOne({ 
            where: { userId },
            include: [{
                model: User,
                as: 'user',
                attributes: ['email']
            }],
            transaction: t
        });

        if (!wallet) {
            await t.rollback();
            return res.status(404).json({ message: 'Wallet not found' });
        }

        await wallet.update({ 
            balanceThreshold: newThreshold 
        }, { transaction: t });

        await t.commit();

        // Check if current balance is below new threshold and send notification
        if (Number(wallet.balance) < newThreshold && wallet.user?.email) {
            await sendLowBalanceEmail(wallet.user.email, Number(wallet.balance), newThreshold);
        }

        // Emit wallet update event
        await emitWalletUpdate(req, userId);

        res.json({ 
            message: 'Balance threshold updated successfully',
            balanceThreshold: newThreshold
        });
    } catch (error) {
        await t.rollback();
        console.error('Error setting balance threshold:', error);
        res.status(500).json({ message: 'Error updating balance threshold' });
    }
};

// Set max balance for a wallet (admin only)
exports.setMaxBalance = async (req, res) => {
    const t = await sequelize.transaction();
    
    try {
        const { userId } = req.params;
        const { maxBalance } = req.body;

        // Convert maxBalance to number and validate
        const newMaxBalance = Number(maxBalance);
        if (isNaN(newMaxBalance) || newMaxBalance <= 0) {
            await t.rollback();
            return res.status(400).json({ message: 'Invalid maximum balance' });
        }

        const wallet = await Wallet.findOne({ 
            where: { userId },
            transaction: t
        });
        
        if (!wallet) {
            await t.rollback();
            return res.status(404).json({ message: 'Wallet not found' });
        }

        const currentBalance = Number(wallet.balance);
        // Check if new max balance is less than current balance
        if (newMaxBalance < currentBalance) {
            await t.rollback();
            return res.status(400).json({ 
                message: 'New maximum balance cannot be less than current balance' 
            });
        }

        await wallet.update({ 
            maxBalance: newMaxBalance,
            updatedAt: new Date()
        }, { transaction: t });

        await t.commit();

        // Emit wallet update event
        await emitWalletUpdate(req, userId);

        const response = wallet.toJSON();
        response.balance = Number(response.balance);
        response.maxBalance = Number(response.maxBalance);

        res.json({
            maxBalance: response.maxBalance,
            message: 'Maximum balance updated successfully'
        });
    } catch (error) {
        await t.rollback();
        console.error('Error setting max balance:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

// Add funds to user wallet (admin only)
exports.addFunds = async (req, res) => {
    const t = await sequelize.transaction();
    
    try {
        const { userId } = req.params;
        const { amount, description } = req.body;

        // Validate amount
        const creditAmount = Number(amount);
        if (isNaN(creditAmount) || creditAmount <= 0) {
            await t.rollback();
            return res.status(400).json({ message: 'Invalid amount' });
        }

        // Find wallet and include user for notifications
        const wallet = await Wallet.findOne({ 
            where: { userId },
            include: [{
                model: User,
                as: 'user',
                attributes: ['email']
            }],
            transaction: t
        });

        if (!wallet) {
            await t.rollback();
            return res.status(404).json({ message: 'Wallet not found' });
        }

        // Check if adding funds would exceed max balance
        const newBalance = Number(wallet.balance) + creditAmount;
        if (newBalance > Number(wallet.maxBalance)) {
            await t.rollback();
            return res.status(400).json({ 
                message: `Cannot add funds. Maximum balance limit is ${wallet.maxBalance}`
            });
        }

        // Create transaction record
        const transaction = await WalletTransaction.create({
            id: uuidv4(),
            walletId: wallet.id,
            type: 'credit',
            amount: creditAmount,
            description: description || 'Funds added successfully',
            status: 'completed',
            metadata: {
                addedBy: req.user.id
            }
        }, { transaction: t });

        // Update wallet balance
        await wallet.update({ 
            balance: newBalance 
        }, { transaction: t });

        await t.commit();

        // Send notifications
        if (wallet.user?.email) {
            // Send credit notification
            await sendWalletCreditEmail(wallet.user.email, creditAmount, newBalance);
            
            // Send low balance alert if needed
            if (newBalance < Number(wallet.balanceThreshold)) {
                await sendLowBalanceEmail(wallet.user.email, newBalance, Number(wallet.balanceThreshold));
            }
        }

        // Emit wallet update event
        await emitWalletUpdate(req, userId);

        res.json({
            message: 'Funds added successfully',
            transaction: {
                id: transaction.id,
                amount: creditAmount,
                newBalance
            }
        });
    } catch (error) {
        await t.rollback();
        console.error('Error adding funds:', error);
        res.status(500).json({ message: 'Error adding funds to wallet' });
    }
};

// Get transaction history
exports.getTransactions = async (req, res) => {
    try {
        const { userId } = req.params;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;

        // Check if user is accessing their own transactions or is an admin
        if (req.user.role !== 'admin' && req.user.id !== userId) {
            return res.status(403).json({ message: 'Access denied' });
        }

        const wallet = await Wallet.findOne({ where: { userId } });
        if (!wallet) {
            return res.json({
                transactions: [],
                totalCount: 0,
                currentPage: page,
                totalPages: 0,
                hasMore: false
            });
        }

        const { count, rows: transactions } = await WalletTransaction.findAndCountAll({
            where: { walletId: wallet.id },
            order: [['createdAt', 'DESC']],
            limit,
            offset
        });

        // Format transaction amounts and calculate previous balance
        const formattedTransactions = transactions.map(t => {
            const json = t.toJSON();
            json.amount = Number(json.amount);
            json.currentBalance = Number(json.balance);
            json.previousBalance = json.type === 'credit' 
                ? Number(json.balance) - Number(json.amount)
                : Number(json.balance) + Number(json.amount);
            return json;
        });

        res.json({
            transactions: formattedTransactions,
            totalCount: count,
            currentPage: page,
            totalPages: Math.ceil(count / limit),
            hasMore: offset + transactions.length < count
        });
    } catch (error) {
        console.error('Error fetching transactions:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

// Export all transactions
exports.exportTransactions = async (req, res) => {
    try {
        const userId = req.user.id;
        
        // Find user's wallet
        const wallet = await Wallet.findOne({ where: { userId } });
        if (!wallet) {
            return res.json({ transactions: [] });
        }

        // Get all transactions for export
        const transactions = await WalletTransaction.findAll({
            where: { walletId: wallet.id },
            order: [['createdAt', 'DESC']]
        });

        // Format transaction amounts and calculate previous balance
        const formattedTransactions = transactions.map(t => {
            const json = t.toJSON();
            json.amount = Number(json.amount);
            json.currentBalance = Number(json.balance);
            json.previousBalance = json.type === 'credit' 
                ? Number(json.balance) - Number(json.amount)
                : Number(json.balance) + Number(json.amount);
            return json;
        });

        res.json({ transactions: formattedTransactions });
    } catch (error) {
        console.error('Error exporting transactions:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

// Helper function to emit wallet updates
const emitWalletUpdate = async (req, userId) => {
    try {
        const wallet = await Wallet.findOne({ where: { userId } });
        if (wallet) {
            const io = req.app.get('io');
            if (io) {
                io.to(`user_${userId}`).emit('walletUpdate', {
                    balance: Number(wallet.balance),
                    maxBalance: Number(wallet.maxBalance),
                    balanceThreshold: Number(wallet.balanceThreshold)
                });
            }
        }
    } catch (error) {
        console.error('Error emitting wallet update:', error);
    }
};
