import http from 'k6/http';
import { sleep } from 'k6';
import { CONFIG } from './config.js';
import { checkResponse, getAuthToken } from './helpers.js';

export const options = {
    thresholds: CONFIG.thresholds,
    scenarios: {
        knowledge_flow: CONFIG.scenarios.load
    }
};

export function setup() {
    const token = getAuthToken(http);
    return { 
        baseUrl: CONFIG.baseUrl,
        token: token
    };
}

export default function (data) {
    const baseUrl = data.baseUrl;
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${data.token}`
    };

    // Get all articles
    const articlesResponse = http.get(`${baseUrl}/api/knowledge-base/articles`, {
        headers: headers
    });
    checkResponse(articlesResponse);

    sleep(1);

    // Get articles by category
    const categoryResponse = http.get(`${baseUrl}/api/knowledge-base/articles?category=general`, {
        headers: headers
    });
    checkResponse(categoryResponse);

    sleep(1);

    // Search articles
    const searchResponse = http.get(`${baseUrl}/api/knowledge-base/articles/search?q=esim`, {
        headers: headers
    });
    checkResponse(searchResponse);

    sleep(1);

    // Get single article
    const articleId = articlesResponse.json('articles')[0]?.id || 'test-article-id';
    const singleArticleResponse = http.get(`${baseUrl}/api/knowledge-base/articles/${articleId}`, {
        headers: headers
    });
    checkResponse(singleArticleResponse);

    sleep(1);
}
