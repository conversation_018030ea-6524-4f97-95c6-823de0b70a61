'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('KnowledgeBase', {
      id: {
        type: Sequelize.STRING(36),
        primaryKey: true,
        allowNull: false
      },
      title: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      category: {
        type: Sequelize.ENUM('general', 'technical', 'billing', 'support', 'faq'),
        allowNull: false,
        defaultValue: 'general'
      },
      visibility: {
        type: Sequelize.ENUM('public', 'partner', 'admin'),
        allowNull: false,
        defaultValue: 'public'
      },
      status: {
        type: Sequelize.ENUM('draft', 'published', 'archived'),
        allowNull: false,
        defaultValue: 'draft'
      },
      authorId: {
        type: Sequelize.STRING(36),
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        }
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false
      }
    });

    await queryInterface.addIndex('KnowledgeBase', ['category']);
    await queryInterface.addIndex('KnowledgeBase', ['visibility']);
    await queryInterface.addIndex('KnowledgeBase', ['status']);
    await queryInterface.addIndex('KnowledgeBase', ['authorId']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('KnowledgeBase');
  }
};
