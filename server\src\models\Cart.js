const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const User = require('./User');
const EsimPlan = require('./EsimPlan');

const Cart = sequelize.define('Cart', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4
    },
    userId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        references: {
            model: User,
            key: 'id'
        }
    },
    esimPlanId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        references: {
            model: EsimPlan,
            key: 'id'
        }
    },
    quantity: {
        type: DataTypes.INTEGER,
        defaultValue: 1,
        validate: {
            min: 1
        }
    }
}, {
    tableName: 'cart',
    timestamps: true
});

// Associations
Cart.belongsTo(User, { foreignKey: 'userId' });
Cart.belongsTo(EsimPlan, { foreignKey: 'esimPlanId' });

module.exports = Cart;