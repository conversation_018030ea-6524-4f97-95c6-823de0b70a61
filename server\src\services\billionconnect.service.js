const axios = require('axios');
const crypto = require('crypto');

class BillionConnectService {
    constructor() {
        this.baseURL = process.env.BILLIONCONNECT_API_URL || 'https://api-flow-ts.billionconnect.com/Flow/saler/2.0/invoke';
        this.channelId = process.env.BILLIONCONNECT_CHANNEL_ID;
        this.appSecret = process.env.BILLIONCONNECT_APP_SECRET;
        
        if (!this.channelId || !this.appSecret) {
            console.warn('BillionConnect API credentials not configured');
        }
    }

    /**
     * Generate MD5 signature for BillionConnect API
     * @param {Object} requestBody - The request body object
     * @returns {string} MD5 hash signature
     */
    generateSignature(requestBody) {
        const bodyString = JSON.stringify(requestBody);
        const signatureString = this.appSecret + bodyString;
        return crypto.createHash('md5').update(signatureString, 'utf8').digest('hex');
    }

    /**
     * Get current time in GMT+8 timezone
     * @returns {string} Formatted time string YYYY-MM-DD HH:mm:ss
     */
    getGMT8Time() {
        const now = new Date();
        // Convert to GMT+8 (Beijing time)
        const gmt8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));
        return gmt8Time.toISOString().slice(0, 19).replace('T', ' ');
    }

    /**
     * Make API request to BillionConnect
     * @param {Object} requestBody - The request body
     * @returns {Promise<Object>} API response
     */
    async makeRequest(requestBody) {
        try {
            const signature = this.generateSignature(requestBody);
            
            const headers = {
                'Content-Type': 'application/json;charset=UTF-8',
                'x-channel-id': this.channelId,
                'x-sign-method': 'md5',
                'x-sign-value': signature
            };

            console.log('BillionConnect API Request:', {
                url: this.baseURL,
                headers: { ...headers, 'x-sign-value': '[HIDDEN]' },
                body: requestBody
            });

            const response = await axios.post(this.baseURL, requestBody, { headers });
            
            console.log('BillionConnect API Response:', {
                status: response.status,
                tradeCode: response.data?.tradeCode,
                tradeMsg: response.data?.tradeMsg
            });

            return this.handleResponse(response);
        } catch (error) {
            console.error('BillionConnect API Error:', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            throw error;
        }
    }

    /**
     * Handle API response and check for errors
     * @param {Object} response - Axios response object
     * @returns {Object} Response data
     */
    handleResponse(response) {
        if (!response.data) {
            throw new Error('No response data received');
        }

        const { tradeCode, tradeMsg, tradeData } = response.data;

        // Check if the request was successful
        if (tradeCode !== '1000') {
            throw new Error(`BillionConnect API Error: ${tradeMsg} (Code: ${tradeCode})`);
        }

        return tradeData;
    }

    /**
     * Get available commodities using F002 tradeType
     * @param {string} salesMethod - Sales method (default: "5" for distribution)
     * @param {string} language - Language code (default: "2" for English)
     * @returns {Promise<Array>} Array of available products
     */
    async getCommodities(salesMethod = "5", language = "2") {
        try {
            const requestBody = {
                tradeType: "F002",
                tradeTime: this.getGMT8Time(),
                tradeData: {
                    salesMethod,
                    language
                }
            };

            const commodities = await this.makeRequest(requestBody);
            
            if (!Array.isArray(commodities)) {
                console.error('Expected array of commodities but got:', typeof commodities);
                return [];
            }

            console.log(`Retrieved ${commodities.length} commodities from BillionConnect`);
            return commodities;
        } catch (error) {
            console.error('Error fetching BillionConnect commodities:', error);
            return [];
        }
    }

    /**
     * Transform BillionConnect product to our standard format
     * @param {Object} product - BillionConnect product object
     * @returns {Object} Standardized product object
     */
    transformProduct(product) {
        try {
            // BillionConnect uses highFlowSize field (in KB) to indicate data amount
            // -1 in highFlowSize means unlimited data
            let isUnlimited = false;
            let dataAmountKB = 0;
            let capacity = 0; // This will be used by provider factory to determine plan type

            const highFlowSizeValue = product.highFlowSize;
            console.log(`Processing BillionConnect product ${product.skuId}: highFlowSize = ${highFlowSizeValue}`);

            if (highFlowSizeValue !== undefined && highFlowSizeValue !== null && highFlowSizeValue !== '') {
                const parsedHighFlowSize = parseInt(highFlowSizeValue);
                if (!isNaN(parsedHighFlowSize)) {
                    // Check if this is an unlimited plan based on highFlowSize (-1)
                    if (parsedHighFlowSize === -1) {
                        isUnlimited = true;
                        capacity = -1; // Set capacity to -1 for unlimited plans
                        console.log(`   → Unlimited plan detected (highFlowSize = -1)`);
                    } else {
                        dataAmountKB = parsedHighFlowSize; // highFlowSize is already in KB
                        capacity = parsedHighFlowSize; // Use original value for capacity
                        console.log(`   → Fixed plan detected (highFlowSize = ${parsedHighFlowSize} KB)`);
                    }
                } else {
                    // If highFlowSize is invalid, treat as unlimited
                    isUnlimited = true;
                    capacity = -1;
                    console.log(`   → Invalid highFlowSize, treating as unlimited`);
                }
            } else {
                // If no valid data amount is provided, treat as unlimited
                isUnlimited = true;
                capacity = -1;
                console.log(`   → No highFlowSize provided, treating as unlimited`);
            }

            let dataAmount, dataUnit;
            if (isUnlimited) {
                dataAmount = null; // Set to null for unlimited plans to satisfy database constraints
                dataUnit = null; // Set to null for unlimited plans (database constraint requires both null or both not null)
            } else {
                const dataAmountMB = Math.round(dataAmountKB / 1024);
                const dataAmountGB = dataAmountMB >= 1024 ? (dataAmountMB / 1024).toFixed(1) : null;

                if (dataAmountGB && parseFloat(dataAmountGB) >= 1) {
                    dataAmount = parseFloat(dataAmountGB);
                    dataUnit = 'GB';
                } else if (dataAmountMB > 0) {
                    dataAmount = dataAmountMB;
                    dataUnit = 'MB';
                } else {
                    // If calculated data amount is 0 or invalid, treat as unlimited
                    dataAmount = null;
                    dataUnit = null;
                    capacity = -1;
                    isUnlimited = true;
                    console.log(`   → Calculated data amount is 0, treating as unlimited`);
                }
            }

            console.log(`   → Final values: isUnlimited=${isUnlimited}, capacity=${capacity}, dataAmount=${dataAmount}, dataUnit=${dataUnit}`);

            // Get primary country info
            const primaryCountry = product.country && product.country[0];
            const countryName = primaryCountry?.name || 'Unknown';
            const countryCode = primaryCountry?.mcc || '';
            
            // Get primary operator info
            const primaryOperator = primaryCountry?.operatorInfo && primaryCountry.operatorInfo[0];
            const networkName = primaryOperator?.operator || 'Unknown';
            const networkType = primaryOperator?.network || '4G';

            // Parse validity days
            const validityDays = parseInt(product.days) || 1;

            // Determine if hotspot is supported (0 = Available, 1 = Not Available)
            const hotspotSupported = product.hotspotSupport === "0";

            // Determine speed based on limitFlowSpeed (database uses 'Restricted' not 'Restrictive')
            const limitFlowSpeed = product.limitFlowSpeed;
            const speed = limitFlowSpeed && limitFlowSpeed !== '0' && limitFlowSpeed !== '' ? 'Restricted' : 'Unrestricted';

            return {
                id: product.skuId,
                externalSkuId: product.skuId,
                externalProductId: product.skuId,
                name: product.name,
                description: product.desc || product.name,
                planData: dataAmount,
                planDataUnit: dataUnit,
                capacity: capacity, // Add capacity field for plan type determination
                validityDays: validityDays,
                networkName: networkName,
                networkType: networkType,
                hotspot: hotspotSupported,
                top_up: false, // BillionConnect doesn't specify rechargeableProduct in F002
                isUnlimited: isUnlimited,
                speed: speed, // Add speed field
                country: {
                    id: countryCode,
                    name: countryName
                },
                supportedCountries: (product.country || []).map(c => c.mcc), // Extract country codes
                providerMetadata: {
                    skuId: product.skuId,
                    type: product.type,
                    highFlowSize: product.highFlowSize,
                    limitFlowSpeed: product.limitFlowSpeed,
                    hotspotSupport: product.hotspotSupport,
                    countries: product.country,
                    originalProduct: product,
                    isUnlimited: isUnlimited,
                    capacity: capacity
                }
            };
        } catch (error) {
            console.error('Error transforming BillionConnect product:', error);
            return null;
        }
    }

    /**
     * Get price for specific plans using F003 tradeType
     * @param {Array} planRequests - Array of plan request objects with skuId and copies
     * @param {string} salesMethod - Sales method (default: "5" for distribution)
     * @param {string} language - Language code (default: "2" for English)
     * @returns {Promise<Array>} Array of price information
     */
    async getPlanPrices(planRequests, salesMethod = "5", language = "2") {
        try {
            if (!Array.isArray(planRequests) || planRequests.length === 0) {
                throw new Error('planRequests must be a non-empty array');
            }

            // Validate plan requests format
            const validatedRequests = planRequests.map(request => {
                if (!request.skuId) {
                    throw new Error('Each plan request must have a skuId');
                }
                return {
                    skuId: request.skuId.toString(),
                    copies: request.copies || request.quantity || 1
                };
            });

            const requestBody = {
                tradeType: "F003",
                tradeTime: this.getGMT8Time(),
                tradeData: {
                    salesMethod,
                    language,
                    planList: validatedRequests
                }
            };

            const priceData = await this.makeRequest(requestBody);

            if (!Array.isArray(priceData)) {
                console.error('Expected array of price data but got:', typeof priceData);
                return [];
            }

            console.log(`Retrieved price information for ${priceData.length} plans from BillionConnect`);
            return priceData;
        } catch (error) {
            console.error('Error fetching BillionConnect plan prices:', error);
            return [];
        }
    }

    /**
     * Get price for a single plan
     * @param {string} skuId - The SKU ID of the plan
     * @param {number} quantity - Number of plans (default: 1)
     * @param {string} salesMethod - Sales method (default: "5" for distribution)
     * @param {string} language - Language code (default: "2" for English)
     * @returns {Promise<Object|null>} Price information object or null
     */
    async getSinglePlanPrice(skuId, quantity = 1, salesMethod = "5", language = "2") {
        try {
            const priceData = await this.getPlanPrices([{ skuId, copies: quantity }], salesMethod, language);

            // Find the plan with matching SKU ID
            const planPrice = priceData.find(plan => plan.skuId === skuId.toString());

            if (!planPrice) {
                console.log(`No price data found for SKU: ${skuId}`);
                return null;
            }

            // Transform the price data for the requested quantity
            return this.transformPriceData(planPrice, quantity);
        } catch (error) {
            console.error('Error fetching single plan price:', error);
            return null;
        }
    }

    /**
     * Convert CNY to USD using current exchange rate
     * @param {number} cnyAmount - Amount in Chinese Yuan
     * @returns {number} Amount in USD
     */
    convertCNYToUSD(cnyAmount) {
        // Current approximate exchange rate: 1 USD = 7.1 CNY (as of 2024)
        // This should ideally be fetched from a real-time exchange rate API
        const CNY_TO_USD_RATE = 0.14; // 1 CNY = 0.14 USD (approximately)
        return Number((cnyAmount * CNY_TO_USD_RATE).toFixed(2));
    }

    /**
     * Transform BillionConnect price data to our standard format
     * @param {Object} planPriceData - BillionConnect plan price data object with skuId and price array
     * @param {number} requestedQuantity - The requested quantity to find price for
     * @returns {Object} Standardized price object
     */
    transformPriceData(planPriceData, requestedQuantity = 1) {
        try {
            if (!planPriceData.price || !Array.isArray(planPriceData.price)) {
                console.error('Invalid price data structure:', planPriceData);
                return null;
            }

            // Find the price tier for the requested quantity
            let selectedPrice = null;

            // Look for exact quantity match first
            selectedPrice = planPriceData.price.find(p => parseInt(p.copies) === requestedQuantity);

            // If no exact match, find the closest lower quantity or use the first one
            if (!selectedPrice) {
                const sortedPrices = planPriceData.price
                    .filter(p => parseInt(p.copies) <= requestedQuantity)
                    .sort((a, b) => parseInt(b.copies) - parseInt(a.copies));

                selectedPrice = sortedPrices[0] || planPriceData.price[0];
            }

            if (!selectedPrice) {
                console.error('No price data found for plan:', planPriceData.skuId);
                return null;
            }

            // Convert prices from CNY to USD
            const settlementPriceCNY = parseFloat(selectedPrice.settlementPrice) || 0;
            const retailPriceCNY = parseFloat(selectedPrice.retailPrice) || 0;
            const copies = parseInt(selectedPrice.copies) || 1;

            const settlementPriceUSD = this.convertCNYToUSD(settlementPriceCNY);
            const retailPriceUSD = this.convertCNYToUSD(retailPriceCNY);

            return {
                skuId: planPriceData.skuId,
                externalSkuId: planPriceData.skuId,
                buyingPrice: settlementPriceUSD,
                retailPrice: retailPriceUSD,
                quantity: copies,
                requestedQuantity: requestedQuantity,
                currency: 'USD', // Converted from CNY to USD
                totalPrice: settlementPriceUSD * requestedQuantity,
                pricePerUnit: settlementPriceUSD,
                allPriceTiers: planPriceData.price.map(p => ({
                    copies: parseInt(p.copies),
                    settlementPrice: this.convertCNYToUSD(parseFloat(p.settlementPrice)),
                    retailPrice: this.convertCNYToUSD(parseFloat(p.retailPrice)),
                    originalSettlementPriceCNY: parseFloat(p.settlementPrice),
                    originalRetailPriceCNY: parseFloat(p.retailPrice)
                })),
                providerMetadata: {
                    originalPriceData: planPriceData,
                    selectedPriceTier: selectedPrice,
                    originalPriceCNY: {
                        settlementPrice: settlementPriceCNY,
                        retailPrice: retailPriceCNY
                    },
                    exchangeRate: 0.14,
                    provider: 'billionconnect'
                }
            };
        } catch (error) {
            console.error('Error transforming BillionConnect price data:', error);
            return null;
        }
    }

    /**
     * Get prices for multiple plans and transform them
     * @param {Array} planRequests - Array of plan request objects
     * @returns {Promise<Array>} Array of standardized price objects
     */
    async getTransformedPrices(planRequests) {
        try {
            const priceData = await this.getPlanPrices(planRequests);

            // Create a map of requested quantities for each SKU
            const quantityMap = new Map();
            planRequests.forEach(request => {
                quantityMap.set(request.skuId.toString(), request.copies || 1);
            });

            const transformedPrices = priceData
                .map(planPrice => {
                    const requestedQuantity = quantityMap.get(planPrice.skuId) || 1;
                    return this.transformPriceData(planPrice, requestedQuantity);
                })
                .filter(price => price !== null);

            console.log(`Transformed ${transformedPrices.length} BillionConnect price records`);
            return transformedPrices;
        } catch (error) {
            console.error('Error getting transformed BillionConnect prices:', error);
            return [];
        }
    }

    /**
     * Get all products and transform them to our standard format
     * @returns {Promise<Array>} Array of standardized products
     */
    async getProducts() {
        try {
            const commodities = await this.getCommodities();

            const transformedProducts = commodities
                .map(product => this.transformProduct(product))
                .filter(product => product !== null);

            console.log(`Transformed ${transformedProducts.length} BillionConnect products`);
            return transformedProducts;
        } catch (error) {
            console.error('Error getting BillionConnect products:', error);
            return [];
        }
    }

    /**
     * Get products with their current prices
     * @param {Array} skuIds - Optional array of specific SKU IDs to get prices for
     * @returns {Promise<Array>} Array of products with price information
     */
    async getProductsWithPrices(skuIds = null) {
        try {
            const products = await this.getProducts();

            if (products.length === 0) {
                return [];
            }

            // Filter products if specific SKU IDs are requested
            const targetProducts = skuIds ?
                products.filter(product => skuIds.includes(product.externalSkuId)) :
                products;

            if (targetProducts.length === 0) {
                console.log('No products found for the requested SKU IDs');
                return [];
            }

            // Prepare price requests
            const priceRequests = targetProducts.map(product => ({
                skuId: product.externalSkuId,
                copies: 1
            }));

            // Get prices for all products
            const priceData = await this.getTransformedPrices(priceRequests);

            // Create a map of SKU ID to price data
            const priceMap = new Map();
            priceData.forEach(price => {
                priceMap.set(price.skuId, price);
            });

            // Combine products with their prices
            const productsWithPrices = targetProducts.map(product => {
                const priceInfo = priceMap.get(product.externalSkuId);
                return {
                    ...product,
                    buyingPrice: priceInfo ? priceInfo.buyingPrice : 0,
                    priceInfo: priceInfo || null,
                    hasPriceData: !!priceInfo
                };
            });

            console.log(`Retrieved ${productsWithPrices.length} products with price information`);
            return productsWithPrices;
        } catch (error) {
            console.error('Error getting BillionConnect products with prices:', error);
            return [];
        }
    }
}

module.exports = new BillionConnectService();
