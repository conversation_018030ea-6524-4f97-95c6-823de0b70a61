const request = require('supertest');
const bcrypt = require('bcrypt');
const { User } = require('../../../src/models');
const sequelize = require('../../config/database');
const app = require('../../../app');

describe('Auth Controller Tests', () => {
    let server;

    beforeAll(async () => {
        try {
            await sequelize.authenticate();
            await sequelize.sync({ force: true });
            server = app.listen(0);
        } catch (error) {
            console.error('Test setup failed:', error);
            throw error;
        }
    });

    afterAll(async () => {
        if (server) await server.close();
        await sequelize.close();
    });

    beforeEach(async () => {
        await User.destroy({ where: {}, force: true });
    });

    describe('POST /api/auth/login', () => {
        it('should login successfully with valid credentials', async () => {
            // Create a test user
            const hashedPassword = await bcrypt.hash('password123', 10);
            await User.create({
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'partner',
                isActive: true
            });

            const response = await request(server)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'password123'
                });

            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('token');
            expect(response.body).toHaveProperty('user');
            expect(response.body.user.email).toBe('<EMAIL>');
        });

        it('should fail with invalid password', async () => {
            const hashedPassword = await bcrypt.hash('password123', 10);
            await User.create({
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'partner',
                isActive: true
            });

            const response = await request(server)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'wrongpassword'
                });

            expect(response.status).toBe(401);
            expect(response.body).toHaveProperty('message', 'Invalid credentials');
        });

        it('should fail with non-existent user', async () => {
            const response = await request(server)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'password123'
                });

            expect(response.status).toBe(401);
            expect(response.body).toHaveProperty('message', 'Invalid credentials');
        });

        it('should fail with inactive user', async () => {
            const hashedPassword = await bcrypt.hash('password123', 10);
            await User.create({
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'partner',
                isActive: false
            });

            const response = await request(server)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'password123'
                });

            expect(response.status).toBe(401);
            expect(response.body).toHaveProperty('message', 'Account is inactive');
        });
    });

    describe('POST /api/auth/register', () => {
        it('should register a new partner successfully', async () => {
            const response = await request(server)
                .post('/api/auth/register')
                .send({
                    email: '<EMAIL>',
                    password: 'password123',
                    companyName: 'Test Company',
                    firstName: 'John',
                    lastName: 'Doe',
                    phone: '**********'
                });

            expect(response.status).toBe(201);
            expect(response.body).toHaveProperty('message', 'Registration successful');
            
            // Verify user was created
            const user = await User.findOne({ where: { email: '<EMAIL>' } });
            expect(user).toBeTruthy();
            expect(user.role).toBe('partner');
            expect(user.isActive).toBe(false); // Should be inactive until approved
        });

        it('should fail with existing email', async () => {
            // Create existing user
            const hashedPassword = await bcrypt.hash('password123', 10);
            await User.create({
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'partner',
                isActive: true
            });

            const response = await request(server)
                .post('/api/auth/register')
                .send({
                    email: '<EMAIL>',
                    password: 'password123',
                    companyName: 'Test Company',
                    firstName: 'John',
                    lastName: 'Doe',
                    phone: '**********'
                });

            expect(response.status).toBe(400);
            expect(response.body).toHaveProperty('message', 'Email already registered');
        });

        it('should fail with invalid email format', async () => {
            const response = await request(server)
                .post('/api/auth/register')
                .send({
                    email: 'invalidemail',
                    password: 'password123',
                    companyName: 'Test Company',
                    firstName: 'John',
                    lastName: 'Doe',
                    phone: '**********'
                });

            expect(response.status).toBe(400);
            expect(response.body).toHaveProperty('message');
            expect(response.body.message.toLowerCase()).toContain('email');
        });

        it('should fail with weak password', async () => {
            const response = await request(server)
                .post('/api/auth/register')
                .send({
                    email: '<EMAIL>',
                    password: '123', // Too short
                    companyName: 'Test Company',
                    firstName: 'John',
                    lastName: 'Doe',
                    phone: '**********'
                });

            expect(response.status).toBe(400);
            expect(response.body).toHaveProperty('message');
            expect(response.body.message.toLowerCase()).toContain('password');
        });
    });

    describe('POST /api/auth/forgot-password', () => {
        it('should generate reset token for existing user', async () => {
            // Create a test user
            const hashedPassword = await bcrypt.hash('password123', 10);
            await User.create({
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'partner',
                isActive: true
            });

            const response = await request(server)
                .post('/api/auth/forgot-password')
                .send({
                    email: '<EMAIL>'
                });

            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('message', 'Password reset instructions sent');
        });

        it('should not reveal if email does not exist', async () => {
            const response = await request(server)
                .post('/api/auth/forgot-password')
                .send({
                    email: '<EMAIL>'
                });

            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('message', 'Password reset instructions sent');
        });
    });
});
