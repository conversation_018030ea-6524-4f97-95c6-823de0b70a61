import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import api from '@/lib/axios';
import { useToast } from "@/components/ui/use-toast";
import { ArrowLeft } from 'lucide-react';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
    SelectGroup
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const formSchema = z.object({
    firstName: z.string().min(2, 'First name is required'),
    lastName: z.string().min(2, 'Last name is required'),
    email: z.string().email('Invalid email'),
    alternateEmail: z.string().email('Invalid alternate email').optional().or(z.literal('')),
    countryId: z.string().min(2, 'Country is required'),
    phoneNumber: z.string().min(10, 'Phone number is required'),
    alternatePhoneNumber: z.string().optional().or(z.literal('')),
    password: z.string().min(6, 'Password must be at least 6 characters'),
    businessName: z.string().min(2, 'Business name is required'),
    businessEmail: z.string().email('Invalid business email'),
    billingAddressLine1: z.string().min(1, 'Billing address line 1 is required'),
    billingAddressLine2: z.string().optional().or(z.literal('')),
    billingCity: z.string().optional(),
    billingProvince: z.string().optional().or(z.literal('')),
    billingCountryId: z.string().optional().or(z.literal('')),
    billingPostalCode: z.string().optional().or(z.literal('')), 
    markupPercentage: z.string()
        .min(1, 'Markup percentage is required')
        .transform((val) => Number(val))
        .refine((val) => !isNaN(val), {
            message: 'Must be a valid number'
        })
        .refine((val) => val >= 0 && val <= 100, {
            message: 'Must be between 0 and 100'
        })
});


export default function AddPartner() {
    const [countries, setCountries] = useState([]);
    const [loading, setLoading] = useState(false);
    const { toast } = useToast();
    const navigate = useNavigate();

    const form = useForm({
        resolver: zodResolver(formSchema),
        defaultValues: {
            firstName: '',
            lastName: '',
            email: '',
            alternateEmail: '',
            countryId: '',
            phoneNumber: '',
            alternatePhoneNumber: '',
            password: '',
            businessName: '',
            businessEmail: '',
            billingAddressLine1: '',
            billingAddressLine2: '',
            billingCity: '',
            billingProvince: '',
            billingCountryId: '',
            billingPostalCode: '',
            markupPercentage: ''
        }
    });

    useEffect(() => {
        fetchCountries();
    }, []);

    const fetchCountries = async () => {
        try {
            const response = await api.get('/api/countries');
            setCountries(Array.isArray(response.data) ? response.data : []);
        } catch (error) {
            console.error('Error fetching countries:', error);
            toast({
                title: "Error",
                description: "Failed to fetch countries",
                variant: "destructive"
            });
        }
    };

    const onSubmit = async (data) => {
        setLoading(true);
        try {
            // Convert empty string billingCountryId to null
            const formData = {
                ...data,
                role: 'partner',
                billingCountryId: data.billingCountryId || null
            };
            
            await api.post('/api/partners', formData);
            toast({
                title: "Success",
                description: "Partner created successfully"
            });
            navigate('/admin/partners');
        } catch (error) {
            toast({
                title: "Error",
                description: error.response?.data?.message || "Failed to create partner",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="h-full flex flex-col gap-6 p-6">
            <div className="flex items-center gap-4">
            <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => navigate('/admin/partners')}
                >
                    <ArrowLeft className="h-4 w-4" />
                </Button>
            <div>
                <h1 className="text-2xl font-bold">Add New Partner</h1>
                <p className="text-gray-600 mt-1">Create a new eSIM partner account</p>
            </div>
            </div>
            <div className="bg-white rounded-lg shadow-sm border p-6">
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Account Information */}
                            <div className="space-y-4">
                                <h2 className="text-lg font-semibold">Account Information</h2>


                                <FormField
                                    control={form.control}
                                    name="email"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Email *</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} type="email" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="password"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Password *</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} type="password" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="firstName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>First Name *</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="lastName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Last Name *</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="alternateEmail"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Alternate Email (Optional)</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} type="email" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="markupPercentage"
                                    render={({ field: { value, onChange, ...field } }) => (
                                        <FormItem>
                                            <FormLabel>Markup % *</FormLabel>
                                            <FormControl>
                                                <Input
                                                    id={`${field.name}-input`}
                                                    type="number"
                                                    min="0"
                                                    max="100"
                                                    step="0.01"
                                                    value={value || ''}
                                                    onChange={(e) => onChange(e.target.value === '' ? '' : e.target.value)}
                                                    {...field}
                                                    placeholder="Enter markup percentage (0-100)"
                                                />
                                            </FormControl>
                                            <FormMessage />
                                            <span className="block text-xs text-gray-500">
                                                Applied when no specific selling price is set
                                            </span>
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="countryId"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Country *</FormLabel>
                                            <Select
                                                value={field.value}
                                                onValueChange={field.onChange}
                                                required
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="w-full" id={`${field.name}-select`}>
                                                        <SelectValue placeholder="Select country" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent className="max-h-[200px] overflow-y-auto">
                                                    <SelectGroup>
                                                        {countries.map((country) => (
                                                            <SelectItem
                                                                key={country.id}
                                                                value={country.id}
                                                                className="cursor-pointer"
                                                            >
                                                                {country.name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectGroup>
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="phoneNumber"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Phone Number *</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="alternatePhoneNumber"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Alternate Phone Number (Optional)</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            {/* Business Information */}
                            <div className="space-y-4">
                                <h2 className="text-lg font-semibold">Business Information</h2>

                                <FormField
                                    control={form.control}
                                    name="businessName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Business Name *</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="businessEmail"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Business Email *</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} type="email" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="billingAddressLine1"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Billing Address Line 1 *</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="billingAddressLine2"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Billing Address Line 2 (Optional)</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="billingCity"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>City</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="billingProvince"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Province/State</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="billingCountryId"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Billing Country (Optional)</FormLabel>
                                            <Select
                                                value={field.value}
                                                onValueChange={field.onChange}
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="w-full" id={`${field.name}-select`}>
                                                        <SelectValue placeholder="Select country" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent className="max-h-[200px] overflow-y-auto">
                                                    <SelectGroup>
                                                        {countries.map((country) => (
                                                            <SelectItem
                                                                key={country.id}
                                                                value={country.id}
                                                                className="cursor-pointer"
                                                            >
                                                                {country.name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectGroup>
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="billingPostalCode"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Postal Code</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </div>

                        <div className="flex justify-end gap-4">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => navigate('/admin/partners')}
                            >
                                Cancel
                            </Button>
                            <Button type="submit" disabled={loading}>
                                {loading ? 'Creating...' : 'Create Partner'}
                            </Button>
                        </div>
                    </form>
                </Form>
            </div>
        </div>
    );
}
