const sequelize = require('../config/database');

async function checkMarkupColumn() {
    try {
        const [results] = await sequelize.query(`
            SELECT 
                COLUMN_NAME,
                COLUMN_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                COLUMN_COMMENT
            FROM 
                INFORMATION_SCHEMA.COLUMNS 
            WHERE 
                TABLE_SCHEMA = 'esim_platform' 
                AND TABLE_NAME = 'Users'
                AND COLUMN_NAME = 'markupPercentage';
        `);
        
        console.log('Markup Percentage Column Definition:');
        console.log(JSON.stringify(results[0], null, 2));
        
        process.exit(0);
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}

checkMarkupColumn();
