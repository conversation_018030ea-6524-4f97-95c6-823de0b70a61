import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { 
    Wallet as WalletIcon, 
    Plus, 
    ArrowUpRight, 
    ArrowDownRight, 
    History,
    Download,
    Search,
    Filter,
    User,
    Package,
    Settings,
    Pencil,
    AlertCircle
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import api from '@/lib/axios';
import { format } from 'date-fns';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';

const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};

const getStatusVariant = (status) => {
    const variants = {
        completed: "success",
        pending: "warning",
        failed: "destructive"
    };
    return variants[status] || "secondary";
};

export default function Wallet() {
    const [loading, setLoading] = useState(true);
    const [walletData, setWalletData] = useState({
        balance: 0,
        maxBalance: 1000,
        transactions: [],
        stats: {
            totalTopup: 0,
            totalSpent: 0
        },
        balanceThreshold: 0
    });
    const [transactions, setTransactions] = useState([]);
    const [transactionPage, setTransactionPage] = useState(1);
    const [hasMoreTransactions, setHasMoreTransactions] = useState(true);
    const [transactionLimit] = useState(10);
    const [showThresholdDialog, setShowThresholdDialog] = useState(false);
    const [thresholdValue, setThresholdValue] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isExporting, setIsExporting] = useState(false);
    const { toast } = useToast();

    useEffect(() => {
        fetchWalletData();
        fetchTransactions();
    }, [transactionPage]);

    const fetchWalletData = async () => {
        try {
            const response = await api.get('/api/partner/wallet');
            if (response.data) {
                setWalletData(response.data);
            }
        } catch (error) {
            // console.error('Error fetching wallet data:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to fetch wallet data"
            });
        } finally {
            setLoading(false);
        }
    };

    const fetchTransactions = async () => {
        try {
            const response = await api.get('/api/partner/wallet/transactions', {
                params: {
                    page: transactionPage,
                    limit: transactionLimit
                }
            });
            
            if (response.data) {
                setTransactions(response.data.transactions || []);
                setHasMoreTransactions(response.data.hasMore || false);
            }
        } catch (error) {
            // console.error('Error fetching transactions:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to fetch transactions"
            });
        }
    };

    const handleThresholdChange = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);

        try {
            const response = await api.post('/api/partner/wallet/threshold', {
                threshold: parseFloat(thresholdValue)
            });

            toast({
                title: "Success",
                description: response.data.message,
                variant: "success"
            });

            // Update wallet data to reflect new threshold
            fetchWalletData();
            setShowThresholdDialog(false);
        } catch (error) {
            // console.error('Error updating threshold:', error);
            toast({
                title: "Error",
                description: error.response?.data?.message || "Failed to update threshold",
                variant: "destructive"
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    useEffect(() => {
        if (walletData?.balanceThreshold) {
            setThresholdValue(walletData.balanceThreshold.toString());
        }
    }, [walletData?.balanceThreshold]);

    const exportTransactions = async () => {
        setIsExporting(true);
        try {
            // Fetch all transactions for export
            const response = await api.get('/api/partner/wallet/transactions/export');
            const transactions = response.data.transactions;

            // Prepare CSV data
            const csvData = [
                ['Transaction ID', 'Date', 'Type', 'Description', 'Previous Balance', 'Amount', 'Current Balance', 'Status'],
                ...transactions.map(t => [
                    t.id,
                    format(new Date(t.createdAt), 'MMM dd, yyyy'),
                    t.type,
                    t.description,
                    formatCurrency(t.previousBalance).replace('$', ''),
                    `${t.type === 'credit' ? '+' : '-'}${formatCurrency(t.amount).replace('$', '')}`,
                    formatCurrency(t.currentBalance).replace('$', ''),
                    t.status
                ])
            ];

            // Convert to CSV string
            const csvString = csvData.map(row => row.map(cell => 
                typeof cell === 'string' && cell.includes(',') ? `"${cell}"` : cell
            ).join(',')).join('\n');

            // Create and download file
            const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `wallet_transactions_${format(new Date(), 'yyyy-MM-dd')}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            toast({
                title: "Success",
                description: "Transaction history exported successfully",
                variant: "success"
            });
        } catch (error) {
            // console.error('Error exporting transactions:', error);
            toast({
                title: "Error",
                description: "Failed to export transactions",
                variant: "destructive"
            });
        } finally {
            setIsExporting(false);
        }
    };

    if (loading) {
        return (
            <div className="h-full flex flex-col gap-6 p-4 sm:p-6">
                <Skeleton className="h-8 w-48" />
                <Skeleton className="h-4 w-96" />
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <Card className="col-span-2">
                        <CardContent className="p-6">
                            <Skeleton className="h-24 w-full" />
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <Skeleton className="h-24 w-full" />
                        </CardContent>
                    </Card>
                </div>
            </div>
        );
    }

    return (
        <div className="h-full flex flex-col gap-6 p-4 sm:p-6">
            <div>
                <h1 className="text-xl sm:text-2xl font-bold tracking-tight">My Wallet</h1>
                <p className="text-sm sm:text-base text-muted-foreground mt-1">Manage your wallet balance and view transactions</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
                {/* Wallet Balance Card */}
                <Card className="col-span-1 lg:col-span-2 shadow-lg rounded-2xl overflow-hidden">
                    {/* Header with Black Gradient Background */}
                    <CardHeader className="p-5 sm:p-6 bg-gradient-to-r from-gray-800 to-gray-600 text-white">
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle className="text-lg sm:text-xl font-semibold">Available Balance</CardTitle>
                                <CardDescription className="text-sm text-white opacity-90">Your current wallet balance</CardDescription>
                            </div>
                            <div className="bg-white/10 p-3 rounded-xl">
                                <WalletIcon className="w-6 h-6 sm:w-7 sm:h-7 text-white" />
                            </div>
                        </div>
                    </CardHeader>

                    {/* Content Section */}
                    <CardContent className="p-5 sm:p-6">
                        <div className="flex items-start justify-between mb-6">
                            <div>
                                <p className="text-3xl sm:text-4xl font-bold text-gray-900">
                                    {formatCurrency(walletData.balance || 0)}
                                </p>
                                <p className="text-sm text-gray-500 mt-1">
                                    Maximum Balance: <span className="font-medium">{formatCurrency(walletData.maxBalance || 0)}</span>
                                </p>

                                {/* Balance Threshold Section */}
                                <div className="flex items-center gap-3 mt-6">
                                    <div className="text-lg text-gray-600">
                                        Balance Threshold: <span className="font-medium">{formatCurrency(walletData.balanceThreshold || 0)}</span>
                                    </div>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-9 w-9 p-0 bg-gray-200 hover:bg-gray-300 rounded-full transition"
                                        onClick={() => {
                                            setThresholdValue(walletData?.balanceThreshold?.toString() || '500');
                                            setShowThresholdDialog(true);
                                        }}
                                    >
                                        <Pencil className="h-5 w-5 text-gray-600" />
                                    </Button>
                                </div>

                                {/* Low Balance Alert */}
                                {walletData?.balance < walletData?.balanceThreshold && (
                                    <Alert variant="warning" className="mt-4 flex items-center bg-yellow-100 text-yellow-700 p-3 rounded-lg">
                                        <AlertCircle className="h-5 w-5 mr-2" />
                                        <AlertDescription>
                                            Your balance is below the threshold
                                        </AlertDescription>
                                    </Alert>
                                )}
                            </div>
                        </div>
                    </CardContent>

                    {/* Footer Buttons */}
                    <CardFooter className="p-5 sm:p-6 flex flex-col sm:flex-row gap-3">
                        <Button
                            variant="outline"
                            className="w-full sm:flex-1 flex items-center justify-center gap-2 text-sm bg-black transition rounded-lg shadow-sm"
                            onClick={exportTransactions}
                            disabled={isExporting}
                        >
                            {isExporting ? (
                                <>
                                    <span className="animate-spin">⏳</span>
                                    <span className="hidden sm:inline">Exporting...</span>
                                    <span className="sm:hidden">Export</span>
                                </>
                            ) : (
                                <>
                                    <Download className="w-5 h-5 text-white" />
                                    <span className="hidden sm:inline">Download Statement</span>
                                    <span className="sm:hidden">Download</span>
                                </>
                            )}
                        </Button>
                    </CardFooter>
                </Card>



                {/* Quick Stats */}
                <Card className="col-span-1 shadow-lg rounded-2xl overflow-hidden">
                    <CardHeader className="bg-gradient-to-r from-cyan-300 to-blue-100 p-4 sm:p-6">
                        <CardTitle className="text-lg sm:text-xl">Quick Stats</CardTitle>
                        <CardDescription>Your wallet activity</CardDescription>
                    </CardHeader>
                    <CardContent className="p-4 sm:p-6 space-y-4">
                        <div className="flex items-center justify-between p-2 sm:p-3 bg-primary/5 rounded-lg">
                            <div className="flex items-center gap-2 sm:gap-3">
                                <div className="bg-primary/10 p-1.5 sm:p-2 rounded-lg">
                                    <ArrowUpRight className="w-3 h-3 sm:w-4 sm:h-4 text-primary" />
                                </div>
                                <div>
                                    <div className="text-sm sm:text-base">Total Top-Up Added</div>
                                    <div className="text-base sm:text-lg font-semibold">
                                        {formatCurrency(walletData.stats.totalTopup)}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="flex items-center justify-between p-2 sm:p-3 bg-destructive/5 rounded-lg">
                            <div className="flex items-center gap-2 sm:gap-3">
                                <div className="bg-destructive/10 p-1.5 sm:p-2 rounded-lg">
                                    <ArrowDownRight className="w-3 h-3 sm:w-4 sm:h-4 text-destructive" />
                                </div>
                                <div>
                                    <div className="text-sm sm:text-base">Total Amount Spent</div>
                                    <div className="text-base sm:text-lg font-semibold">
                                        {formatCurrency(walletData.stats.totalSpent)}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Transactions Table */}
                <Card className=" col-span-1 lg:col-span-3">
                    <CardHeader className="bg-gradient-to-r from-emerald-100 to-green-100 p-4 sm:p-6">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                            <div>
                                <CardTitle className="text-lg sm:text-xl">Transaction History</CardTitle>
                                <CardDescription>Your recent wallet transactions</CardDescription>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent className="p-0 sm:p-6">
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100">
                                        <TableHead className="hidden sm:table-cell">Transaction ID</TableHead>
                                        <TableHead>Date</TableHead>
                                        <TableHead>Type</TableHead>
                                        <TableHead className="hidden sm:table-cell">Description</TableHead>
                                        <TableHead className="hidden sm:table-cell text-right">Previous Balance</TableHead>
                                        <TableHead className="text-right">Amount</TableHead>
                                        <TableHead className="hidden sm:table-cell text-right">Current Balance</TableHead>
                                        <TableHead>Status</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {transactions.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={8} className="text-center py-4">
                                                No transactions found
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        transactions.map((transaction) => (
                                            <TableRow key={transaction.id}>
                                                <TableCell className="hidden sm:table-cell font-mono text-xs">
                                                    {transaction.id}
                                                </TableCell>
                                                <TableCell className="text-xs sm:text-sm whitespace-nowrap">
                                                    {format(new Date(transaction.createdAt), 'MMM dd, yyyy')}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center gap-1 sm:gap-2">
                                                        {transaction.type === 'credit' ? (
                                                            <ArrowUpRight className="w-3 h-3 sm:w-4 sm:h-4 text-green-500" />
                                                        ) : (
                                                            <ArrowDownRight className="w-3 h-3 sm:w-4 sm:h-4 text-red-500" />
                                                        )}
                                                        <span className="text-xs sm:text-sm capitalize">{transaction.type}</span>
                                                    </div>
                                                </TableCell>
                                                <TableCell className="hidden sm:table-cell text-sm">
                                                    {transaction.description}
                                                </TableCell>
                                                <TableCell className="hidden sm:table-cell text-center whitespace-nowrap">
                                                    <span className="text-xs sm:text-sm text-gray-600">
                                                        {formatCurrency(transaction.previousBalance)}
                                                    </span>
                                                </TableCell>
                                                <TableCell className="text-right whitespace-nowrap">
                                                    <span className={`text-xs sm:text-sm ${transaction.type === 'credit' ? 'text-green-500' : 'text-red-500'}`}>
                                                        {transaction.type === 'credit' ? '+' : '-'}{formatCurrency(transaction.amount)}
                                                    </span>
                                                </TableCell>
                                                <TableCell className="hidden sm:table-cell text-center whitespace-nowrap">
                                                    <span className="text-xs sm:text-sm text-gray-900 font-medium">
                                                        {formatCurrency(transaction.currentBalance)}
                                                    </span>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant={getStatusVariant(transaction.status)} className="capitalize">
                                                        {transaction.status}
                                                    </Badge>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Pagination */}
                        <div className="flex justify-center mt-4 sm:mt-6 gap-2 p-4 sm:p-0">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setTransactionPage(prev => Math.max(1, prev - 1))}
                                disabled={transactionPage === 1}
                                className="text-xs sm:text-sm h-8"
                            >
                                Previous
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setTransactionPage(prev => prev + 1)}
                                disabled={!hasMoreTransactions}
                                className="text-xs sm:text-sm h-8"
                            >
                                Next
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Threshold Dialog */}
            <Dialog open={showThresholdDialog} onOpenChange={setShowThresholdDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Set Balance Threshold</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleThresholdChange} className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="threshold">Low Balance Alert Threshold</Label>
                            <div className="relative">
                                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">$</span>
                                <Input
                                    id="threshold"
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    value={thresholdValue}
                                    onChange={(e) => setThresholdValue(e.target.value)}
                                    className="pl-7"
                                    placeholder="Enter threshold amount"
                                    required
                                />
                            </div>
                            <p className="text-sm text-muted-foreground">
                                You'll receive an email notification when your balance falls below this amount.
                            </p>
                        </div>
                        <DialogFooter>
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setShowThresholdDialog(false)}
                                disabled={isSubmitting}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? 'Saving...' : 'Save Threshold'}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
}
