import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Wallet,
    ShoppingBag,
    ArrowRight,
    Signal,
    Globe,
    TrendingUp,
    Calendar,
    BarChart3,
    Activity,
    Sparkles
} from 'lucide-react';
import {
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
    Area,
    AreaChart
} from 'recharts';
import api from '@/lib/axios';

// Enhanced StatCard with modern design and animations
const StatCard = ({ title, value, icon: Icon, trend, onClick, loading, description, color }) => {
    // Define enhanced gradient colors with dark blue preference
    const gradients = {
        purple: 'from-purple-600 via-purple-700 to-indigo-800',
        blue: 'from-blue-700 via-blue-800 to-indigo-900',
        green: 'from-emerald-600 via-teal-700 to-cyan-800',
        orange: 'from-orange-600 via-amber-700 to-yellow-800',
    };

    const selectedGradient = gradients[color] || gradients.blue;

    return (
        <Card className="group overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 cursor-pointer border-0 bg-gradient-to-br from-white to-gray-50">
            <div className={`bg-gradient-to-br ${selectedGradient} p-6 text-white h-full relative overflow-hidden`} onClick={onClick}>
                {/* Animated background pattern */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl group-hover:scale-150 transition-transform duration-700"></div>

                <div className="flex items-start justify-between relative z-10">
                    <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                            <p className="text-sm font-semibold text-white/95 tracking-wide uppercase">{title}</p>
                            <Sparkles className="w-3 h-3 text-white/70 group-hover:text-white/90 transition-colors duration-300" />
                        </div>
                        {loading ? (
                            <Skeleton className="h-12 w-32 mt-2 bg-white/30 rounded-lg" />
                        ) : (
                            <>
                                <h3 className="text-4xl font-bold mt-2 mb-1 group-hover:scale-105 transition-transform duration-300">{value}</h3>
                                {description && (
                                    <p className="text-sm text-white/85 mt-2 leading-relaxed">{description}</p>
                                )}
                                {trend && (
                                    <div className="flex items-center mt-4 text-white/95 bg-white/20 px-3 py-1.5 rounded-full backdrop-blur-sm">
                                        {trend.type === 'up' ? (
                                            <TrendingUp className="w-4 h-4 mr-2 text-green-300" />
                                        ) : (
                                            <TrendingUp className="w-4 h-4 mr-2 transform rotate-180 text-red-300" />
                                        )}
                                        <p className="text-sm font-semibold">{trend.value}</p>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                    <div className="bg-white/25 p-4 rounded-2xl backdrop-blur-sm group-hover:bg-white/35 group-hover:scale-110 transition-all duration-300 shadow-lg">
                        <Icon className="w-7 h-7 group-hover:rotate-12 transition-transform duration-300" />
                    </div>
                </div>
            </div>
        </Card>
    );
};

// Time Period Selector Component
const TimePeriodSelector = ({ selectedPeriod, onPeriodChange }) => {
    const periods = [
        { value: '7d', label: '7 Days', icon: Calendar },
        { value: '30d', label: '30 Days', icon: BarChart3 },
        { value: '3m', label: '3 Months', icon: Activity },
        { value: '1y', label: '1 Year', icon: TrendingUp },
    ];

    return (
        <div className="flex gap-2 bg-white/80 backdrop-blur-sm p-1 rounded-xl border border-gray-200/50">
            {periods.map((period) => {
                const Icon = period.icon;
                return (
                    <button
                        key={period.value}
                        onClick={() => onPeriodChange(period.value)}
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                            selectedPeriod === period.value
                                ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg scale-105'
                                : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                        }`}
                    >
                        <Icon className="w-4 h-4" />
                        {period.label}
                    </button>
                );
            })}
        </div>
    );
};

export default function Dashboard() {
    const navigate = useNavigate();
    const { toast } = useToast();
    const [loading, setLoading] = useState(true);
    const [selectedPeriod, setSelectedPeriod] = useState('30d');
    const [stats, setStats] = useState({
        walletBalance: 0,
        totalOrders: 0,
        totalPlans: 0,
        last30DaysOrders: 0,
        monthlyTrends: [],
        recentOrders: []
    });

    const fetchDashboardStats = async (period = '30d') => {
        try {
            setLoading(true);
            const response = await api.get(`/api/partner/dashboard/stats?period=${period}`);
            setStats(response.data);
        } catch (error) {
            // console.error('Error fetching dashboard stats:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to fetch dashboard statistics"
            });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchDashboardStats(selectedPeriod);
    }, [selectedPeriod]);

    const handlePeriodChange = (period) => {
        setSelectedPeriod(period);
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-100/50">
            {/* Enhanced Header with Dark Blue Gradient */}
            <div className="relative overflow-hidden bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 p-8 rounded-2xl shadow-2xl mb-8">
                {/* Animated background elements */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20"></div>
                <div className="absolute -top-4 -right-4 w-32 h-32 bg-white/10 rounded-full blur-2xl"></div>
                <div className="absolute -bottom-4 -left-4 w-24 h-24 bg-blue-400/20 rounded-full blur-xl"></div>

                <div className="relative z-10 flex items-center justify-between">
                    <div>
                        <h1 className="text-4xl font-bold text-white mb-2 tracking-tight">
                            Partner Dashboard
                        </h1>
                        <p className="text-blue-100 text-lg leading-relaxed">
                            Welcome back! Here&apos;s your comprehensive eSIM services overview
                        </p>
                    </div>
                    <div className="hidden md:flex items-center gap-4">
                        <div className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-xl border border-white/30">
                            <p className="text-white/90 text-sm font-medium">
                                {format(new Date(), 'EEEE, MMMM d, yyyy')}
                            </p>
                        </div>
                        <Button
                            variant="outline"
                            className="bg-white/20 border-white/30 text-white hover:bg-white/30 backdrop-blur-sm"
                            onClick={() => fetchDashboardStats(selectedPeriod)}
                        >
                            <Activity className="w-4 h-4 mr-2" />
                            Refresh
                        </Button>
                    </div>
                </div>
            </div>

            <div className="px-6 pb-6 space-y-8">
                {/* Enhanced Quick Stats */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <StatCard
                        title="Wallet Balance"
                        value={`$${stats.walletBalance.toFixed(2)}`}
                        icon={Wallet}
                        description="Available funds for purchases"
                        onClick={() => navigate('/dashboard/wallet')}
                        loading={loading}
                        color="purple"
                    />
                    <StatCard
                        title="Total Orders"
                        value={stats.totalOrders}
                        icon={ShoppingBag}
                        description="Lifetime eSIM purchases"
                        trend={{
                            type: 'up',
                            value: `${stats.last30DaysOrders} new this month`
                        }}
                        onClick={() => navigate('/dashboard/orders')}
                        loading={loading}
                        color="blue"
                    />
                    <StatCard
                        title="Available Plans"
                        value={stats.totalPlans}
                        icon={Globe}
                        description="Active eSIM plans"
                        onClick={() => navigate('/dashboard/plans')}
                        loading={loading}
                        color="green"
                    />
                    <StatCard
                        title="Active Services"
                        value={stats.last30DaysOrders}
                        icon={Signal}
                        description="Orders in last 30 days"
                        onClick={() => navigate('/dashboard/orders')}
                        loading={loading}
                        color="orange"
                    />
                </div>

                {/* Enhanced Order History Chart */}
                <Card className="shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden bg-white/70 backdrop-blur-lg border-0">
                    <div className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 p-8 border-b relative overflow-hidden">
                        {/* Background decoration */}
                        <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent"></div>
                        <div className="absolute -top-4 -right-4 w-32 h-32 bg-white/10 rounded-full blur-2xl"></div>

                        <div className="relative z-10 flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                            <div>
                                <h2 className="text-2xl font-bold text-white mb-2 flex items-center gap-3">
                                    <BarChart3 className="w-6 h-6" />
                                    Order History Analytics
                                </h2>
                                <p className="text-blue-100 leading-relaxed">
                                    Comprehensive view of your eSIM purchase trends and patterns
                                </p>
                            </div>
                            <div className="flex flex-col sm:flex-row gap-4">
                                <TimePeriodSelector
                                    selectedPeriod={selectedPeriod}
                                    onPeriodChange={handlePeriodChange}
                                />
                                <Button
                                    variant="outline"
                                    className="bg-white/20 border-white/30 text-white hover:bg-white/30 backdrop-blur-sm"
                                    onClick={() => navigate('/dashboard/orders')}
                                >
                                    View All Orders
                                    <ArrowRight className="w-4 h-4 ml-2" />
                                </Button>
                            </div>
                        </div>
                    </div>

                    <div className="p-8 bg-gradient-to-br from-white to-blue-50/30">
                        <div className="h-[400px]">
                            {loading ? (
                                <div className="w-full h-full flex items-center justify-center">
                                    <div className="text-center">
                                        <Skeleton className="w-full h-full rounded-xl" />
                                        <p className="text-gray-500 mt-4">Loading analytics...</p>
                                    </div>
                                </div>
                            ) : stats.monthlyTrends.length === 0 ? (
                                <div className="w-full h-full flex items-center justify-center text-slate-500">
                                    <div className="text-center">
                                        <div className="bg-gradient-to-br from-blue-100 to-indigo-100 p-6 rounded-2xl mb-4 inline-block">
                                            <TrendingUp className="w-16 h-16 text-blue-400 mx-auto" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-gray-700 mb-2">No Data Available</h3>
                                        <p className="text-gray-500">Start making orders to see your analytics here</p>
                                    </div>
                                </div>
                            ) : (
                                <ResponsiveContainer width="100%" height="100%">
                                    <AreaChart data={stats.monthlyTrends} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                                        <defs>
                                            <linearGradient id="enhancedOrdersGradient" x1="0" y1="0" x2="0" y2="1">
                                                <stop offset="0%" stopColor="#3B82F6" stopOpacity={0.9}/>
                                                <stop offset="25%" stopColor="#6366F1" stopOpacity={0.7}/>
                                                <stop offset="50%" stopColor="#8B5CF6" stopOpacity={0.5}/>
                                                <stop offset="75%" stopColor="#A855F7" stopOpacity={0.3}/>
                                                <stop offset="100%" stopColor="#C084FC" stopOpacity={0.1}/>
                                            </linearGradient>
                                            <filter id="glow">
                                                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                                                <feMerge>
                                                    <feMergeNode in="coloredBlur"/>
                                                    <feMergeNode in="SourceGraphic"/>
                                                </feMerge>
                                            </filter>
                                        </defs>
                                        <CartesianGrid
                                            strokeDasharray="3 3"
                                            stroke="#E2E8F0"
                                            strokeOpacity={0.6}
                                            horizontal={true}
                                            vertical={false}
                                        />
                                        <XAxis
                                            dataKey="month"
                                            tickFormatter={(value) => format(new Date(value), 'MMM yyyy')}
                                            interval="preserveStartEnd"
                                            axisLine={false}
                                            tickLine={false}
                                            style={{ fontSize: '13px', fill: '#64748B', fontWeight: '500' }}
                                        />
                                        <YAxis
                                            allowDecimals={false}
                                            domain={[0, 'auto']}
                                            axisLine={false}
                                            tickLine={false}
                                            style={{ fontSize: '13px', fill: '#64748B', fontWeight: '500' }}
                                        />
                                        <Tooltip
                                            labelFormatter={(value) => format(new Date(value), 'MMMM yyyy')}
                                            formatter={(value) => [`${value} orders`, 'Orders']}
                                            contentStyle={{
                                                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                                border: 'none',
                                                borderRadius: '16px',
                                                padding: '16px',
                                                boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                                                backdropFilter: 'blur(10px)',
                                                fontSize: '14px',
                                                fontWeight: '500'
                                            }}
                                            labelStyle={{ color: '#1F2937', fontWeight: '600', marginBottom: '8px' }}
                                        />
                                        <Area
                                            type="monotone"
                                            dataKey="count"
                                            name="Orders"
                                            stroke="#3B82F6"
                                            strokeWidth={3}
                                            fillOpacity={1}
                                            fill="url(#enhancedOrdersGradient)"
                                            dot={{
                                                r: 6,
                                                strokeWidth: 3,
                                                stroke: '#3B82F6',
                                                fill: '#FFFFFF',
                                                filter: 'url(#glow)'
                                            }}
                                            activeDot={{
                                                r: 10,
                                                strokeWidth: 3,
                                                stroke: '#3B82F6',
                                                fill: '#FFFFFF',
                                                filter: 'url(#glow)'
                                            }}
                                        />
                                    </AreaChart>
                                </ResponsiveContainer>
                            )}
                        </div>
                    </div>
                </Card>

                {/* Enhanced Recent Orders */}
                <Card className="shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden bg-white/70 backdrop-blur-lg border-0">
                    <div className="bg-gradient-to-br from-emerald-600 via-teal-700 to-cyan-800 p-8 border-b relative overflow-hidden">
                        {/* Background decoration */}
                        <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent"></div>
                        <div className="absolute -top-4 -right-4 w-32 h-32 bg-white/10 rounded-full blur-2xl"></div>

                        <div className="relative z-10 flex items-center justify-between">
                            <div>
                                <h2 className="text-2xl font-bold text-white mb-2 flex items-center gap-3">
                                    <ShoppingBag className="w-6 h-6" />
                                    Recent Orders
                                </h2>
                                <p className="text-emerald-100 leading-relaxed">
                                    Your latest eSIM purchases and transaction history
                                </p>
                            </div>
                            <Button
                                variant="outline"
                                className="bg-white/20 border-white/30 text-white hover:bg-white/30 backdrop-blur-sm"
                                onClick={() => navigate('/dashboard/orders')}
                            >
                                View All Orders
                                <ArrowRight className="w-4 h-4 ml-2" />
                            </Button>
                        </div>
                    </div>
                    <div className="bg-gradient-to-br from-white to-emerald-50/30 overflow-x-auto">
                        {stats.recentOrders.length === 0 && !loading ? (
                            <div className="p-12 text-center">
                                <div className="bg-gradient-to-br from-emerald-100 to-teal-100 p-6 rounded-2xl mb-4 inline-block">
                                    <ShoppingBag className="w-16 h-16 text-emerald-400 mx-auto" />
                                </div>
                                <h3 className="text-xl font-semibold text-gray-700 mb-2">No Recent Orders</h3>
                                <p className="text-gray-500 mb-4">Your recent orders will appear here once you make a purchase</p>
                                <Button
                                    onClick={() => navigate('/dashboard/plans')}
                                    className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700"
                                >
                                    Browse Plans
                                </Button>
                            </div>
                        ) : (
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-gradient-to-r from-emerald-50 to-teal-50 border-b-2 border-emerald-100">
                                        <TableHead className="font-bold text-emerald-800 py-4">Order ID</TableHead>
                                        <TableHead className="font-bold text-emerald-800">Plan</TableHead>
                                        <TableHead className="font-bold text-emerald-800">Details</TableHead>
                                        <TableHead className="font-bold text-emerald-800">Quantity</TableHead>
                                        <TableHead className="font-bold text-emerald-800">Amount</TableHead>
                                        <TableHead className="font-bold text-emerald-800">Status</TableHead>
                                        <TableHead className="font-bold text-emerald-800">Date</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {loading ? (
                                        Array(5).fill(0).map((_, i) => (
                                            <TableRow key={i} className="hover:bg-emerald-50/50 transition-colors border-b border-emerald-100/50">
                                                <TableCell><Skeleton className="h-5 w-24 rounded-lg" /></TableCell>
                                                <TableCell><Skeleton className="h-5 w-32 rounded-lg" /></TableCell>
                                                <TableCell><Skeleton className="h-5 w-48 rounded-lg" /></TableCell>
                                                <TableCell><Skeleton className="h-5 w-20 rounded-lg" /></TableCell>
                                                <TableCell><Skeleton className="h-5 w-20 rounded-lg" /></TableCell>
                                                <TableCell><Skeleton className="h-5 w-24 rounded-lg" /></TableCell>
                                                <TableCell><Skeleton className="h-5 w-24 rounded-lg" /></TableCell>
                                            </TableRow>
                                        ))
                                    ) : (
                                        stats.recentOrders.map((order) => (
                                            <TableRow key={order.id} className="hover:bg-emerald-50/50 cursor-pointer transition-all duration-300 border-b border-emerald-100/30">
                                                <TableCell className="font-bold text-slate-800 py-4">{order.id}</TableCell>
                                                <TableCell className="font-medium text-slate-700">{order.planName}</TableCell>
                                                <TableCell>
                                                    <div className="flex items-center gap-2">
                                                        {order.planType === 'Unlimited' ? (
                                                            <span className="font-semibold text-blue-700 bg-blue-100 px-3 py-1.5 rounded-full text-xs border border-blue-200">
                                                                Unlimited
                                                            </span>
                                                        ) : order.planType === 'Custom' ? (
                                                            <span className="font-semibold text-violet-700 bg-violet-100 px-3 py-1.5 rounded-full text-xs border border-violet-200">
                                                                {order.customPlanData || 'Custom Plan'}
                                                            </span>
                                                        ) : order.planData && order.planDataUnit ? (
                                                            <span className="font-semibold text-slate-700 bg-slate-100 px-3 py-1.5 rounded-full text-xs border border-slate-200">
                                                                {order.planData} {order.planDataUnit}
                                                            </span>
                                                        ) : (
                                                            <span className="text-slate-400">-</span>
                                                        )}
                                                        <span className="text-slate-500 text-xs bg-slate-50 px-2 py-1 rounded-md">
                                                            {order.validityDays} Day{order.validityDays > 1 ? 's' : ''}
                                                        </span>
                                                    </div>
                                                </TableCell>
                                                <TableCell className="font-semibold text-slate-700">{order.quantity}</TableCell>
                                                <TableCell className="font-bold text-slate-800">${order.amount?.toFixed(2)}</TableCell>
                                                <TableCell>
                                                    <span className={`px-3 py-1.5 rounded-full text-xs font-semibold border ${
                                                        order.status === 'completed' ? 'bg-emerald-100 text-emerald-800 border-emerald-200' :
                                                        order.status === 'pending' ? 'bg-amber-100 text-amber-800 border-amber-200' :
                                                        'bg-slate-100 text-slate-800 border-slate-200'
                                                    }`}>
                                                        {order.status}
                                                    </span>
                                                </TableCell>
                                                <TableCell className="text-slate-600 font-medium">
                                                    {format(new Date(order.createdAt), 'MMM d, yyyy')}
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        )}
                    </div>
                </Card>
            </div>
        </div>
    );
}
