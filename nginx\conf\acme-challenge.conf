# This configuration file is specifically for handling ACME challenges
# It has a higher priority than app.conf due to alphabetical ordering

server {
    listen 80;
    server_name api.vizlync.net;
    
    # Only handle ACME challenge requests
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files $uri =404;
        
        # Add debug headers
        add_header X-Debug-Info "Serving from ACME challenge handler" always;
        add_header Content-Type text/plain;
        
        # Prevent proxying these requests to the app
        break;
    }
    
    # For all other requests, pass to the main configuration
    location / {
        return 301 https://$host$request_uri;
    }
}
