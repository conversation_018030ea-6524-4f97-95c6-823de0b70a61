const { Model, DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const { v4: uuidv4 } = require('uuid');
const Provider = require('./Provider');
const generateProductId = require('../utils/generateProductId');

class EsimPlan extends Model {
    static associate(models) {
        // Countries association
        EsimPlan.belongsToMany(models.Country, {
            through: models.EsimPlanCountries,
            as: 'countries',
            foreignKey: 'esimPlanId'
        });

        // Stock association
        EsimPlan.hasMany(models.EsimStock, {
            foreignKey: 'esimPlanId',
            as: 'stocks'
        });

        // Stock history association
        EsimPlan.hasMany(models.EsimPlanStockHistory, {
            foreignKey: 'esimPlanId',
            as: 'stockHistory'
        });

        // Provider association
        EsimPlan.belongsTo(Provider, {
            foreignKey: 'providerId',
            as: 'provider'
        });
    }
}

EsimPlan.init({
    id: {
        type: DataTypes.STRING(36),
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    productId: {
        type: DataTypes.CHAR(12),
        allowNull: false,
        unique: true,
        defaultValue: () => generateProductId()
    },
    externalProductId: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    externalSkuId: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    name: {
        type: DataTypes.STRING(100),
        allowNull: false
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    planInfo: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    additionalInfo: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    instructions: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    providerId: {
        type: DataTypes.STRING(36),
        allowNull: true,
        references: {
            model: Provider,
            key: 'id'
        }
    },
    networkName: {
        type: DataTypes.STRING(100),
        allowNull: false
    },
    networkType: {
        type: DataTypes.STRING(50),
        allowNull: true,
        defaultValue: '4G/LTE'
    },
    region: {
        type: DataTypes.TEXT,
        get() {
            const rawValue = this.getDataValue('region');
            return rawValue ? rawValue.split(',').map(r => r.trim()) : [];
        },
        set(val) {
            if (Array.isArray(val)) {
                this.setDataValue('region', val.join(','));
            } else if (typeof val === 'string') {
                this.setDataValue('region', val);
            }
        }
    },
    supportedRegions: {
        type: DataTypes.JSON,
        allowNull: true
    },
    buyingPrice: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false
    },
    validityDays: {
        type: DataTypes.INTEGER,
        allowNull: false,
        validate: {
            validityDaysCheck(value) {
                // Allow zero validity days for replacement eSIMs
                if (value === 0 && this.category !== 'esim_replacement') {
                    throw new Error('Validity days must be greater than 0 for non-replacement plans');
                }
            }
        }
    },
    planType: {
        type: DataTypes.ENUM('Fixed', 'Unlimited', 'Custom'),
        allowNull: false,
        defaultValue: 'Fixed'
    },
    planData: {
        type: DataTypes.DECIMAL(10, 1),
        allowNull: true,
        validate: {
            planDataCheck(value) {
                // For Fixed plans, data must be positive unless it's a replacement or addon eSIM
                if (
                    this.planType === 'Fixed' &&
                    (value === 0 || value === null) &&
                    this.category !== 'esim_replacement' &&
                    this.category !== 'esim_addon'
                ) {
                    throw new Error('Plan data must be greater than 0 for Fixed plans that are not replacement or addon eSIMs');
                }
            }
            
        },
        get() {
            const value = this.getDataValue('planData');
            if (value === null) return null;
            // Return as integer if it's a whole number, otherwise return with 1 decimal place
            return Number.isInteger(Number(value)) ? parseInt(value) : Number(value);
        }
    },
    planDataUnit: {
        type: DataTypes.ENUM('MB', 'GB', 'TB'),
        allowNull: true
    },
    customPlanData: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    sellingPrice: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
        validate: {
            isValidPrice(value) {
                // For replacement eSIMs, selling price can be null
                if (this.category === 'esim_replacement') {
                    return; // Skip validation for replacement eSIMs
                }
                
                if (value !== null) {
                    const buyingPrice = this.getDataValue('buyingPrice'); 
                    
                    if (buyingPrice !== null && value <= buyingPrice) {
                        throw new Error('Selling price must be greater than buying price');
                    }
                }
            }
        }
    },
    category: {
        type: DataTypes.ENUM('esim_realtime', 'esim_addon', 'esim_replacement'),
        allowNull: false,
        defaultValue: 'esim_realtime'
    },
    planCategory: {
        type: DataTypes.ENUM('Voice and Data', 'Data Only'),
        allowNull: false,
        defaultValue: 'Data Only'
    },
    is_voice: {
        type: DataTypes.ENUM('Available', 'Not Available'),
        allowNull: false,
        defaultValue: 'Not Available',
        
    },
    voiceMinUnit: {
        type: DataTypes.ENUM('Min', 'Hr', 'Sec'),
        allowNull: true,
        defaultValue: null
    },
    voiceMin: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: null,
        validate: {
            isVoiceMinValid(value) {
                if (this.planCategory === 'Data Only' && value !== null) {
                    throw new Error('Voice minutes should not be set for Data Only plans');
                }
            }
        }
    },
    is_sms:{
        type: DataTypes.ENUM('Available', 'Not Available'),
        allowNull: false,
        defaultValue: 'Not Available'
    },
    sms:{
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: null
    },
    top_up: {
        type: DataTypes.ENUM('Available', 'Not Available'),
        allowNull: false,
        defaultValue: 'Not Available'
    },
    features: {
        type: DataTypes.JSON,
        allowNull: true
    },
    providerMetadata: {
        type: DataTypes.JSON,
        allowNull: true
    },
    profile: {
        type: DataTypes.ENUM('Local', 'Roaming'),
        allowNull: false,
        defaultValue: 'Local'
    },
    status: {
        type: DataTypes.ENUM('visible', 'hidden'),
        allowNull: false,
        defaultValue: 'visible'
    },
    isActive: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    startDateEnabled: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false
    },
    hotspot: {
        type: DataTypes.ENUM('Available', 'Not Available'),
        allowNull: false,
        defaultValue: 'Available',
    },
    activationPolicy: {
        type: DataTypes.ENUM('Activation upon purchase', 'Activation upon first usage', 'Activation upon travel date'),
        allowNull: false,
        defaultValue: 'Activation upon purchase',
    },  
    speed: {
        type: DataTypes.ENUM('Restricted', 'Unrestricted'),
        allowNull: false,
        defaultValue: 'Unrestricted',
    },
    stockThreshold: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 10,
        validate: {
            min: 1,
            max: 1000
        }
    }
}, {
    sequelize,
    modelName: 'EsimPlan',
    tableName: 'esimplans',
    timestamps: true
});

EsimPlan.beforeCreate(async (plan) => {
    let attempts = 0;
    const maxAttempts = 10;
    let unique = false;

    while (!unique && attempts < maxAttempts) {
        const productId = generateProductId();
        const existing = await EsimPlan.findOne({ where: { productId } });
        
        if (!existing) {
            plan.productId = productId;
            unique = true;
        }
        attempts++;
    }

    if (!unique) {
        throw new Error('Could not generate a unique product ID after multiple attempts');
    }
});

module.exports = EsimPlan;
