{"env": {"browser": true, "es2021": true, "cypress/globals": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:cypress/recommended"], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react", "react-hooks", "cypress"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "warn", "no-unused-vars": "warn", "react/display-name": "off"}, "settings": {"react": {"version": "detect"}}}