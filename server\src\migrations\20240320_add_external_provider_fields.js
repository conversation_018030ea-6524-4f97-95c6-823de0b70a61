module.exports = {
    up: async (queryInterface, Sequelize) => {
        // Add provider-related fields to EsimPlans if they don't exist
        await queryInterface.addColumn('esimplans', 'externalProductId', {
            type: Sequelize.STRING(255),
            allowNull: true,
            after: 'productId'
        }).catch(e => console.log('Column already exists: externalProductId'));

        await queryInterface.addColumn('esimplans', 'externalSkuId', {
            type: Sequelize.STRING(255),
            allowNull: true,
            after: 'externalProductId'
        }).catch(e => console.log('Column already exists: externalSkuId'));

        await queryInterface.addColumn('esimplans', 'providerMetadata', {
            type: Sequelize.JSON,
            allowNull: true,
            after: 'status'
        }).catch(e => console.log('Column already exists: providerMetadata'));

        // Add provider-related fields to Orders if they don't exist
        await queryInterface.addColumn('orders', 'externalOrderId', {
            type: Sequelize.STRING(255),
            allowNull: true,
            after: 'id'
        }).catch(e => console.log('Column already exists: externalOrderId'));

        await queryInterface.addColumn('orders', 'providerResponse', {
            type: Sequelize.JSON,
            allowNull: true,
            after: 'status'
        }).catch(e => console.log('Column already exists: providerResponse'));

        await queryInterface.addColumn('orders', 'providerMetadata', {
            type: Sequelize.JSON,
            allowNull: true,
            after: 'providerResponse'
        }).catch(e => console.log('Column already exists: providerMetadata'));

        await queryInterface.addColumn('orders', 'usageData', {
            type: Sequelize.JSON,
            allowNull: true,
            after: 'providerMetadata'
        }).catch(e => console.log('Column already exists: usageData'));

        await queryInterface.addColumn('orders', 'lastUsageCheck', {
            type: Sequelize.DATE,
            allowNull: true,
            after: 'usageData'
        }).catch(e => console.log('Column already exists: lastUsageCheck'));

        // Add provider-related fields to EsimStocks if they don't exist
        await queryInterface.addColumn('esimstocks', 'externalStockId', {
            type: Sequelize.STRING(255),
            allowNull: true,
            after: 'id'
        }).catch(e => console.log('Column already exists: externalStockId'));

        await queryInterface.addColumn('esimstocks', 'externalIccid', {
            type: Sequelize.STRING(255),
            allowNull: true,
            after: 'externalStockId'
        }).catch(e => console.log('Column already exists: externalIccid'));

        await queryInterface.addColumn('esimstocks', 'providerMetadata', {
            type: Sequelize.JSON,
            allowNull: true,
            after: 'notes'
        }).catch(e => console.log('Column already exists: providerMetadata'));

        // Add indexes
        await queryInterface.addIndex('orders', ['externalOrderId'], {
            name: 'idx_external_order'
        }).catch(e => console.log('Index already exists: idx_external_order'));

        await queryInterface.addIndex('esimstocks', ['externalIccid'], {
            name: 'idx_external_iccid'
        }).catch(e => console.log('Index already exists: idx_external_iccid'));
    },

    down: async (queryInterface, Sequelize) => {
        // Remove provider-related fields from EsimPlans
        await queryInterface.removeColumn('esimplans', 'externalProductId')
            .catch(e => console.log('Column does not exist: externalProductId'));
        await queryInterface.removeColumn('esimplans', 'externalSkuId')
            .catch(e => console.log('Column does not exist: externalSkuId'));
        await queryInterface.removeColumn('esimplans', 'providerMetadata')
            .catch(e => console.log('Column does not exist: providerMetadata'));

        // Remove provider-related fields from Orders
        await queryInterface.removeColumn('orders', 'externalOrderId')
            .catch(e => console.log('Column does not exist: externalOrderId'));
        await queryInterface.removeColumn('orders', 'providerResponse')
            .catch(e => console.log('Column does not exist: providerResponse'));
        await queryInterface.removeColumn('orders', 'providerMetadata')
            .catch(e => console.log('Column does not exist: providerMetadata'));
        await queryInterface.removeColumn('orders', 'usageData')
            .catch(e => console.log('Column does not exist: usageData'));
        await queryInterface.removeColumn('orders', 'lastUsageCheck')
            .catch(e => console.log('Column does not exist: lastUsageCheck'));

        // Remove provider-related fields from EsimStocks
        await queryInterface.removeColumn('esimstocks', 'externalStockId')
            .catch(e => console.log('Column does not exist: externalStockId'));
        await queryInterface.removeColumn('esimstocks', 'externalIccid')
            .catch(e => console.log('Column does not exist: externalIccid'));
        await queryInterface.removeColumn('esimstocks', 'providerMetadata')
            .catch(e => console.log('Column does not exist: providerMetadata'));

        // Remove indexes
        await queryInterface.removeIndex('orders', 'idx_external_order')
            .catch(e => console.log('Index does not exist: idx_external_order'));
        await queryInterface.removeIndex('esimstocks', 'idx_external_iccid')
            .catch(e => console.log('Index does not exist: idx_external_iccid'));
    }
}; 