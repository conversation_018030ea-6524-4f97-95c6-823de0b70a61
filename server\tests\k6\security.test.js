import http from 'k6/http';
import { sleep, check, group } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { CONFIG } from './config.js';

// Custom metrics
const securityFailures = new Rate('security_failures');
const blockedRequests = new Trend('blocked_requests');
const rateLimitedRequests = new Counter('rate_limited_total');
const successfulBlocks = new Rate('successful_security_blocks');

export const options = {
    thresholds: {
        'http_req_duration': ['p(95)<3000'],      // 95% of requests should be below 3s
        'http_req_failed': ['rate>=0.4'],         // At least 40% should fail (we're testing security!)
        'security_failures': ['rate<0.2'],        // Less than 20% unexpected security check failures
        'blocked_requests': ['p(95)<1000'],       // Most requests should be blocked within 1s
        'successful_security_blocks': ['rate>0.8'] // At least 80% of security measures should work
    },
    scenarios: {
        security_tests: {
            executor: 'ramping-vus',
            startVUs: 0,
            stages: [
                { duration: '30s', target: 50 },  // Quick ramp-up for brute force simulation
                { duration: '1m', target: 50 },   // Sustained high-frequency requests
                { duration: '30s', target: 0 }    // Ramp-down
            ]
        }
    }
};

export default function(data) {
    const baseUrl = CONFIG.baseUrl;
    const headers = { 'Content-Type': 'application/json' };

    group('Login Security', () => {
        const startTime = Date.now();
        const loginPayload = JSON.stringify({
            email: `test${__VU}@example.com`, // Use unique email per VU
            password: 'wrongpassword'
        });

        const loginResponse = http.post(`${baseUrl}/api/auth/login`, loginPayload, {
            headers: headers
        });

        // Track blocked time
        blockedRequests.add(Date.now() - startTime);

        // Track rate limiting and security blocks
        if (loginResponse.status === 429) {
            rateLimitedRequests.add(1);
        }

        const isSecurityWorking = check(loginResponse, {
            'login security measures working': (r) => 
                r.status === 429 || // Rate limited
                r.status === 401 || // Unauthorized
                r.status === 400    // Bad request
        });
        successfulBlocks.add(isSecurityWorking);

        sleep(0.5); // Increased sleep to reduce request frequency
    });

    group('OTP Security', () => {
        const invalidOtpPayload = JSON.stringify({
            otp: '000000',
            tempToken: 'invalid-token'
        });

        const otpResponse = http.post(`${baseUrl}/api/auth/verify-otp`, invalidOtpPayload, {
            headers: headers
        });

        const isSecurityWorking = check(otpResponse, {
            'OTP security measures working': (r) => r.status === 400 || r.status === 401
        });
        successfulBlocks.add(isSecurityWorking);

        sleep(0.5);
    });

    group('Password Reset Security', () => {
        const resetPayload = JSON.stringify({
            token: 'invalid-reset-token',
            newPassword: 'newpassword123'
        });

        const resetResponse = http.post(`${baseUrl}/api/auth/reset-password`, resetPayload, {
            headers: headers
        });

        const isSecurityWorking = check(resetResponse, {
            'password reset security working': (r) => r.status === 400 || r.status === 401
        });
        successfulBlocks.add(isSecurityWorking);

        sleep(0.5);
    });

    group('Protected Routes Security', () => {
        // Test without token
        const protectedResponse = http.get(`${baseUrl}/api/auth/verify`, {
            headers: headers
        });

        const isUnauthorizedBlocked = check(protectedResponse, {
            'unauthorized access blocked': (r) => r.status === 401
        });
        successfulBlocks.add(isUnauthorizedBlocked);

        // Test with invalid token
        const invalidTokenHeaders = {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer invalid-token'
        };

        const invalidTokenResponse = http.get(`${baseUrl}/api/auth/verify`, {
            headers: invalidTokenHeaders
        });

        const isInvalidTokenBlocked = check(invalidTokenResponse, {
            'invalid token blocked': (r) => r.status === 401
        });
        successfulBlocks.add(isInvalidTokenBlocked);

        sleep(0.5);
    });

    group('Knowledge Base Security', () => {
        // Try to access draft article
        const draftArticleResponse = http.get(`${baseUrl}/api/knowledge-base/articles/draft-123`, {
            headers: headers
        });

        const isDraftProtected = check(draftArticleResponse, {
            'draft article access restricted': (r) => r.status === 401 || r.status === 403
        });
        successfulBlocks.add(isDraftProtected);

        // Try to access archived article
        const archivedArticleResponse = http.get(`${baseUrl}/api/knowledge-base/articles/archived-123`, {
            headers: headers
        });

        const isArchivedProtected = check(archivedArticleResponse, {
            'archived article access restricted': (r) => r.status === 401 || r.status === 403
        });
        successfulBlocks.add(isArchivedProtected);

        sleep(0.5);
    });
}
