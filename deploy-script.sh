#!/bin/bash

# Deployment script for eSIM Platform Backend
# This script will be copied to the EC2 instance and executed

set -e  # Exit on any error

echo "Starting deployment process..."

# Navigate to project directory
cd ~/esim-project

# Verify .env file exists
echo "Checking environment variables..."
if [ -f "server/.env" ]; then
    echo "✅ .env file found in server directory"
else
    echo "❌ Error: .env file not found in server directory"
    exit 1
fi

# Stop existing containers if they exist
echo "Stopping existing containers..."
if [ -f "docker-compose.yml" ]; then
    docker-compose down || true
fi

# Clean up SSL certificate files with proper permissions
echo "Cleaning up SSL certificate files..."
if [ -d "nginx/certbot/conf" ]; then
    # Use Docker to remove files with root permissions
    docker run --rm -v "$(pwd)/nginx/certbot/conf:/etc/letsencrypt" alpine:latest \
        sh -c "rm -rf /etc/letsencrypt/renewal-hooks" || true
fi

# Remove old containers and images to free up space
echo "Cleaning up old Docker resources..."
docker system prune -f || true

# Build and start the containers
echo "Building and starting containers..."
docker-compose up -d --build

# Wait for containers to be ready
echo "Waiting for containers to start..."
sleep 10

# Check if containers are running
echo "Checking container status..."
if [ $(docker ps -q | wc -l) -lt 3 ]; then
    echo "❌ Some containers failed to start. Checking logs..."
    docker-compose logs
    exit 1
else
    echo "✅ All containers are running"
fi

# Use HTTP-only configuration initially
DOMAIN_NAME="${DOMAIN_NAME:-api.vizlync.com}"
ADMIN_EMAIL="${ADMIN_EMAIL:-<EMAIL>}"

echo "Setting up HTTP-only configuration for $DOMAIN_NAME..."

# Check if we're using a custom Nginx Dockerfile
if [ -f "Dockerfile.nginx" ]; then
    echo "✅ Using custom Nginx Dockerfile"
else
    # Create a very simple Nginx configuration for initial deployment
    echo "Creating minimal Nginx configuration..."
    mkdir -p nginx/conf

    # Create a very basic configuration that's guaranteed to work
    cat > nginx/conf/app.conf << EOL
server {
    listen 80;
    server_name localhost;

    # For Let's Encrypt HTTP-01 challenge
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files \$uri =404;
    }

    location / {
        proxy_pass http://app:3000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
    }
}
EOL

    echo "✅ Basic Nginx configuration created"
fi

# Start containers with the minimal configuration
echo "Starting containers with minimal configuration..."
docker-compose up -d

# Wait for containers to start
echo "Waiting for containers to start..."
sleep 10

# Check if Nginx is running
if ! docker ps | grep -q nginx; then
    echo "❌ Nginx container failed to start. Checking logs..."
    docker-compose logs nginx

    echo "Trying with an even more minimal configuration..."
    cat > nginx/conf/app.conf << EOL
server {
    listen 80 default_server;

    # For Let's Encrypt HTTP-01 challenge
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files \$uri =404;
    }

    location / {
        return 200 'API is running. Nginx is working.';
        add_header Content-Type text/plain;
    }
}
EOL

    # Try to restart Nginx
    docker-compose restart nginx
    sleep 5

    if ! docker ps | grep -q nginx; then
        echo "❌ Nginx still failing. Please check the configuration manually."
    else
        echo "✅ Nginx started with fallback configuration"
    fi
fi

# Check if SSL certificates exist and configure accordingly
if [ -f "nginx/certbot/conf/live/$DOMAIN_NAME/fullchain.pem" ]; then
    echo "✅ SSL certificates found! Configuring HTTPS..."

    # Create SSL-enabled configuration
    cat > nginx/conf/app.conf << EOL
server {
    listen 80;
    server_name localhost $DOMAIN_NAME;

    # For Let's Encrypt HTTP-01 challenge
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files \$uri =404;
    }

    # Redirect all other HTTP traffic to HTTPS
    location / {
        return 301 https://\$host\$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN_NAME;

    ssl_certificate /etc/letsencrypt/live/$DOMAIN_NAME/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN_NAME/privkey.pem;

    # SSL parameters
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";

    # Proxy to Node.js application
    location / {
        proxy_pass http://esim-backend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOL

    # Restart Nginx to apply SSL configuration
    docker-compose restart nginx
    sleep 5

    echo "✅ SSL configuration applied!"
    echo "Your application is now accessible at:"
    echo "  HTTPS: https://$DOMAIN_NAME"
    echo "  HTTP:  http://$DOMAIN_NAME (redirects to HTTPS)"
else
    echo ""
    echo "NOTE: SSL certificates are not set up yet."
    echo "To set up SSL certificates, run the following command after deployment:"
    echo "  ./setup-ssl.sh $DOMAIN_NAME $ADMIN_EMAIL"
fi

# Final health check
echo "Performing final health check..."
sleep 5

# Check if app container is healthy
if docker exec esim-backend curl -f http://localhost:3000/ > /dev/null 2>&1; then
    echo "✅ Application is responding"
else
    echo "⚠️  Application health check failed, checking logs..."
    docker-compose logs app
fi

# Check if nginx is healthy
if docker exec nginx nginx -t > /dev/null 2>&1; then
    echo "✅ Nginx configuration is valid"
else
    echo "❌ Nginx configuration error"
    docker exec nginx nginx -t
fi

echo "🎉 Deployment completed successfully!"
echo "Your application should be accessible at:"
echo "  HTTP:  http://$DOMAIN_NAME"
echo ""
echo "To set up HTTPS with SSL certificates, run:"
echo "  chmod +x setup-ssl.sh"
echo "  ./setup-ssl.sh $DOMAIN_NAME $ADMIN_EMAIL"
