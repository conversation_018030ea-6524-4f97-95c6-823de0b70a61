/* Professional, minimal, corporate login background */
body.login-body {
  min-height: 100vh;
background: linear-gradient(135deg, #0f172a 0%, #1e293b 40%, #475569 100%);
background-blend-mode: overlay;
background-image: radial-gradient(circle at 20% 30%, rgba(255,255,255,0.02), transparent 60%);
  /* Deep blue to soft gray gradient */
  position: relative;
  overflow: hidden;
}

body.login-body::before {
  content: '';
  position: absolute;
  z-index: 0;
  top: -120px;
  left: -120px;
  width: 480px;
  height: 480px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, #0e7490 0%, #334155 100%);
  opacity: 0.18;
  filter: blur(12px);
}

body.login-body::after {
  content: '';
  position: absolute;
  z-index: 0;
  bottom: -100px;
  right: -100px;
  width: 340px;
  height: 340px;
  border-radius: 50%;
  background: radial-gradient(circle at 70% 70%, #64748b 0%, #0e7490 100%);
  opacity: 0.13;
  filter: blur(8px);
}

/* Remove pattern overlay for a cleaner, more minimal look */
.background-pattern { display: none !important; }
