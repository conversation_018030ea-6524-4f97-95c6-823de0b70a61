const sequelize = require('../config/database');

async function fixMarkupColumnDirect() {
    try {
        // Execute the SQL directly without transaction
        await sequelize.query(`
            USE esim_platform;
            
            -- Update existing NULL values to 0
            UPDATE Users SET markupPercentage = 0 WHERE markupPercentage IS NULL;
            
            -- Create temporary column
            ALTER TABLE Users ADD COLUMN markup_temp DECIMAL(5,2) NOT NULL DEFAULT 0;
            
            -- Copy data
            UPDATE Users SET markup_temp = COALESCE(markupPercentage, 0);
            
            -- Drop old column
            ALTER TABLE Users DROP COLUMN markupPercentage;
            
            -- Rename new column
            ALTER TABLE Users CHANGE COLUMN markup_temp markupPercentage DECIMAL(5,2) NOT NULL DEFAULT 0;
        `);
        
        console.log('Successfully fixed markupPercentage column');
        process.exit(0);
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}

fixMarkupColumnDirect();
