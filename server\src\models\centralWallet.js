const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const CentralWallet = sequelize.define('CentralWallet', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
    },
    totalBalance: {
        type: DataTypes.DECIMAL(12,2),
        allowNull: false,
        defaultValue: 0.00
    }
}, {
    tableName: 'centralwallet',
    timestamps: true
});

module.exports = CentralWallet;
