const sequelize = require('../config/database');

async function updateEsimPlanSchema() {
    try {
        // First update any NULL values to 0 temporarily
        await sequelize.query(`
            UPDATE EsimPlans 
            SET sellingPrice = 0 
            WHERE sellingPrice IS NULL
        `);
        console.log('Updated NULL values temporarily');

        // Modify the column to allow NULL values
        await sequelize.query(`
            ALTER TABLE EsimPlans 
            MODIFY COLUMN sellingPrice DECIMAL(10,2) NULL
        `);
        console.log('Modified column to allow NULL values');

        process.exit(0);
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}

updateEsimPlanSchema();
