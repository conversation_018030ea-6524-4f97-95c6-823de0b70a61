const sequelize = require('../config/database');

async function fixMarkupColumnStepByStep() {
    try {
        // Step 1: Update NULL values
        await sequelize.query(`
            UPDATE Users 
            SET markupPercentage = 0 
            WHERE markupPercentage IS NULL
        `);
        console.log('Step 1: Updated NULL values');

        // Step 2: Add temporary column
        await sequelize.query(`
            ALTER TABLE Users 
            ADD COLUMN markup_temp DECIMAL(5,2) NOT NULL DEFAULT 0
        `);
        console.log('Step 2: Added temporary column');

        // Step 3: Copy data
        await sequelize.query(`
            UPDATE Users 
            SET markup_temp = COALESCE(markupPercentage, 0)
        `);
        console.log('Step 3: Copied data');

        // Step 4: Drop old column
        await sequelize.query(`
            ALTER TABLE Users 
            DROP COLUMN markupPercentage
        `);
        console.log('Step 4: Dropped old column');

        // Step 5: Rename new column
        await sequelize.query(`
            ALTER TABLE Users 
            CHANGE COLUMN markup_temp markupPercentage DECIMAL(5,2) NOT NULL DEFAULT 0
        `);
        console.log('Step 5: Renamed column');

        console.log('Successfully fixed markupPercentage column');
        process.exit(0);
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}

fixMarkupColumnStepByStep();
