const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../middleware/auth');
const partnerApiController = require('../controllers/partnerApiController');

// Route to get API key information
router.get('/api-key', isAuthenticated, partnerApiController.getApiKeyInfo);

// Route to generate a new API key
router.post('/api-key/generate', isAuthenticated, partnerApiController.generateApiKey);

// Route to reveal the full API key
router.get('/api-key/reveal', isAuthenticated, partnerApiController.revealApiKey);

module.exports = router; 