const express = require('express');
const router = express.Router();
const { getWallet, addFunds, getTransactions, setMaxBalance, setBalanceThreshold, exportTransactions } = require('../controllers/walletController');
const { isAuthenticated, isAdmin } = require('../middleware/auth');

// Partner routes for their own wallet
router.get('/partner/wallet', isAuthenticated, (req, res) => {
    req.params.userId = req.user.id;
    getWallet(req, res);
});

router.get('/partner/wallet/transactions', isAuthenticated, (req, res) => {
    req.params.userId = req.user.id;
    getTransactions(req, res);
});

router.get('/partner/wallet/transactions/export', isAuthenticated, (req, res) => {
    req.params.userId = req.user.id;
    exportTransactions(req, res);
});

router.post('/partner/wallet/threshold', isAuthenticated, setBalanceThreshold);

// Admin routes for wallet management
router.get('/users/:userId/wallet', isAuthenticated, isAdmin, getWallet);
router.post('/users/:userId/wallet/add-funds', isAuthenticated, isAdmin, addFunds);
router.get('/users/:userId/wallet/transactions', isAuthenticated, isAdmin, getTransactions);
router.get('/users/:userId/wallet/transactions/export', isAuthenticated, isAdmin, exportTransactions);
router.post('/users/:userId/wallet/set-max-balance', isAuthenticated, isAdmin, setMaxBalance);

module.exports = router;
