const express = require('express');
const router = express.Router();
const { 
    getCentralWallet, 
    updateCentralWallet, 
    addPartnerFunds,
    editPartnerFunds,
    getPartnerTransactions 
} = require('../controllers/centralWalletController');
const { isAuthenticated, isAdmin } = require('../middleware/auth');

// Route to get central wallet details (admin only)
router.get('/central-wallet', isAuthenticated, isAdmin, getCentralWallet);

// Route to update central wallet total balance (admin only)
router.put('/central-wallet', isAuthenticated, isAdmin, updateCentralWallet);

// Route to transfer funds from central wallet to a partner's wallet (admin only)
router.post('/partners/:partnerId/central-wallet/add-funds', isAuthenticated, isAdmin, addPartnerFunds);

// Route to edit partner's wallet funds (admin only)
router.put('/partners/:partnerId/central-wallet/edit-funds', isAuthenticated, isAdmin, editPartner<PERSON>unds);

// Route to get partner's wallet transactions (admin only)
router.get('/partners/:partnerId/wallet/transactions', isAuthenticated, isAdmin, getPartnerTransactions);

module.exports = router;
