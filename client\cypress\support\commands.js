// Custom Cypress commands

export function login(email, password) {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('apiUrl')}/auth/login`,
    body: { email, password }
  }).then((response) => {
    window.localStorage.setItem('token', response.body.token);
    window.localStorage.setItem('user', JSON.stringify(response.body.user));
  });
};

export function logout() {
  window.localStorage.removeItem('token');
  window.localStorage.removeItem('user');
};

export function createTestPartner() {
  const partner = {
    email: `test-partner-${Date.now()}@example.com`,
    password: 'testpassword123',
    firstName: 'Test',
    lastName: 'Partner',
    companyName: 'Test Company',
    phone: '1234567890'
  };

  return cy.request({
    method: 'POST',
    url: `${Cypress.env('apiUrl')}/auth/register`,
    body: partner
  }).then((response) => {
    return { ...partner, ...response.body };
  });
};

// Partner login command
Cypress.Commands.add('partnerLogin', () => {
  // Mock successful login response
  cy.intercept('POST', '/api/auth/login', {
    statusCode: 200,
    body: {
      tempToken: 'temp-token-123'
    }
  }).as('login');

  // Mock OTP verification
  cy.intercept('POST', '/api/auth/verify-otp', {
    statusCode: 200,
    body: {
      user: {
        id: 'test-partner-id',
        email: '<EMAIL>',
        role: 'partner'
      },
      token: 'valid-token-123'
    }
  }).as('verifyOtp');

  // Visit login page
  cy.visit('/auth/login');

  // Fill login form
  cy.get('input[type="email"]').type('<EMAIL>');
  cy.get('input[type="password"]').type('password123');
  cy.get('button[type="submit"]').click();

  // Wait for login response
  cy.wait('@login');

  // Send OTP
  cy.contains('Send OTP').click();

  // Enter OTP
  cy.get('input[type="text"]').type('123456');
  cy.get('button[type="submit"]').click();

  // Wait for OTP verification
  cy.wait('@verifyOtp');

  // Ensure we're redirected to the dashboard
  cy.url().should('include', '/dashboard');
});
