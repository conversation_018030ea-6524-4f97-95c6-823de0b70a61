import http from 'k6/http';
import { sleep } from 'k6';
import { CONFIG } from './config.js';
import { checkResponse, getAuthToken } from './helpers.js';

export const options = {
    thresholds: CONFIG.thresholds,
    scenarios: {
        plans_flow: CONFIG.scenarios.load
    }
};

export function setup() {
    const token = getAuthToken(http);
    return { 
        baseUrl: CONFIG.baseUrl,
        token: token
    };
}

export default function (data) {
    const baseUrl = data.baseUrl;
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${data.token}`
    };

    // Get plans list
    const plansResponse = http.get(`${baseUrl}/api/esim-plans/partner`, {
        headers: headers
    });
    checkResponse(plansResponse);

    sleep(1);

    // Add to cart
    const cartResponse = http.post(`${baseUrl}/api/cart`, JSON.stringify({
        planId: plansResponse.json('plans')[0].id,
        quantity: 1
    }), {
        headers: headers
    });
    checkResponse(cartResponse, 201);

    sleep(1);

    // Get cart
    const getCartResponse = http.get(`${baseUrl}/api/cart`, {
        headers: headers
    });
    checkResponse(getCartResponse);

    sleep(1);

    // Place order
    const orderResponse = http.post(`${baseUrl}/api/v1/orders/create`, JSON.stringify({
        cartItems: getCartResponse.json('cartItems')
    }), {
        headers: headers
    });
    checkResponse(orderResponse, 201);

    sleep(1);
}
