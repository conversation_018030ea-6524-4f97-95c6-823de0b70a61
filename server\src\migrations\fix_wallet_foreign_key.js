'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Drop the existing foreign key constraint
      await queryInterface.sequelize.query(
        'ALTER TABLE wallets DROP FOREIGN KEY wallets_ibfk_1'
      );

      // Add the new foreign key constraint with correct table name
      await queryInterface.sequelize.query(`
        ALTER TABLE wallets 
        ADD CONSTRAINT wallets_userId_fkey 
        FOREIGN KEY (userId) 
        REFERENCES users(id) 
        ON DELETE RESTRICT 
        ON UPDATE CASCADE
      `);
    } catch (error) {
      console.error('Migration failed:', error);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Drop the new foreign key constraint
      await queryInterface.sequelize.query(
        'ALTER TABLE wallets DROP FOREIGN KEY wallets_userId_fkey'
      );

      // Add back the original foreign key constraint
      await queryInterface.sequelize.query(`
        ALTER TABLE wallets 
        ADD CONSTRAINT wallets_ibfk_1 
        FOREIGN KEY (userId) 
        REFERENCES users(id) 
        ON DELETE RESTRICT 
        ON UPDATE CASCADE
      `);
    } catch (error) {
      console.error('Migration rollback failed:', error);
    }
  }
}; 