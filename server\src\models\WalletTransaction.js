const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const WalletTransaction = sequelize.define('WalletTransaction', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4
    },
    walletId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        references: {
            model: 'wallets',
            key: 'id'
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE'
    },
    type: {
        type: DataTypes.ENUM('credit', 'debit'),
        allowNull: false
    },
    amount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        validate: {
            min: 0.01
        }
    },
    balance: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false
    },
    description: {
        type: DataTypes.STRING(255),
        allowNull: false
    },
    status: {
        type: DataTypes.ENUM('pending', 'completed', 'failed', 'reversed'),
        allowNull: false,
        defaultValue: 'pending'
    },
    metadata: {
        type: DataTypes.JSON,
        allowNull: true
    },
    referenceId: {
        type: DataTypes.STRING(100),
        allowNull: true
    },
    referenceType: {
        type: DataTypes.ENUM('order', 'refund', 'manual', 'other'),
        allowNull: false,
        defaultValue: 'other'
    }
}, {
    tableName: 'wallettransactions',
    timestamps: true,
    indexes: [
        {
            name: 'idx_transaction_search',
            fields: ['walletId', 'type', 'status', 'createdAt']
        },
        {
            name: 'idx_transaction_reference',
            fields: ['referenceId', 'referenceType']
        }
    ]
});

// Define associations
WalletTransaction.associate = function(models) {
    WalletTransaction.belongsTo(models.Wallet, {
        foreignKey: 'walletId',
        as: 'wallet',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE'
    });
};

module.exports = WalletTransaction;
