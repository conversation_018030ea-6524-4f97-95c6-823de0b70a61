INSERT INTO Countries (id, iso3, name, phoneCode, currencyCode, currencySymbol, region, isActive, createdAt, updatedAt) VALUES
('US', 'USA', 'United States', '+1', 'USD', '$', 'North America', true, NOW(), NOW()),
('CN', 'CHN', 'China', '+86', 'CNY', '¥', 'Asia', true, NOW(), NOW()),
('IN', 'IND', 'India', '+91', 'INR', '₹', 'Asia', true, NOW(), NOW()),
('JP', 'JPN', 'Japan', '+81', 'JPY', '¥', 'Asia', true, NOW(), NOW()),
('DE', 'DEU', 'Germany', '+49', 'EUR', '€', 'Europe', true, NOW(), NOW()),
('GB', 'GBR', 'United Kingdom', '+44', 'GBP', '£', 'Europe', true, NOW(), NOW()),
('FR', 'FRA', 'France', '+33', 'EUR', '€', 'Europe', true, NOW(), NOW()),
('IT', 'ITA', 'Italy', '+39', 'EUR', '€', 'Europe', true, NOW(), NOW()),
('CA', 'CAN', 'Canada', '+1', 'CAD', '$', 'North America', true, NOW(), NOW()),
('RU', 'RUS', 'Russia', '+7', 'RUB', '₽', 'Europe/Asia', true, NOW(), NOW()),
('BR', 'BRA', 'Brazil', '+55', 'BRL', 'R$', 'South America', true, NOW(), NOW()),
('AU', 'AUS', 'Australia', '+61', 'AUD', '$', 'Oceania', true, NOW(), NOW()),
('KR', 'KOR', 'South Korea', '+82', 'KRW', '₩', 'Asia', true, NOW(), NOW()),
('MX', 'MEX', 'Mexico', '+52', 'MXN', '$', 'North America', true, NOW(), NOW()),
('ID', 'IDN', 'Indonesia', '+62', 'IDR', 'Rp', 'Asia', true, NOW(), NOW()),
('SA', 'SAU', 'Saudi Arabia', '+966', 'SAR', '﷼', 'Asia', true, NOW(), NOW()),
('TR', 'TUR', 'Turkey', '+90', 'TRY', '₺', 'Europe/Asia', true, NOW(), NOW()),
('ZA', 'ZAF', 'South Africa', '+27', 'ZAR', 'R', 'Africa', true, NOW(), NOW()),
('ES', 'ESP', 'Spain', '+34', 'EUR', '€', 'Europe', true, NOW(), NOW()),
('NL', 'NLD', 'Netherlands', '+31', 'EUR', '€', 'Europe', true, NOW(), NOW()),
('CH', 'CHE', 'Switzerland', '+41', 'CHF', 'CHF', 'Europe', true, NOW(), NOW()),
('AR', 'ARG', 'Argentina', '+54', 'ARS', '$', 'South America', true, NOW(), NOW()),
('SE', 'SWE', 'Sweden', '+46', 'SEK', 'kr', 'Europe', true, NOW(), NOW()),
('PL', 'POL', 'Poland', '+48', 'PLN', 'zł', 'Europe', true, NOW(), NOW()),
('TH', 'THA', 'Thailand', '+66', 'THB', '฿', 'Asia', true, NOW(), NOW()),
('NG', 'NGA', 'Nigeria', '+234', 'NGN', '₦', 'Africa', true, NOW(), NOW()),
('EG', 'EGY', 'Egypt', '+20', 'EGP', '£', 'Africa', true, NOW(), NOW()),
('PK', 'PAK', 'Pakistan', '+92', 'PKR', '₨', 'Asia', true, NOW(), NOW()),
('PH', 'PHL', 'Philippines', '+63', 'PHP', '₱', 'Asia', true, NOW(), NOW()),
('VN', 'VNM', 'Vietnam', '+84', 'VND', '₫', 'Asia', true, NOW(), NOW()),
('IR', 'IRN', 'Iran', '+98', 'IRR', '﷼', 'Asia', true, NOW(), NOW()),
('CO', 'COL', 'Colombia', '+57', 'COP', '$', 'South America', true, NOW(), NOW()),
('BD', 'BGD', 'Bangladesh', '+880', 'BDT', '৳', 'Asia', true, NOW(), NOW()),
('MY', 'MYS', 'Malaysia', '+60', 'MYR', 'RM', 'Asia', true, NOW(), NOW()),
('UA', 'UKR', 'Ukraine', '+380', 'UAH', '₴', 'Europe', true, NOW(), NOW()),
('SG', 'SGP', 'Singapore', '+65', 'SGD', '$', 'Asia', true, NOW(), NOW()),
('BE', 'BEL', 'Belgium', '+32', 'EUR', '€', 'Europe', true, NOW(), NOW()),
('AT', 'AUT', 'Austria', '+43', 'EUR', '€', 'Europe', true, NOW(), NOW()),
('IQ', 'IRQ', 'Iraq', '+964', 'IQD', 'ع.د', 'Asia', true, NOW(), NOW()),
('IL', 'ISR', 'Israel', '+972', 'ILS', '₪', 'Asia', true, NOW(), NOW()),
('CL', 'CHL', 'Chile', '+56', 'CLP', '$', 'South America', true, NOW(), NOW()),
('HK', 'HKG', 'Hong Kong', '+852', 'HKD', '$', 'Asia', true, NOW(), NOW()),
('AE', 'ARE', 'United Arab Emirates', '+971', 'AED', 'د.إ', 'Asia', true, NOW(), NOW()),
('PT', 'PRT', 'Portugal', '+351', 'EUR', '€', 'Europe', true, NOW(), NOW()),
('GR', 'GRC', 'Greece', '+30', 'EUR', '€', 'Europe', true, NOW(), NOW()),
('CZ', 'CZE', 'Czech Republic', '+420', 'CZK', 'Kč', 'Europe', true, NOW(), NOW()),
('RO', 'ROU', 'Romania', '+40', 'RON', 'lei', 'Europe', true, NOW(), NOW()),
('DK', 'DNK', 'Denmark', '+45', 'DKK', 'kr', 'Europe', true, NOW(), NOW()),
('NO', 'NOR', 'Norway', '+47', 'NOK', 'kr', 'Europe', true, NOW(), NOW());
('NO', 'NOR', 'Norway', '+47', 'NOK', 'kr', 'Europe', true, NOW(), NOW());

INSERT INTO Users (
    id, email, alternateEmail, password, role, firstName, lastName, 
    countryId, phoneNumber, alternatePhoneNumber, businessName, businessEmail, 
    isActive, lastLogin, loginAttempts, lockUntil, createdAt, updatedAt
) VALUES 
-- Admin Users
('550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', '<EMAIL>', '$2b$10$aqmcvKAkVnBDmXjNMhWgQe2DC/2SLMy8JBPLO6qmok3fH8JsMYCO6', 'admin', 'John', 'Doe', 
 'US', '+1234567890', NULL, NULL, NULL, 
 true, NOW(), 0, NULL, NOW(), NOW()),

('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', '<EMAIL>', 'h$2b$10$aqmcvKAkVnBDmXjNMhWgQe2DC/2SLMy8JBPLO6qmok3fH8JsMYCO6', 'admin', 'Jane', 'Smith', 
 'GB', '+447911223344', NULL, NULL, NULL, 
 true, NOW(), 0, NULL, NOW(), NOW()),

-- Partner Users
('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', '<EMAIL>', '$2b$10$UQo4fA02Iv9zsa6ExJ8zi.avfBUUKGge1j9semlSwGo7D42TkXW/u', 'partner', 'Carlos', 'Martinez', 
 'ES', '+***********', '+***********', 'Martinez Tech', '<EMAIL>', 
 true, NOW(), 0, NULL, NOW(), NOW()),


('550e8400-e29b-41d4-a716-446655440004', '<EMAIL>', '<EMAIL>', '$2b$10$UQo4fA02Iv9zsa6ExJ8zi.avfBUUKGge1j9semlSwGo7D42TkXW/u', 'partner', 'Amit', 'Sharma', 
 'IN', '+919876543210', '+919812345678', 'Sharma Enterprises', '<EMAIL>', 
 true, NOW(), 0, NULL, NOW(), NOW());
