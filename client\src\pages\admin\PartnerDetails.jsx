import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
    CardFooter
} from "@/components/ui/card";
import {
    Alert,
    AlertDescription,
} from "@/components/ui/alert";
import {
    Ta<PERSON>,
    TabsContent,
    TabsList,
    TabsTrigger,
} from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Building2, Mail, Phone, MapPin, Wallet, Package, User, Loader2, Flag, ChevronLeft, ChevronRight } from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";
import api from '@/lib/axios';
import { format } from 'date-fns';

import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";


export default function PartnerDetails() {
    const { id } = useParams();
    const navigate = useNavigate();
    const { toast } = useToast();
    const [partner, setPartner] = useState(null);
    const [loading, setLoading] = useState(true);
    const [walletLoading, setWalletLoading] = useState(true);
    const [activeTab, setActiveTab] = useState("details");
    const [walletData, setWalletData] = useState({ balance: 0, maxBalance: 0 });
    const [showFundDialog, setShowFundDialog] = useState(false);
    const [fundAmount, setFundAmount] = useState('');
    const [fundDescription, setFundDescription] = useState('');
    const [addingFunds, setAddingFunds] = useState(false);
    const [showMaxBalanceDialog, setShowMaxBalanceDialog] = useState(false);
    const [newMaxBalance, setNewMaxBalance] = useState('');
    const [updatingMaxBalance, setUpdatingMaxBalance] = useState(false);
    const [showEditFundDialog, setShowEditFundDialog] = useState(false);
    const [editAmount, setEditAmount] = useState('');
    const [editDescription, setEditDescription] = useState('');
    const [transactions, setTransactions] = useState([]);
    const [transactionPage, setTransactionPage] = useState(1);
    const [transactionLimit] = useState(10);
    const [orders, setOrders] = useState([]);
    const [searchTerm] = useState('');
    const [orderPage, setOrderPage] = useState(1);
    const [totalOrderPages, setTotalOrderPages] = useState(1);
    const [orderLimit] = useState(10);

    const getStatusColor = (status) => {
        const colors = {
            'completed': 'bg-green-100 text-green-800',
            'expired': 'bg-gray-100 text-gray-800',
            'pending': 'bg-yellow-100 text-yellow-800',
            'failed': 'bg-red-100 text-red-800',
            'cancelled': 'bg-red-100 text-red-800'
        };
        return colors[status.toLowerCase()] || 'bg-gray-100 text-gray-800';
    };


    const fetchPartnerDetails = useCallback(async () => {
        try {
            const response = await api.get(`/api/partners/${id}`);
            setPartner(response.data);
        } catch (error) {
            // console.error('Error fetching partner details:', error);
            toast({
                title: "Error",
                description: "Failed to fetch partner details",
                variant: "destructive"
            });
            navigate('/admin/partners');
        } finally {
            setLoading(false);
        }
    }, [id, navigate, toast]);

    const fetchOrders = useCallback(async () => {
        try {
            const response = await api.get(`/api/orders/admin/user/${id}`, {
                params: {
                    page: orderPage,
                    limit: orderLimit
                }
            });
            setOrders(response.data.orders);
            setTotalOrderPages(response.data.totalPages);
        } catch (error) {
            // console.error('Error fetching orders:', error);
        }
    }, [id, orderPage, orderLimit]);

    const fetchWalletData = useCallback(async () => {
        try {
            setWalletLoading(true);
            const response = await api.get(`/api/users/${id}/wallet`);
            setWalletData(response.data || { balance: 0, maxBalance: 0 });

            // Fetch transactions
            const transactionsResponse = await api.get(`/api/partners/${id}/wallet/transactions`, {
                params: {
                    page: transactionPage,
                    limit: transactionLimit
                }
            });
            setTransactions(transactionsResponse.data.transactions);
        } catch (error) {
            // console.error('Error fetching wallet data:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to fetch wallet information"
            });
            setWalletData({ balance: 0, maxBalance: 0 });
        } finally {
            setWalletLoading(false);
        }
    }, [id, transactionPage, transactionLimit, toast]);

    useEffect(() => {
        if (id) {
            fetchPartnerDetails();
            fetchWalletData();
            fetchOrders();
        }
    }, [id, transactionPage, orderPage, fetchPartnerDetails, fetchWalletData, fetchOrders]);

    const filteredOrders = orders.filter(order => {
        const searchString = searchTerm.toLowerCase();
        return (
            order.id.toString().includes(searchString) ||
            order.planName.toLowerCase().includes(searchString) ||
            order.status.toLowerCase().includes(searchString) ||
            (order.iccid && order.iccid.toLowerCase().includes(searchString))
        );
    });

    const handleAddFunds = async (e) => {
        e.preventDefault();

        if (addingFunds) return; // Prevent multiple submissions

        try {
            const amount = Number(fundAmount);
            if (isNaN(amount) || amount <= 0) {
                toast({
                    variant: "destructive",
                    title: "Invalid amount",
                    description: "Please enter a valid amount greater than 0"
                });
                return;
            }

            // Check if adding this amount would exceed maximum balance
            const currentBalance = walletData?.balance || 0;
            const maxBalance = walletData?.maxBalance || 0;
            if (maxBalance > 0 && (currentBalance + amount) > maxBalance) {
                toast({
                    variant: "destructive",
                    title: "Maximum balance exceeded",
                    description: `Adding $${amount.toFixed(2)} would exceed the maximum balance limit of $${maxBalance.toFixed(2)}. Available limit: $${(maxBalance - currentBalance).toFixed(2)}`
                });
                return;
            }

            setAddingFunds(true);

            const response = await api.post(`/api/partners/${id}/central-wallet/add-funds`, {
                amount,
                description: fundDescription
            });

            // Check if response is successful and has the expected structure
            if (response.data && response.data.partnerWallet && typeof response.data.partnerWallet.balance !== 'undefined') {
                setWalletData(prev => ({
                    ...prev,
                    balance: Number(response.data.partnerWallet.balance)
                }));

                setFundAmount('');
                setFundDescription('');
                setShowFundDialog(false);

                await fetchWalletData();

                toast({
                    title: "Success",
                    description: response.data.message || "Funds added successfully"
                });
            } else {
                throw new Error('Invalid response structure from server');
            }
        } catch (error) {
            // console.error('Error adding funds:', error);

            // Handle specific error types
            const errorData = error.response?.data;
            let errorMessage = "Failed to add funds";
            let errorTitle = "Error";

            if (errorData) {
                switch (errorData.error) {
                    case 'INSUFFICIENT_CENTRAL_FUNDS':
                        errorTitle = "Insufficient Central Wallet Funds";
                        errorMessage = `Not enough funds available in central wallet. Available: $${errorData.available?.toFixed(2) || '0.00'}, Requested: $${errorData.requested?.toFixed(2) || '0.00'}`;
                        break;
                    case 'MAX_BALANCE_EXCEEDED':
                        errorTitle = "Maximum Balance Exceeded";
                        errorMessage = "Adding this amount would exceed the partner's maximum wallet balance limit";
                        break;
                    case 'INVALID_AMOUNT':
                        errorTitle = "Invalid Amount";
                        errorMessage = "Please enter a valid amount greater than 0";
                        break;
                    default:
                        errorMessage = errorData.message || errorMessage;
                }
            }

            toast({
                variant: "destructive",
                title: errorTitle,
                description: errorMessage
            });
        } finally {
            setAddingFunds(false);
        }
    };

    const handleSetMaxBalance = async (e) => {
        e.preventDefault();
        try {
            const maxBalance = Number(newMaxBalance);
            if (isNaN(maxBalance) || maxBalance <= 0) {
                toast({
                    variant: "destructive",
                    title: "Invalid amount",
                    description: "Please enter a valid amount greater than 0"
                });
                return;
            }

            const response = await api.post(`/api/users/${id}/wallet/set-max-balance`, {
                maxBalance
            });

            setWalletData(prev => ({
                ...prev,
                maxBalance: Number(response.data.maxBalance)
            }));

            setNewMaxBalance('');
            setShowMaxBalanceDialog(false);

            toast({
                title: "Success",
                description: "Maximum balance updated successfully"
            });
        } catch (error) {
            // console.error('Error setting max balance:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: error.response?.data?.message || "Failed to update maximum balance"
            });
        }
    };


    const handleEditFunds = async (e) => {
        e.preventDefault();

        if (updatingMaxBalance) return; // Prevent multiple submissions

        try {
            const newAmount = Number(editAmount);
            if (isNaN(newAmount) || newAmount < 0) {
                toast({
                    variant: "destructive",
                    title: "Invalid amount",
                    description: "Please enter a valid amount greater than or equal to 0"
                });
                return;
            }

            // Check if new amount exceeds maximum balance
            const maxBalance = walletData?.maxBalance || 0;
            if (maxBalance > 0 && newAmount > maxBalance) {
                toast({
                    variant: "destructive",
                    title: "Maximum balance exceeded",
                    description: `The amount $${newAmount.toFixed(2)} exceeds the maximum balance limit of $${maxBalance.toFixed(2)}`
                });
                return;
            }

            setUpdatingMaxBalance(true);

            const response = await api.put(`/api/partners/${id}/central-wallet/edit-funds`, {
                newAmount,
                currentAmount: walletData?.balance || 0,
                description: editDescription
            });

            // Check if response is successful and has the expected structure
            if (response.data && response.data.partnerWallet && typeof response.data.partnerWallet.balance !== 'undefined') {
                setWalletData(prev => ({
                    ...prev,
                    balance: Number(response.data.partnerWallet.balance)
                }));

                setEditAmount('');
                setEditDescription('');
                setShowEditFundDialog(false);

                await fetchWalletData();

                toast({
                    title: "Success",
                    description: response.data.message || "Wallet balance adjusted successfully"
                });
            } else {
                throw new Error('Invalid response structure from server');
            }
        } catch (error) {
            // console.error('Full Response Error:', error.response);
            // console.error('Error editing funds:', error);

            // Handle specific error types
            const errorData = error.response?.data;
            let errorMessage = "Failed to edit wallet balance";
            let errorTitle = "Error";

            if (errorData) {
                switch (errorData.error) {
                    case 'INSUFFICIENT_CENTRAL_FUNDS':
                        errorTitle = "Insufficient Central Wallet Funds";
                        errorMessage = `Not enough funds available in central wallet to complete this adjustment. Available: $${errorData.details?.available?.toFixed(2) || '0.00'}, Requested: $${errorData.details?.requested?.toFixed(2) || '0.00'}`;
                        break;
                    case 'MAX_BALANCE_EXCEEDED':
                        errorTitle = "Maximum Balance Exceeded";
                        errorMessage = `The new balance $${errorData.details?.newBalance?.toFixed(2) || '0.00'} would exceed the maximum wallet balance limit of $${errorData.details?.maxBalance?.toFixed(2) || '0.00'}`;
                        break;
                    case 'INVALID_AMOUNT':
                        errorTitle = "Invalid Amount";
                        errorMessage = "Please enter a valid amount greater than or equal to 0";
                        break;
                    case 'BALANCE_MISMATCH':
                        errorTitle = "Balance Mismatch";
                        errorMessage = `Current balance has changed. Expected: $${errorData.details?.provided?.toFixed(2) || '0.00'}, Actual: $${errorData.details?.actual?.toFixed(2) || '0.00'}. Please refresh and try again.`;
                        // Refresh wallet data automatically
                        fetchWalletData();
                        break;
                    case 'WALLET_NOT_FOUND':
                        errorTitle = "Wallet Not Found";
                        errorMessage = "Partner wallet not found. Please refresh the page and try again.";
                        break;
                    case 'DATABASE_VALIDATION_ERROR':
                        errorTitle = "Validation Error";
                        errorMessage = errorData.message || "Database validation failed. Please check your input and try again.";
                        break;
                    default:
                        errorMessage = errorData.message || errorMessage;
                        if (errorData.error) {
                            errorTitle = `Error: ${errorData.error}`;
                        }
                }
            } else if (error.message) {
                errorMessage = error.message;
            }

            toast({
                variant: "destructive",
                title: errorTitle,
                description: errorMessage
            });
        } finally {
            setUpdatingMaxBalance(false);
        }
    };



    if (loading) {
        return (
            <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
                <div className="flex flex-col items-center gap-4">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <p className="text-sm text-muted-foreground">Loading partner details...</p>
                </div>
            </div>
        );
    }

    if (!partner) {
        return (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)] gap-4">
                <Alert className="max-w-md">
                    <AlertDescription>
                        Partner not found or you don't have permission to view this page.
                    </AlertDescription>
                </Alert>
                <Button
                    variant="outline"
                    onClick={() => navigate('/admin/partners')}
                    className="gap-2"
                >
                    <ArrowLeft className="h-4 w-4" />
                    Back to Partners
                </Button>
            </div>
        );
    }

    const InfoItem = ({ icon: Icon, label, value, className = "" }) => (
        <div className={`flex items-start gap-3 ${className}`}>
            <Icon className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div className="space-y-1">
                <p className="text-sm text-muted-foreground">{label}</p>
                {typeof value === 'string' ? (
                    <p className="font-medium">{value || 'N/A'}</p>
                ) : (
                    <div className="font-medium space-y-1">
                        {value}
                    </div>
                )}
            </div>
        </div>
    );


const WalletSection = () => {
  // Safely extract wallet data with fallbacks
  const currentBalance = walletData?.balance ?? 0;
  const maxBalance = walletData?.maxBalance ?? 0;
  const availableLimit = maxBalance > 0 ? Math.max(0, maxBalance - currentBalance) : 0;

  return (
    <div className="space-y-4">
      {walletLoading ? (
        <div className="flex items-center justify-center p-6">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : (
        <div className="rounded-2xl border border-gray-200 bg-white shadow-sm p-6 space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-lg text-gray-800 font-semibold">Current Balance</span>
            <span className="text-3xl font-bold text-green-600">
              ${Number(currentBalance).toFixed(2)}
            </span>
          </div>

          <div className="flex justify-between items-center border-t pt-4">
            <span className="text-sm text-gray-500">Maximum Balance Allowed</span>
            <span className="text-sm text-gray-600">
              ${Number(maxBalance).toFixed(2)}
            </span>
          </div>

          {maxBalance > 0 && (
            <div className="flex justify-between items-center border-t pt-4">
              <span className="text-sm text-gray-500">Available Limit</span>
              <span className="text-sm text-blue-600 font-medium">
                ${Number(availableLimit).toFixed(2)}
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};


    return (
        <div className="container mx-auto py-6 max-w-7xl">
            <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-4">
                    <Button onClick={() => navigate('/admin/partners')} className="mt-6">
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back
                    </Button>
                    <div>
                        <h1 className="text-2xl font-bold">{partner.businessName || `${partner.firstName} ${partner.lastName}`}</h1>
                        <p className="text-sm text-muted-foreground">Partner ID: {id}</p>
                    </div>
                </div>
                <div className="flex items-center gap-3">
                    <Badge variant="outline" className="h-7">
                        Active Partner
                    </Badge>
                    <Button
                        onClick={() => navigate(`/admin/partners/edit/${id}`)}
                        className="gap-2 bg-blue-700 text-white"
                    >
                        Edit Partner
                    </Button>
                </div>
            </div>

            <Tabs defaultValue={activeTab} className="w-full" onValueChange={setActiveTab}>
                <TabsList className="grid w-full max-w-[400px] grid-cols-3">
                    <TabsTrigger value="details" className="gap-2">
                        <User className="h-4 w-4" />
                        Details
                    </TabsTrigger>
                    <TabsTrigger value="orders" className="gap-2">
                        <Package className="h-4 w-4" />
                        Orders
                    </TabsTrigger>
                    <TabsTrigger value="wallet" className="gap-2">
                        <Wallet className="h-4 w-4" />
                        Wallet
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="details">
                    <div className="grid gap-6 grid-cols-1 lg:grid-cols-1">
                        <Card className="lg:col-span-2 shadow-lg border border-gray-200">
                            <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-500 p-6 rounded-t-lg">
                                <CardTitle className="flex items-center gap-2 text-white">
                                    <User className="h-6 w-6" />
                                    Partner Information
                                </CardTitle>
                                <CardDescription className="text-blue-200">View and manage partner details</CardDescription>
                            </CardHeader>
                            <CardContent className="grid gap-8 p-6">
                                <div className="grid gap-6 sm:grid-cols-2">
                                    <div className="space-y-4">
                                        <h3 className="font-semibold text-sm text-blue-600 uppercase tracking-wider">
                                            Personal Information
                                        </h3>
                                        <div className="space-y-3">
                                            <InfoItem
                                                icon={User}
                                                label="Full Name"
                                                value={`${partner.firstName} ${partner.lastName}`}
                                                iconClass="text-blue-500"
                                            />
                                            <InfoItem
                                                icon={Mail}
                                                label="Email Address"
                                                value={partner.email}
                                                iconClass="text-blue-500"
                                            />
                                            <InfoItem
                                                icon={Phone}
                                                label="Phone Number"
                                                value={partner.phoneNumber}
                                                iconClass="text-blue-500"
                                            />
                                            <InfoItem
                                                icon={Flag}
                                                label="Country"
                                                value={partner.country.name}
                                                iconClass="text-blue-500"
                                            />
                                        </div>
                                    </div>
                                    <div className="space-y-4">
                                        <h3 className="font-semibold text-sm text-blue-600 uppercase tracking-wider">
                                            Business Information
                                        </h3>
                                        <div className="space-y-3">
                                            <InfoItem
                                                icon={Building2}
                                                label="Business Name"
                                                value={partner.businessName}
                                                iconClass="text-blue-500"
                                            />
                                            <InfoItem
                                                icon={Mail}
                                                label="Business Email"
                                                value={partner.businessEmail}
                                                iconClass="text-blue-500"
                                            />
                                        </div>
                                        <div className="space-y-3">
                                            <InfoItem
                                                icon={Building2}
                                                label="Markup Percentage"
                                                value={`${partner.markupPercentage}%`}
                                                iconClass="text-blue-500"
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className="space-y-4">
                                    <h3 className="font-semibold text-sm text-blue-600 uppercase tracking-wider">
                                        Billing Address
                                    </h3>
                                    <div className="grid gap-3 sm:grid-cols-2">
                                        <InfoItem
                                            icon={MapPin}
                                            label="Address"
                                            value={
                                                <div>
                                                    <p>{partner.billingAddressLine1}</p>
                                                    {partner.billingAddressLine2 && (
                                                        <p>{partner.billingAddressLine2}</p>
                                                    )}
                                                </div>
                                            }
                                            iconClass="text-blue-500"
                                        />
                                        <InfoItem
                                            icon={MapPin}
                                            label="City & Province"
                                            value={`${partner.billingCity}, ${partner.billingProvince}`}
                                            iconClass="text-blue-500"
                                        />
                                        <InfoItem
                                            icon={MapPin}
                                            label="Country"
                                            value={partner.country.name}
                                            iconClass="text-blue-500"
                                        />
                                        <InfoItem
                                            icon={MapPin}
                                            label="Postal Code"
                                            value={partner.billingPostalCode}
                                            iconClass="text-blue-500"
                                        />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent value="orders">
                    <Card>
                        <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-500 mb-4 rounded-t-lg">
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle className="text-white">Orders</CardTitle>
                                    <CardDescription className="text-blue-200">View and manage partner's orders</CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="bg-white rounded-lg shadow-sm border flex-1 flex flex-col min-h-[500px]">
                                <div className="overflow-x-auto flex-1">
                                    <Table>
                                        <TableHeader>
                                            <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100">
                                                <TableHead className="w-[100px]">#Order ID</TableHead>
                                                <TableHead className="w-[200px]">Plan</TableHead>
                                                <TableHead className="w-[200px]">ICCID</TableHead>
                                                <TableHead className="w-[100px]">Quantity</TableHead>
                                                <TableHead className="w-[100px]">Amount</TableHead>
                                                <TableHead className="w-[100px]">Status</TableHead>
                                                <TableHead className="w-[200px]">Date</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {loading ? (
                                                <TableRow>
                                                    <TableCell colSpan={6} className="text-center py-8">
                                                        Loading orders...
                                                    </TableCell>
                                                </TableRow>
                                            ) : filteredOrders.length === 0 ? (
                                                <TableRow>
                                                    <TableCell colSpan={6} className="text-center py-8">
                                                        <div className="flex flex-col items-center justify-center py-12">
                                                            <Package className="h-12 w-12 text-muted-foreground mb-4" />
                                                            <p className="text-muted-foreground">No orders found</p>
                                                            <p className="text-sm text-muted-foreground mt-1">Orders will appear here once the partner makes their first purchase</p>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ) : (
                                                filteredOrders.map((order) => (
                                                    <TableRow key={order.id}>
                                                        <TableCell className="font-medium">#{order.id}</TableCell>
                                                        <TableCell>{order.plan.name}</TableCell>
                                                        <TableCell>{order.iccid}</TableCell>
                                                        <TableCell>{order.quantity}</TableCell>
                                                        <TableCell>${parseFloat(order.orderTotal).toFixed(2)}</TableCell>
                                                        <TableCell>
                                                            <Badge variant="secondary" className={getStatusColor(order.status)}>
                                                                {order.status}
                                                            </Badge>
                                                        </TableCell>
                                                        <TableCell>{format(new Date(order.createdAt), 'MMM dd, yyyy')}</TableCell>
                                                    </TableRow>
                                                ))
                                            )}
                                        </TableBody>
                                    </Table>
                                </div>
                                {/* Pagination */}
                                {!loading && filteredOrders.length > 0 && (
                                    <div className="flex items-center justify-between px-4 py-4 border-t">
                                        <div className="flex-1 text-sm text-gray-500">
                                            Page {orderPage} of {totalOrderPages}
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => setOrderPage(prev => Math.max(1, prev - 1))}
                                                disabled={orderPage === 1}
                                            >
                                                <ChevronLeft className="h-4 w-4" />
                                                Previous
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => setOrderPage(prev => Math.min(totalOrderPages, prev + 1))}
                                                disabled={orderPage === totalOrderPages}
                                            >
                                                Next
                                                <ChevronRight className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="wallet" className="space-y-4">
                    <Card className="shadow-lg border border-gray-200">
                        <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-500 p-6 rounded-t-lg">
                            <CardTitle className="text-white">Wallet Information</CardTitle>
                            <CardDescription className="text-blue-200">Manage partner's wallet balance and transactions</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <WalletSection />
                            <div className="flex gap-4 mt-4">
                                <Button onClick={() => setShowFundDialog(true)} className="bg-blue-700 text-white">
                                    Add Funds
                                </Button>
                                <Button onClick={() => setShowEditFundDialog(true)} className="bg-blue-700 text-white">
                                    Edit Wallet Balance
                                </Button>
                                <Button variant="outline" onClick={() => setShowMaxBalanceDialog(true)} className="bg-blue-700 text-white">
                                    Set Maximum Balance
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Transactions Card */}
                    <Card>
                        <CardHeader className="bg-gradient-to-r from-blue-500 to-blue-300 p-6 rounded-t-lg">
                            <CardTitle className="text-white">Transaction History</CardTitle>
                            <CardDescription className="text-blue-200">View all wallet transactions</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {walletLoading ? (
                                <div className="flex items-center justify-center p-8">
                                    <Loader2 className="h-8 w-8 animate-spin" />
                                </div>
                            ) : transactions.length > 0 ? (
                                <div className="space-y-4">
                                    <div className="rounded-lg border">
                                        <Table>
                                            <TableHeader>
                                                <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100">
                                                    <TableHead>Transaction ID</TableHead>
                                                    <TableHead>Date</TableHead>
                                                    <TableHead>Type</TableHead>
                                                    <TableHead>Description</TableHead>
                                                    <TableHead>Amount</TableHead>
                                                    <TableHead className="text-right">Balance</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {transactions.map((transaction) => (
                                                    <TableRow key={transaction.id}>
                                                        <TableCell className="font-medium">{transaction.id}</TableCell>
                                                        <TableCell>
                                                            {format(new Date(transaction.createdAt), 'MMM dd, yyyy HH:mm')}
                                                        </TableCell>
                                                        <TableCell>
                                                            <Badge 
                                                                variant={transaction.type === 'credit' ? 'success' : 'destructive'}
                                                            >
                                                                {transaction.type === 'credit' ? 'Credit' : 'Debit'}
                                                            </Badge>
                                                        </TableCell>
                                                        <TableCell>{transaction.description}</TableCell>
                                                        <TableCell className={transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'}>
                                                            {transaction.type === 'credit' ? '+' : '-'}${Number(transaction.amount || 0).toFixed(2)}
                                                        </TableCell>
                                                        <TableCell className="text-right">
                                                            ${Number(transaction.balance || 0).toFixed(2)}
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </div>

                                    {/* Pagination */}
                                    <div className="flex justify-center gap-2 mt-4">
                                        <Button
                                            variant="outline"
                                            onClick={() => setTransactionPage(prev => Math.max(1, prev - 1))}
                                            disabled={transactionPage === 1}
                                        >
                                            <ChevronLeft className="h-4 w-4 mr-2" />
                                            Previous
                                        </Button>
                                        <Button
                                            variant="outline"
                                            onClick={() => setTransactionPage(prev => prev + 1)}
                                            disabled={transactions.length < transactionLimit}
                                        >
                                            Next
                                            <ChevronRight className="h-4 w-4 ml-2" />
                                        </Button>
                                    </div>
                                </div>
                            ) : (
                                <div className="flex flex-col items-center justify-center py-8">
                                    <Wallet className="h-12 w-12 text-gray-400 mb-4" />
                                    <p className="text-gray-500">No transactions found</p>
                                    <p className="text-sm text-gray-400 mt-1">
                                        Transaction history will appear here once the wallet is funded
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>

            <Dialog open={showFundDialog} onOpenChange={setShowFundDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Add Funds to Wallet</DialogTitle>
                        <DialogDescription>
                            Enter the amount you want to add to the partner's wallet.
                        </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleAddFunds}>
                        <div className="grid gap-4 py-4">
                            <div className="grid gap-2">
                                <Label htmlFor="amount">Amount ($)</Label>
                                <Input
                                    id="amount"
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    value={fundAmount}
                                    onChange={(e) => setFundAmount(e.target.value)}
                                    placeholder="Enter amount"
                                />
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="description">Description</Label>
                                <Input
                                    id="description"
                                    value={fundDescription}
                                    onChange={(e) => setFundDescription(e.target.value)}
                                    placeholder="Enter description"
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button
                                variant="outline"
                                onClick={() => setShowFundDialog(false)}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={addingFunds || !fundAmount}
                            >
                                {addingFunds ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Adding Funds...
                                    </>
                                ) : (
                                    'Add Funds'
                                )}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>

            <Dialog open={showEditFundDialog} onOpenChange={setShowEditFundDialog}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Edit Wallet Balance</DialogTitle>
                        <DialogDescription>
                            Adjust the partner's wallet balance. Reducing the balance will return funds to the central wallet.
                        </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleEditFunds}>
                        <div className="grid gap-4 py-4">
                            <div className="grid gap-2">
                                <Label htmlFor="edit-amount">New Balance Amount</Label>
                                <Input
                                    id="edit-amount"
                                    type="number"
                                    step="0.01"
                                    value={editAmount}
                                    onChange={(e) => setEditAmount(e.target.value)}
                                    placeholder="Enter new balance amount"
                                />
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="edit-description">Description</Label>
                                <Input
                                    id="edit-description"
                                    value={editDescription}
                                    onChange={(e) => setEditDescription(e.target.value)}
                                    placeholder="Reason for adjustment"
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button type="submit">Save Changes</Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>

            <Dialog open={showMaxBalanceDialog} onOpenChange={setShowMaxBalanceDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Set Maximum Balance</DialogTitle>
                        <DialogDescription>
                            Set the maximum amount that can be held in this wallet.
                        </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleSetMaxBalance}>
                        <div className="grid gap-4 py-4">
                            <div className="grid gap-2">
                                <Label htmlFor="maxBalance">Maximum Balance ($)</Label>
                                <Input
                                    id="maxBalance"
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    value={newMaxBalance}
                                    onChange={(e) => setNewMaxBalance(e.target.value)}
                                    placeholder="Enter maximum balance"
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button
                                variant="outline"
                                onClick={() => setShowMaxBalanceDialog(false)}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={updatingMaxBalance || !newMaxBalance}
                            >
                                {updatingMaxBalance ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Updating...
                                    </>
                                ) : (
                                    'Set Maximum Balance'
                                )}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
}