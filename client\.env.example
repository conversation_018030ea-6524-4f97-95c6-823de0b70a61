# Development (default)
VITE_API_URL=http://localhost:3000
VITE_NODE_ENV=development

# Production (when building for production)
# VITE_API_URL=https://esim-app.onrender.com
# VITE_NODE_ENV=production

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_MOCK_API=false

# Other Configuration
VITE_APP_NAME="eSIM Management"
VITE_APP_VERSION="1.0.0"

# CORS Settings
VITE_CORS_ENABLED=true
VITE_ALLOWED_ORIGINS="http://localhost:5000,http://localhost:5173,https://esim-management.vercel.app,http://esim-management.vercel.app,https://www.esim-management.vercel.app" 