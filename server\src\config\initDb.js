const bcrypt = require('bcrypt');
const sequelize = require('./database');
const User = require('../models/User');
const OTP = require('../models/OTP');

const initializeDatabase = async () => {
    try {
        // Sync all models with database
        await sequelize.sync({ force: true });
        console.log('Database synchronized');

        // Create initial admin user
        const hashedPassword = await bcrypt.hash('admin123', 10);
        await User.create({
            email: '<EMAIL>',
            password: hashedPassword,
            role: 'admin',
            firstName: 'Admin',
            lastName: 'User',
            isActive: true
        });

        console.log('Initial admin user created');
        console.log('Database initialization completed successfully');
    } catch (error) {
        console.error('Database initialization failed:', error);
        process.exit(1);
    }
};

// If this file is run directly (not required as a module)
if (require.main === module) {
    initializeDatabase();
}

module.exports = initializeDatabase;
