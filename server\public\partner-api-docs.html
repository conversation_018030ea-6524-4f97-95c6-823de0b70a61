<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partner API Documentation</title>
    
    <!-- Using local CSS resources to avoid CSP issues -->
    <link href="/api/config/styles/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/api/config/styles/highlight.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 2rem;
            padding-bottom: 2rem;
            scroll-behavior: smooth;
        }
        .container {
            max-width: 1200px;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        h1 {
            border-bottom: 2px solid #eaecef;
            padding-bottom: 0.3rem;
        }
        h2 {
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.3rem;
        }
        pre {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 16px;
            overflow: auto;
        }
        code {
            font-family: SFMono-Regular, Consolas, Liberation Mono, <PERSON>lo, monospace;
            font-size: 85%;
        }
        .endpoint {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 2rem;
            border-left: 4px solid #007bff;
        }
        .method {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
        }
        .get {
            background-color: #28a745;
        }
        .post {
            background-color: #007bff;
        }
        .try-api {
            margin-top: 15px;
        }
        .nav-link.active {
            background-color: #007bff !important;
            color: white !important;
        }
        .tab-content {
            padding: 20px;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 0.25rem 0.25rem;
        }
        .copy-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            z-index: 10;
        }
        .response-container {
            max-height: 300px;
            overflow-y: auto;
        }
        #api-keys-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 2rem;
        }
        /* New styles for endpoint navigation */
        .list-group-item-action:hover {
            background-color: #f0f8ff;
            border-left: 3px solid #007bff;
            transition: all 0.2s ease;
        }
        .endpoints-nav {
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        html {
            scroll-behavior: smooth;
            scroll-padding-top: 20px;
        }
        .endpoint {
            scroll-margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Partner API Documentation</h1>
            <p class="lead">
                This API allows authorized partners to fetch product data, place and track orders, and view usage information.
            </p>
        </header>

        <section id="api-keys-section">
            <h2>API Authentication</h2>
            <p>All API requests require the following HTTP headers:</p>
            <ul>
                <li><code>Authorization: Bearer {your_api_key}</code></li>
                <li><code>X-Partner-ID: {your_partner_id}</code></li>
            </ul>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="auth-api-key" class="form-label">API Key</label>
                    <input type="text" class="form-control" id="auth-api-key" placeholder="Enter your API key">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="auth-partner-id" class="form-label">Partner ID</label>
                    <input type="text" class="form-control" id="auth-partner-id" placeholder="Enter your Partner ID">
                </div>
            </div>
            <div class="mb-3">
                <label for="base-url" class="form-label">Base URL</label>
                <select class="form-select" id="base-url">
                  <!-- Options will be populated dynamically from API_CONFIG -->
                </select>
            </div>
            <div class="alert alert-info">
                <strong>Note:</strong> You can obtain your API key and Partner ID from the Partner Portal.
            </div>
        </section>

        <section id="endpoints">
            <h2>Endpoints</h2>

            <!-- Endpoints Quick Navigation -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h3 class="h5 mb-0">All Endpoints</h3>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="#get-products" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <div class="mb-2 mb-md-0">
                                    <span class="badge bg-success me-2">GET</span>
                                    <span class="fw-bold">/products</span>
                                </div>
                                <small class="text-muted">Returns a list of all available products</small>
                            </div>
                        </a>
                        <a href="#get-order" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <div class="mb-2 mb-md-0">
                                    <span class="badge bg-success me-2">GET</span>
                                    <span class="fw-bold">/order/{orderId}</span>
                                </div>
                                <small class="text-muted">Retrieve order details using order ID</small>
                            </div>
                        </a>
                        <a href="#create-order" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <div class="mb-2 mb-md-0">
                                    <span class="badge bg-primary me-2">POST</span>
                                    <span class="fw-bold">/order</span>
                                </div>
                                <small class="text-muted">Places a new order for an eSIM product</small>
                            </div>
                        </a>
                        <a href="#get-usage" class="list-group-item list-group-item-action">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <div class="mb-2 mb-md-0">
                                    <span class="badge bg-success me-2">GET</span>
                                    <span class="fw-bold">/usage/{orderId}</span>
                                </div>
                                <small class="text-muted">Returns usage details for a specific order</small>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Get Products -->
            <div class="endpoint" id="get-products">
                <h3>
                    <span class="method get">GET</span>
                    /products
                </h3>
                <p>Returns a list of all available products.</p>

                <ul class="nav nav-tabs" id="getProductsTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="getProducts-example-tab" data-bs-toggle="tab" data-bs-target="#getProducts-example" type="button" role="tab" aria-controls="getProducts-example" aria-selected="true">Example</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="getProducts-try-tab" data-bs-toggle="tab" data-bs-target="#getProducts-try" type="button" role="tab" aria-controls="getProducts-try" aria-selected="false">Try it</button>
                    </li>
                </ul>
                <div class="tab-content" id="getProductsTabContent">
                    <div class="tab-pane fade show active" id="getProducts-example" role="tabpanel" aria-labelledby="getProducts-example-tab">
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="getProducts-curl">Copy</button>
                            <pre><code class="language-bash" id="getProducts-curl">curl -X GET "https://partner-api.your-domain.com/api/v1/products" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id"</code></pre>
                        </div>
                        <h5>Response</h5>
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="getProducts-response">Copy</button>
                            <pre><code class="language-json" id="getProducts-response">{
  "success": true,
  "data": {
    "products": [
      {
        "productId": "ABCDEF123456",
        "name": "Europe 5GB / 30 Days",
        "description": "5GB data valid for 30 days across Europe",
        "planInfo": "5GB data valid for 30 days across Europe with unlimited calls", //if description is null, you can use this field to display the plan info
        "instructions": null,
        "price": 29.99,
        "validityDays": 30,
        "countries": ["FR", "DE", "IT", "ES", "NL"],
        "region": ["Europe", "Asia"],
        "dataAmount": 5,
        "dataUnit": "GB",
        "customPlanData": "2gb 460 kb/sec",
        "voiceMin": 50,
        "voiceMinUnit": "Min",
        "speed": "Unrestricted",
        "planType": "Fixed",
        "category": "esim_realtime", // esim_realtime, esim_addon, esim_replacement
        "networkType": "4G/LTE",
        "isVoiceAvailable": true,
        "isSmsAvailable": false,
        "hotspotAvailable": true,
        "topUpAvailable": false,
        "profile": "local",
        "activationPolicy": "Activation upon purchase",
        "startDateEnabled": false,
        "features": ["5G Support", "Unlimited Calls"]
      }
    ]
  }
}</code></pre>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="getProducts-try" role="tabpanel" aria-labelledby="getProducts-try-tab">
                        <div class="try-it-section">
                            <button class="btn btn-primary try-api" data-endpoint="/products" data-method="GET">Send Request</button>
                            <div class="response-container mt-3">
                                <h5>Response</h5>
                                <pre><code class="language-json" id="getProducts-try-response"></code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Get Order -->
            <div class="endpoint" id="get-order">
                <h3>
                    <span class="method get">GET</span>
                    /order/{orderId}
                </h3>
                <p>Retrieve order details using order ID.</p>

                <ul class="nav nav-tabs" id="getOrderTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="getOrder-example-tab" data-bs-toggle="tab" data-bs-target="#getOrder-example" type="button" role="tab" aria-controls="getOrder-example" aria-selected="true">Example</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="getOrder-try-tab" data-bs-toggle="tab" data-bs-target="#getOrder-try" type="button" role="tab" aria-controls="getOrder-try" aria-selected="false">Try it</button>
                    </li>
                </ul>
                <div class="tab-content" id="getOrderTabContent">
                    <div class="tab-pane fade show active" id="getOrder-example" role="tabpanel" aria-labelledby="getOrder-example-tab">
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="getOrder-curl">Copy</button>
                            <pre><code class="language-bash" id="getOrder-curl">curl -X GET "https://partner-api.your-domain.com/api/v1/order/VLZ123456" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id"</code></pre>
                        </div>
                        <h5>Response</h5>
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="getOrder-response">Copy</button>
                            <pre><code class="language-json" id="getOrder-response">{
  "success": true,
  "data": {
    "orderId": "VLZ123456",
    "status": "completed",
    "product": {
      "productId": "ABCDEF123456",
      "name": "Europe 5GB / 30 Days"
    },
    "orderTotal": 29.99,
    "quantity": 1,
    "startDate": "2023-11-01",
    "expiryDate": "2023-12-01",
    "iccid": "8991000123456789012",
    "smdpAddress": "trl.prod.ondemandconnectivity.com",
    "accessPointName": "mbb",
    "lpaString": "LPA:1$trl.prod.ondemandconnectivity.com$AAA22",
    "activationCode": "LPA:1$smdp.example.com$123456789-abcdef-123456",
    "status": "completed",
    "top_up": "Available",
    "qrCodeUrl": "https://example.com/qr/VLZ123456.png",
    "walletAuthTransactionId": "sksgdnsdyk1234567890",
    "createdAt": "2023-10-25T14:30:45Z"
  }
}</code></pre>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="getOrder-try" role="tabpanel" aria-labelledby="getOrder-try-tab">
                        <div class="try-it-section">
                            <div class="mb-3">
                                <label for="order-id" class="form-label">Order ID</label>
                                <input type="text" class="form-control" id="order-id" 
                                       data-param-type="path" 
                                       data-param-name="orderId" 
                                       placeholder="Enter Order ID (e.g., VLZ123456)">
                            </div>
                            <button class="btn btn-primary try-api" data-endpoint="/order/{orderId}" data-method="GET">Send Request</button>
                            <div class="response-container mt-3">
                                <h5>Response</h5>
                                <pre><code class="language-json" id="getOrder-try-response"></code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create Order -->
            <div class="endpoint" id="create-order">
                <h3>
                    <span class="method post">POST</span>
                    /order
                </h3>
                <p>Places a new order for an eSIM product.</p>

                <ul class="nav nav-tabs" id="createOrderTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="createOrder-example-tab" data-bs-toggle="tab" data-bs-target="#createOrder-example" type="button" role="tab" aria-controls="createOrder-example" aria-selected="true">Example</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="createOrder-try-tab" data-bs-toggle="tab" data-bs-target="#createOrder-try" type="button" role="tab" aria-controls="createOrder-try" aria-selected="false">Try it</button>
                    </li>
                </ul>
                <div class="tab-content" id="createOrderTabContent">
                    <div class="tab-pane fade show active" id="createOrder-example" role="tabpanel" aria-labelledby="createOrder-example-tab">
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="createOrder-curl">Copy</button>
                            <pre><code class="language-bash" id="createOrder-curl">curl -X POST "https://partner-api.your-domain.com/api/v1/order" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id" \
  -H "Content-Type: application/json" \
  -d '{
    "productId": "ABCDEF123456",
    "startDate": "2023-11-01"
  }'</code></pre>
                        </div>
                        <h5>Response</h5>
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="createOrder-response">Copy</button>
                            <pre><code class="language-json" id="createOrder-response">{
  "success": true,
  "data": {
    "orderId": "VLZ123456",
    "status": "completed",
    "qrCodeUrl": "https://example.com/qr/VLZ123456.png",
    "iccid": "8991000123456789012",
    "smdpAddress": "trl.prod.ondemandconnectivity.com",
    "accessPointName": "mbb",
    "lpaString": "LPA:1$trl.prod.ondemandconnectivity.com$AAA22",
    "activationCode": "LPA:1$smdp.example.com$123456789-abcdef-123456",
    "expiryDate": "2023-12-01",
    "orderTotal": 29.99,
    "product": {
      "productId": "ABCDEF123456",
      "name": "Europe 5GB / 30 Days"
    }
  }
}</code></pre>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="createOrder-try" role="tabpanel" aria-labelledby="createOrder-try-tab">
                        <div class="try-it-section">
                            <div class="mb-3">
                                <label for="create-order-product-id" class="form-label">Product ID</label>
                                <input type="text" class="form-control" id="create-order-product-id" 
                                       data-param-type="body" 
                                       data-body-key="productId" 
                                       placeholder="Enter Product ID (e.g., ABCDEF123456)">
                            </div>
                            <div class="mb-3">
                                <label for="create-order-start-date" class="form-label">Start Date (Optional)</label>
                                <input type="date" class="form-control" id="create-order-start-date" 
                                       data-param-type="body" 
                                       data-body-key="startDate">
                            </div>
                            <div class="mb-3">
                                <label for="create-order-body" class="form-label">Request Body</label>
                                <textarea class="form-control" id="create-order-body" rows="5">{
  "productId": "",
  "startDate": null
}</textarea>
                            </div>
                            <button class="btn btn-primary try-api" 
                                    data-endpoint="/order" 
                                    data-method="POST" 
                                    data-body-id="create-order-body">Send Request</button>
                            <div class="response-container mt-3">
                                <h5>Response</h5>
                                <pre><code class="language-json" id="createOrder-try-response"></code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Get Usage -->
            <div class="endpoint" id="get-usage">
                <h3>
                    <span class="method get">GET</span>
                    /usage/{orderId}
                </h3>
                <p>Returns usage details for a specific order.</p>

                <ul class="nav nav-tabs" id="getUsageTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="getUsage-example-tab" data-bs-toggle="tab" data-bs-target="#getUsage-example" type="button" role="tab" aria-controls="getUsage-example" aria-selected="true">Example</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="getUsage-try-tab" data-bs-toggle="tab" data-bs-target="#getUsage-try" type="button" role="tab" aria-controls="getUsage-try" aria-selected="false">Try it</button>
                    </li>
                </ul>
                <div class="tab-content" id="getUsageTabContent">
                    <div class="tab-pane fade show active" id="getUsage-example" role="tabpanel" aria-labelledby="getUsage-example-tab">
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="getUsage-curl">Copy</button>
                            <pre><code class="language-bash" id="getUsage-curl">curl -X GET "https://partner-api.your-domain.com/api/v1/usage/VLZ123456" \
  -H "Authorization: Bearer your-api-key" \
  -H "X-Partner-ID: your-partner-id"</code></pre>
                        </div>
                        <h5>Response</h5>
                        <div class="position-relative">
                            <button class="btn btn-sm btn-outline-secondary copy-btn" data-code="getUsage-response">Copy</button>
                            <pre><code class="language-json" id="getUsage-response">{
  "success": true,
  "data": {
    "orderId": "VLZ123456",
    "totalData": 5368709120,
    "usedData": 2147483648,
    "remainingData": 3221225472,
    "status": "Active",
    "activationDate": "2023-11-01T00:00:00Z",
    "expiryDate": "2023-12-01T00:00:00Z",
    "lastUpdated": "2023-11-15T09:45:22Z",
    "usageMessage": "The operator does not yet support package status check, please refer to your device settings to check your data usage"
  }
}</code></pre>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="getUsage-try" role="tabpanel" aria-labelledby="getUsage-try-tab">
                        <div class="try-it-section">
                            <div class="mb-3">
                                <label for="usage-order-id" class="form-label">Order ID</label>
                                <input type="text" class="form-control" id="usage-order-id" 
                                       data-param-type="path" 
                                       data-param-name="orderId" 
                                       placeholder="Enter Order ID (e.g., VLZ123456)">
                            </div>
                            <button class="btn btn-primary try-api" data-endpoint="/usage/{orderId}" data-method="GET">Send Request</button>
                            <div class="response-container mt-3">
                                <h5>Response</h5>
                                <pre><code class="language-json" id="getUsage-try-response"></code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="error-codes">
            <h2>Error Codes</h2>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Code</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>401 Unauthorized</td>
                        <td>Invalid API key or partner ID</td>
                    </tr>
                    <tr>
                        <td>404 Not Found</td>
                        <td>Resource not found</td>
                    </tr>
                    <tr>
                        <td>500 Internal Server Error</td>
                        <td>General server error</td>
                    </tr>
                </tbody>
            </table>
        </section>

        <footer class="mt-5 pt-3 border-top text-muted">
            <p>&copy; 2025 eSIM Platform. All rights reserved.</p>
        </footer>
    </div>

    <!-- Using our local proxy to avoid CSP issues -->
    <script src="/api/config/scripts/bootstrap.min.js"></script>
    <script src="/api/config/scripts/highlight.min.js"></script>
    <script src="/api/config/scripts/bash.min.js"></script>
    <script src="/api/config/scripts/json.min.js"></script>
    
    <!-- Load environment variables -->
    <script src="/api/config/env.js"></script>
    
    <!-- Load the main script file instead of inline script -->
    <script src="/api/config/scripts/doc-main.js" defer></script>
    
    <!-- Script for enhanced endpoint navigation -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Enhance endpoint navigation links with smooth scrolling
            document.querySelectorAll('.list-group-item-action').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    
                    if (targetElement) {
                        // Smooth scroll to target
                        targetElement.scrollIntoView({
                            behavior: 'smooth'
                        });
                        
                        // Highlight the target section briefly
                        targetElement.classList.add('bg-light');
                        setTimeout(() => {
                            targetElement.classList.remove('bg-light');
                        }, 1500);
                        
                        // Update URL without full page reload
                        history.pushState(null, null, targetId);
                    }
                });
            });
            
            // Check if URL has a hash on page load and scroll to it
            if (location.hash) {
                const targetElement = document.querySelector(location.hash);
                if (targetElement) {
                    setTimeout(() => {
                        targetElement.scrollIntoView({
                            behavior: 'smooth'
                        });
                        
                        // Highlight the target section briefly
                        targetElement.classList.add('bg-light');
                        setTimeout(() => {
                            targetElement.classList.remove('bg-light');
                        }, 1500);
                    }, 500);
                }
            }
        });
    </script>
</body>
</html> 