import { group, sleep } from 'k6';
import auth from './auth.test.js';
import plans from './plans.test.js';
import wallet from './wallet.test.js';
import knowledge from './knowledge.test.js';
import stock from './stock.test.js';
import provider from './provider.test.js';
import security from './security.test.js';
import { CONFIG } from './config.js';

export const options = {
    thresholds: {
        ...CONFIG.thresholds,
        'group_duration{group:auth}': ['p(95)<3000'],
        'group_duration{group:plans}': ['p(95)<4000'],
        'group_duration{group:wallet}': ['p(95)<2000'],
        'group_duration{group:knowledge}': ['p(95)<2000'],
        'group_duration{group:stock}': ['p(95)<3000'],
        'group_duration{group:provider}': ['p(95)<3000'],
        'group_duration{group:security}': ['p(95)<2000'],
        'http_req_failed': ['rate<0.1'],     // Changed from failed_requests
        'http_req_blocked': ['p(95)<500']    // Changed to 500ms for more realistic threshold
    },
    scenarios: {
        full_flow: {
            executor: 'ramping-vus',
            startVUs: 0,
            stages: [
                { duration: '1m', target: 20 },   // Warm up
                { duration: '3m', target: 20 },   // Baseline load
                { duration: '2m', target: 50 },   // Ramp up load
                { duration: '5m', target: 50 },   // Sustained peak
                { duration: '2m', target: 100 },  // High load test
                { duration: '3m', target: 100 },  // Sustained high load
                { duration: '1m', target: 200 },  // Brief spike
                { duration: '2m', target: 0 }     // Ramp down
            ]
        }
    }
};

export function setup() {
    return { baseUrl: CONFIG.baseUrl };
}

export default function(data) {
    group('Authentication Flow', () => {
        auth(data);
    });
    sleep(2);

    group('Plans and Orders Flow', () => {
        plans(data);
    });
    sleep(2);

    group('Wallet Operations', () => {
        wallet(data);
    });
    sleep(2);

    group('Knowledge Base', () => {
        knowledge(data);
    });
    sleep(2);

    group('Stock Management', () => {
        stock(data);
    });
    sleep(2);

    group('Provider Operations', () => {
        provider(data);
    });
    sleep(2);

    group('Security Tests', () => {
        security(data);
    });
    sleep(2);
}
