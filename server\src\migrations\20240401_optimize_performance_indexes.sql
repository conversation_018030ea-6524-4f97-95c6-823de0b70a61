-- Performance optimization indexes for eSIM platform
-- Run this migration to significantly improve query performance

-- Drop existing indexes that will be replaced with better ones (MySQL compatible syntax)
-- Note: These will fail silently if indexes don't exist, which is fine

-- Critical composite index for the main partner query pattern
-- This covers: status, isActive, category, createdAt (for ORDER BY)
CREATE INDEX idx_esimplans_partner_main ON esimplans(status, isActive, category, createdAt DESC);

-- Index for country-based filtering through junction table
CREATE INDEX idx_esimplancountries_country_plan ON esimplancountries(countryId, esimPlanId);
CREATE INDEX idx_esimplancountries_plan_country ON esimplancountries(esimPlanId, countryId);

-- Index for region filtering with proper length
CREATE INDEX idx_esimplans_region_filter ON esimplans(region(100), status, isActive);

-- Search optimization indexes
CREATE INDEX idx_esimplans_name_search ON esimplans(name(100), status, isActive);
CREATE INDEX idx_esimplans_network_search ON esimplans(networkName(100), status, isActive);

-- Composite index for search with category
CREATE INDEX idx_esimplans_search_category ON esimplans(status, isActive, category, name(100));

-- Index for external product lookups (deduplication)
CREATE INDEX idx_esimplans_external_product ON esimplans(externalProductId(100), status);
CREATE INDEX idx_esimplans_external_sku ON esimplans(externalSkuId(100), status);

-- Provider-based queries
CREATE INDEX idx_esimplans_provider_status ON esimplans(providerId, status, isActive, createdAt DESC);

-- Pagination optimization - covers ORDER BY createdAt DESC with filters
CREATE INDEX idx_esimplans_pagination ON esimplans(status, isActive, createdAt DESC, id);

-- Countries table optimization
CREATE INDEX idx_countries_lookup ON countries(name(100), iso3, id);
CREATE INDEX idx_countries_active ON countries(isActive, name(100));

-- Providers table optimization
CREATE INDEX idx_providers_active_api ON providers(type, status, name(50));

-- Stock count optimization
CREATE INDEX idx_esimstock_plan_status ON esimstocks(esimPlanId, status);

-- User lookup optimization for partner queries
CREATE INDEX idx_users_partner_lookup ON users(id, markupPercentage, isActive);

-- Region distinct query optimization
CREATE INDEX idx_esimplans_region_distinct ON esimplans(region(100), status) WHERE region IS NOT NULL;

-- Analyze tables to update statistics
ANALYZE TABLE esimplans;
ANALYZE TABLE esimplancountries;
ANALYZE TABLE countries;
ANALYZE TABLE providers;
ANALYZE TABLE esimstocks;
ANALYZE TABLE users;
