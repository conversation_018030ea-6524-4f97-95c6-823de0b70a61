'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('EsimStocks', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      esimPlanId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'EsimPlans',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      lpaString: {
        type: Sequelize.STRING,
        allowNull: false
      },
      iccid: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      smdpAddress: {
        type: Sequelize.STRING,
        allowNull: false
      },
      accessPointName: {
        type: Sequelize.STRING,
        allowNull: false
      },
      activationCode: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      phoneNumber: {
        type: Sequelize.STRING,
        allowNull: true
      },
      confCode: {
        type: Sequelize.STRING,
        allowNull: true
      },
      walletAuthTransactionId: {
        type: Sequelize.STRING,
        allowNull: true
      },
      orderId: {
        type: Sequelize.STRING,
        allowNull: true
      },
      orderDate: {
        type: Sequelize.DATE,
        allowNull: true
      },
      status: {
        type: Sequelize.ENUM('available', 'assigned', 'activated', 'expired'),
        defaultValue: 'available'
      },
      expiryDate: {
        type: Sequelize.DATE,
        allowNull: true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('EsimStocks', ['esimPlanId', 'status'], {
      name: 'idx_esimplan_status'
    });
    await queryInterface.addIndex('EsimStocks', ['iccid'], {
      name: 'idx_iccid'
    });
    await queryInterface.addIndex('EsimStocks', ['activationCode'], {
      name: 'idx_activation_code'
    });
    await queryInterface.addIndex('EsimStocks', ['orderId'], {
      name: 'idx_order_id'
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Remove indexes first
    await queryInterface.removeIndex('EsimStocks', 'idx_esimplan_status');
    await queryInterface.removeIndex('EsimStocks', 'idx_iccid');
    await queryInterface.removeIndex('EsimStocks', 'idx_activation_code');
    await queryInterface.removeIndex('EsimStocks', 'idx_order_id');

    // Drop the table
    await queryInterface.dropTable('EsimStocks');
  }
};
