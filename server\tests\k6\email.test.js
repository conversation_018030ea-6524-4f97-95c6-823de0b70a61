import http from 'k6/http';
import { sleep } from 'k6';
import { Counter } from 'k6/metrics';
import { CONFIG } from './config.js';
import { checkResponse } from './helpers.js';

// Custom metrics
const emailFailures = new Counter('email_failures');
const connectionErrors = new Counter('connection_errors');

export const options = {
    thresholds: {
        'http_req_duration': ['p(95)<2000'],    // 95% of requests should be below 2s
        'email_failures': ['rate<0.1'],         // Allow up to 10% email failures
        'connection_errors': ['rate<0.05'],      // Allow up to 5% connection errors
    },
    scenarios: {
        email_test: {
            executor: 'ramping-vus',
            startVUs: 0,
            stages: [
                { duration: '1m', target: 50 },   // Ramp up to 50 users
                { duration: '2m', target: 50 },   // Maintain 50 users
                { duration: '1m', target: 0 }     // Ramp down to 0
            ]
        }
    }
};

export function setup() {
    return { 
        baseUrl: CONFIG.baseUrl,
        headers: { 'Content-Type': 'application/json' }
    };
}

export default function (data) {
    const baseUrl = data.baseUrl;
    const headers = data.headers;

    group('Load Test - Low Balance Email Notification', () => {
        try {
            // Generate unique email and balance data
            const uniqueId = Math.floor(Math.random() * 1000000);
            const email = `user${uniqueId}@test.com`;
            const currentBalance = Math.random() * 100; // Random balance between 0 and 100
            const threshold = 50; // Fixed threshold for testing

            // Send low balance notification
            const response = http.post(`${baseUrl}/api/notifications/low-balance`, JSON.stringify({
                email: email,
                currentBalance: currentBalance,
                threshold: threshold
            }), {
                headers: headers
            });

            if (response.status !== 200) {
                console.error(`Email notification failed: ${response.status} ${response.body}`);
                emailFailures.add(1);
            }

        } catch (error) {
            console.error('Email notification error:', error);
            emailFailures.add(1);
            connectionErrors.add(1);
        }

        sleep(2); // Wait between requests
    });
}
