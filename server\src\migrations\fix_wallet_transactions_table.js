'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Rename the table to match the model's tableName
      await queryInterface.sequelize.query(`
        RENAME TABLE WalletTransactions TO wallettransactions
      `);
    } catch (error) {
      console.error('Migration failed:', error);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Revert the table name
      await queryInterface.sequelize.query(`
        RENAME TABLE wallettransactions TO WalletTransactions
      `);
    } catch (error) {
      console.error('Migration rollback failed:', error);
    }
  }
}; 