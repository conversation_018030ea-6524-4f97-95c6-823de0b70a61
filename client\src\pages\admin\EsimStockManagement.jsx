import React, { useState, useEffect, useCallback, memo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
    DialogPortal
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Edit, Trash2, Download, Upload, FileSpreadsheet } from 'lucide-react';
import api from '@/lib/axios';
import * as XLSX from 'xlsx';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import PropTypes from 'prop-types';
import { Textarea } from "@/components/ui/textarea";

const ITEMS_PER_PAGE = 10;

// eslint-disable-next-line no-unused-vars
const formatDateForInput = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return !isNaN(date.getTime()) ? date.toISOString().slice(0, 16) : '';
};

const StockForm = memo(({ stockForm, onInputChange, onSubmit, submitText }) => {
    return (
        <form onSubmit={onSubmit} className="space-y-4">
            <div className="grid gap-4">
                <div className="grid gap-2">
                    <Label htmlFor="lpaString">LPA String *</Label>
                    <Input
                        id="lpaString"
                        name="lpaString"
                        value={stockForm.lpaString}
                        onChange={onInputChange}
                        required
                        autoComplete="off"
                    />
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="iccid">ICC ID *</Label>
                    <Input
                        id="iccid"
                        name="iccid"
                        value={stockForm.iccid}
                        onChange={onInputChange}
                        required
                        autoComplete="off"
                    />
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="smdpAddress">SMDP Address *</Label>
                    <Input
                        id="smdpAddress"
                        name="smdpAddress"
                        value={stockForm.smdpAddress}
                        onChange={onInputChange}
                        required
                        autoComplete="off"
                    />
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="accessPointName">Access Point Name *</Label>
                    <Input
                        id="accessPointName"
                        name="accessPointName"
                        value={stockForm.accessPointName}
                        onChange={onInputChange}
                        required
                        autoComplete="off"
                    />
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="activationCode">Activation Code *</Label>
                    <Input
                        id="activationCode"
                        name="activationCode"
                        value={stockForm.activationCode}
                        onChange={onInputChange}
                        required
                        autoComplete="off"
                    />
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="phoneNumber">Phone Number</Label>
                    <Input
                        id="phoneNumber"
                        name="phoneNumber"
                        value={stockForm.phoneNumber}
                        onChange={onInputChange}
                        autoComplete="off"
                    />
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="confCode">Conf Code</Label>
                    <Input
                        id="confCode"
                        name="confCode"
                        value={stockForm.confCode}
                        onChange={onInputChange}
                        autoComplete="off"
                    />
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="pin">PIN</Label>
                    <Input
                        id="pin"
                        name="pin"
                        value={stockForm.pin}
                        onChange={onInputChange}
                        autoComplete="off"
                    />
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="walletAuthTransactionId">Wallet Auth Transaction ID</Label>
                    <Input
                        id="walletAuthTransactionId"
                        name="walletAuthTransactionId"
                        value={stockForm.walletAuthTransactionId}
                        onChange={onInputChange}
                        autoComplete="off"
                    />
                </div>
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700">Order ID</label>
                    <input
                        type="text"
                        name="orderId"
                        value={stockForm.orderId || ''}
                        onChange={onInputChange}
                        autoComplete="off"
                        disabled={true}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm bg-gray-100"
                    />
                </div>
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700">Order Date</label>
                    <input
                        type="datetime-local"
                        name="orderDate"
                        value={stockForm.orderDate ? new Date(stockForm.orderDate).toISOString().slice(0, 16) : ''}
                        onChange={onInputChange}
                        autoComplete="off"
                        disabled={true}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm bg-gray-100"
                    />
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="notes">Notes</Label>
                    <Input
                        id="notes"
                        name="notes"
                        value={stockForm.notes}
                        onChange={onInputChange}
                        autoComplete="off"
                    />
                </div>
            </div>
            <div className="flex justify-end space-x-4">
                <Button type="submit">{submitText}</Button>
            </div>
        </form>
    );
});

StockForm.propTypes = {
    stockForm: PropTypes.shape({
        iccid: PropTypes.string,
        activationCode: PropTypes.string,
        smdpAddress: PropTypes.string,
        accessPointName: PropTypes.string,
        lpaString: PropTypes.string,
        phoneNumber: PropTypes.string,
        confCode: PropTypes.string,
        pin: PropTypes.string,
        notes: PropTypes.string,
        status: PropTypes.string,
        orderDate: PropTypes.string,
        walletAuthTransactionId: PropTypes.string,
        orderId: PropTypes.string
    }).isRequired,
    onInputChange: PropTypes.func.isRequired,
    onSubmit: PropTypes.func.isRequired,
    submitText: PropTypes.string.isRequired
};

StockForm.displayName = 'StockForm';

const EsimStockManagement = () => {
    const { id: planId } = useParams();
    const navigate = useNavigate();
    const { toast } = useToast();
    const [plan, setPlan] = useState(null);
    const [stocks, setStocks] = useState([]);
    const [filteredStocks, setFilteredStocks] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [selectedStock, setSelectedStock] = useState(null);
    const [loading, setLoading] = useState(true);
    const [stockForm, setStockForm] = useState({
        lpaString: '',
        iccid: '',
        smdpAddress: '',
        accessPointName: '',
        activationCode: '',
        phoneNumber: '',
        confCode: '',
        pin: '',
        walletAuthTransactionId: '',
        orderId: null,
        orderDate: null,
        notes: ''
    });

    const handleInputChange = useCallback((e) => {
        e.preventDefault();
        const { name, value } = e.target;
        
        // Special handling for orderDate
        if (name === 'orderDate') {
            // If the value is empty, set it to null
            if (!value) {
                setStockForm(prev => ({
                    ...prev,
                    orderDate: null
                }));
                return;
            }
            
            // Convert the datetime-local value to MySQL format
            const date = new Date(value);
            if (!isNaN(date.getTime())) {
                setStockForm(prev => ({
                    ...prev,
                    orderDate: date.toISOString().slice(0, 19).replace('T', ' ')
                }));
            }
            return;
        }

        setStockForm(prev => ({
            ...prev,
            [name]: value
        }));
    }, []);

    const resetStockForm = () => {
        setStockForm({
            lpaString: '',
            iccid: '',
            smdpAddress: '',
            accessPointName: '',
            activationCode: '',
            phoneNumber: '',
            confCode: '',
            pin: '',
            walletAuthTransactionId: '',
            orderId: null,
            orderDate: null,
            notes: ''
        });
    };

    const handleAddDialogOpen = (open) => {
        if (open) {
            resetStockForm();
        }
        setIsAddDialogOpen(open);
    };

    useEffect(() => {
        fetchPlanAndStocks();
    }, [planId]);

    useEffect(() => {
        filterStocks();
    }, [stocks, searchTerm, statusFilter]);

    const fetchPlanAndStocks = async () => {
        try {
            const [planRes, stocksRes] = await Promise.all([
                api.get(`/api/esim-plans/${planId}`),
                api.get(`/api/esim-plans/${planId}/stock`)
            ]);
            setPlan(planRes.data);
            setStocks(stocksRes.data);
        } catch (error) {
            console.error('Error fetching data:', error);
            toast({
                title: 'Error',
                description: 'Failed to fetch plan and stock data',
                variant: 'destructive'
            });
        } finally {
            setLoading(false);
        }
    };

    const filterStocks = () => {
        let filtered = [...stocks];

        // Apply search filter
        if (searchTerm) {
            const search = searchTerm.toLowerCase();
            filtered = filtered.filter(stock =>
                stock.iccid.toLowerCase().includes(search) ||
                stock.activationCode.toLowerCase().includes(search) ||
                stock.orderId?.toString().toLowerCase().includes(search) ||
                stock.phoneNumber?.toLowerCase().includes(search)
            );
        }

        // Apply status filter
        if (statusFilter !== 'all') {
            filtered = filtered.filter(stock => stock.status === statusFilter);
        }

        setFilteredStocks(filtered);
        setCurrentPage(1); // Reset to first page when filtering
    };

    const handleDownloadTemplate = () => {
        const template = [
            [
                'LPA String*',
                'ICC ID*',
                'SMDP Address*',
                'Access Point Name*',
                'Activation Code*',
                'Phone Number',
                'Conf Code',
                'PIN',
                'Notes'
            ],
        ];

        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet(template);

        // Set column widths
        const colWidths = [
            { wch: 40 }, // LPA String
            { wch: 15 }, // ICC ID
            { wch: 20 }, // SMDP Address
            { wch: 20 }, // Access Point Name
            { wch: 15 }, // Activation Code
            { wch: 15 }, // Phone Number
            { wch: 10 }, // Conf Code
            { wch: 10 }, // PIN
            { wch: 25 }, // Wallet Auth Transaction ID
            { wch: 15 }, // Order ID
            { wch: 15 }, // Order Date
            { wch: 30 }, // Notes
        ];
        ws['!cols'] = colWidths;

        XLSX.utils.book_append_sheet(wb, ws, 'Template');
        XLSX.writeFile(wb, 'esim_stock_template.xlsx');
    };

    const handleFileUpload = async (event) => {
        const file = event.target.files[0];
        if (!file) return;

        try {
            const reader = new FileReader();
            reader.onload = async (e) => {
                try {
                    const workbook = XLSX.read(e.target.result, { type: 'array' });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                    // Remove header row and empty rows
                    const dataRows = jsonData.slice(1).filter(row => row.length > 0);

                    // Process and validate data
                    const stockData = dataRows.map(row => {
                        // Parse the date and validate it
                        let orderDate = null;
                        if (row[10]) {
                            const parsedDate = new Date(row[10]);
                            if (!isNaN(parsedDate.getTime())) {
                                orderDate = parsedDate.toISOString().slice(0, 19).replace('T', ' ');
                            }
                        }

                        return {
                            lpaString: row[0],
                            iccid: row[1],
                            smdpAddress: row[2],
                            accessPointName: row[3],
                            activationCode: row[4],
                            phoneNumber: row[5] || null,
                            confCode: row[6] || null,
                            pin: row[7] || null,
                            walletAuthTransactionId: row[8] || null,
                            orderId: row[9] || null,
                            orderDate,
                            notes: row[11] || null
                        };
                    });

                    // Validate required fields
                    const invalidItems = stockData.filter(item =>
                        !item.lpaString || !item.iccid || !item.smdpAddress ||
                        !item.accessPointName || !item.activationCode
                    );

                    if (invalidItems.length > 0) {
                        toast({
                            title: 'Validation Error',
                            description: `${invalidItems.length} items are missing required fields`,
                            variant: 'destructive'
                        });
                        return;
                    }

                    // Upload data
                    const response = await api.post(`/api/esim-plans/${planId}/stock/bulk`, stockData);
                    
                    if (response.data && response.data.stocks) {
                        toast({
                            title: 'Success',
                            description: `Successfully uploaded ${response.data.stocks.length} stock items`
                        });
                        fetchPlanAndStocks();
                    } else {
                        throw new Error('Invalid response from server');
                    }
                } catch (error) {
                    // console.error('Error processing file:', error);
                    toast({
                        title: 'Error',
                        description: error.response?.data?.message || 'Failed to process Excel file. Please make sure it matches the template format.',
                        variant: 'destructive'
                    });
                }
            };
            reader.readAsArrayBuffer(file);
        } catch (error) {
            // console.error('Error reading file:', error);
            toast({
                title: 'Error',
                description: error.response?.data?.message || 'Failed to upload stock data',
                variant: 'destructive'
            });
        }
        // Reset file input
        event.target.value = '';
    };

    const handleAddStock = async (e) => {
        e.preventDefault();
        try {
            await api.post(`/api/esim-plans/${planId}/stock`, stockForm);
            toast({
                title: 'Success',
                description: 'Stock added successfully'
            });
            setIsAddDialogOpen(false);
            setStockForm({
                lpaString: '',
                iccid: '',
                smdpAddress: '',
                accessPointName: '',
                activationCode: '',
                phoneNumber: '',
                confCode: '',
                pin: '',
                walletAuthTransactionId: '',
                orderId: '',
                orderDate: null,
                notes: ''
            });
            fetchPlanAndStocks();
        } catch (error) {
            console.error('Error adding stock:', error);
            toast({
                title: 'Error',
                description: error.response?.data?.message || 'Failed to add stock',
                variant: 'destructive'
            });
        }
    };

    const handleEditClick = (stock) => {
        setSelectedStock(stock);
        setStockForm(stock);
        setIsEditDialogOpen(true);
    };

    const handleEditStock = async (e) => {
        e.preventDefault();
        try {
            await api.put(`/api/esim-plans/${planId}/stock/${selectedStock.id}`, stockForm);
            toast({
                title: 'Success',
                description: 'Stock updated successfully'
            });
            setIsEditDialogOpen(false);
            fetchPlanAndStocks();
        } catch (error) {
            console.error('Error updating stock:', error);
            toast({
                title: 'Error',
                description: error.response?.data?.message || 'Failed to update stock',
                variant: 'destructive'
            });
        }
    };

    const handleDeleteClick = (stock) => {
        setSelectedStock(stock);
        setIsDeleteDialogOpen(true);
    };

    const handleDeleteStock = async () => {
        try {
            await api.delete(`/api/esim-plans/${planId}/stock/${selectedStock.id}`);
            toast({
                title: 'Success',
                description: 'Stock deleted successfully'
            });
            setIsDeleteDialogOpen(false);
            fetchPlanAndStocks();
        } catch (error) {
            console.error('Error deleting stock:', error);
            toast({
                title: 'Error',
                description: error.response?.data?.message || 'Failed to delete stock',
                variant: 'destructive'
            });
        }
    };

    const paginatedStocks = filteredStocks.slice(
        (currentPage - 1) * ITEMS_PER_PAGE,
        currentPage * ITEMS_PER_PAGE
    );

    // Calculate total pages once
    const totalPages = Math.ceil(filteredStocks.length / ITEMS_PER_PAGE);

    if (loading) {
        return <div>Loading...</div>;
    }

    return (
        <div className="h-full flex flex-col gap-6 p-6 ">
            <div>
                    <h1 className="text-2xl font-bold tracking-tight">Stock Management</h1>
                    <p className="text-muted-foreground">Manage all the eSIM stocks here</p>
            </div>
            <Card>
                <CardHeader className="border-b bg-gradient-to-r from-blue-800 to-blue-600 py-6 mb-2 rounded-t-lg">
                    <div className="flex justify-between items-center">
                        <div>
                            <CardTitle className="text-white font-bold  ">
                            {plan?.name}
                            </CardTitle>
                        </div>
                        <div className="flex items-center space-x-4">
                            <Button onClick={() => navigate(`/admin/esim-plans`)} className="bg-purple-500 text-white">
                                Back to Plan
                            </Button>
                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="outline"
                                    onClick={handleDownloadTemplate}
                                    className="flex items-center gap-2 border-purple-500 bg-purple-500 text-white"
                                >
                                    <Download className="h-4 w-4" />
                                    Template
                                </Button>
                                <div className="relative">
                                    <input
                                        type="file"
                                        accept=".xlsx,.xls,.csv"
                                        onChange={handleFileUpload}
                                        className="hidden"
                                        id="file-upload"
                                    />
                                    <Button
                                        variant="outline"
                                        onClick={() => document.getElementById('file-upload').click()}
                                        className="flex items-center gap-2 border-purple-500 bg-purple-500 text-white"
                                    >
                                        <Upload className="h-4 w-4" />
                                        Upload Excel
                                    </Button>
                                </div>
                            </div>

                            <Dialog 
                                open={isAddDialogOpen} 
                                onOpenChange={handleAddDialogOpen}
                                modal={true}
                            >
                                <DialogTrigger asChild>
                                    <Button className="bg-purple-500 text-white">Add Stock</Button>
                                </DialogTrigger>
                                <DialogPortal>
                                    <DialogContent 
                                        className="max-w-md max-h-[90vh] overflow-y-auto" 
                                        onPointerDownOutside={(e) => e.preventDefault()}
                                        onInteractOutside={(e) => e.preventDefault()}
                                        onEscapeKeyDown={(e) => e.preventDefault()}
                                        onOpenAutoFocus={(e) => e.preventDefault()}
                                    >
                                        <DialogHeader>
                                            <DialogTitle>Add New Stock</DialogTitle>
                                            <DialogDescription>
                                                Add new eSIM stock details
                                            </DialogDescription>
                                        </DialogHeader>
                                        <div onClick={(e) => e.stopPropagation()}>
                                            <StockForm 
                                                stockForm={stockForm}
                                                onInputChange={handleInputChange}
                                                onSubmit={handleAddStock}
                                                submitText="Add Stock"
                                            />
                                        </div>
                                    </DialogContent>
                                </DialogPortal>
                            </Dialog>

                            <Dialog 
                                open={isEditDialogOpen} 
                                onOpenChange={setIsEditDialogOpen}
                                modal={true}
                            >
                                <DialogPortal>
                                    <DialogContent 
                                        className="max-w-md max-h-[90vh] overflow-y-auto"
                                        onPointerDownOutside={(e) => e.preventDefault()}
                                        onInteractOutside={(e) => e.preventDefault()}
                                        onEscapeKeyDown={(e) => e.preventDefault()}
                                        onOpenAutoFocus={(e) => e.preventDefault()}
                                    >
                                        <DialogHeader>
                                            <DialogTitle>Edit Stock</DialogTitle>
                                            <DialogDescription>
                                                Update eSIM stock details
                                            </DialogDescription>
                                        </DialogHeader>
                                        <div onClick={(e) => e.stopPropagation()}>
                                            <StockForm 
                                                stockForm={stockForm}
                                                onInputChange={handleInputChange}
                                                onSubmit={handleEditStock}
                                                submitText="Update Stock"
                                            />
                                        </div>
                                    </DialogContent>
                                </DialogPortal>
                            </Dialog>
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-2">
                                <Label htmlFor="search">Search:</Label>
                                <Input
                                    id="search"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    placeholder="Search ICCID, Activation Code..."
                                    className="w-[300px]"
                                />
                            </div>
                            <div className="flex items-center space-x-2">
                                <Label htmlFor="status">Status:</Label>
                                <Select value={statusFilter} onValueChange={setStatusFilter}>
                                    <SelectTrigger className="w-[180px]">
                                        <SelectValue placeholder="Select status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Status</SelectItem>
                                        <SelectItem value="available">Available</SelectItem>
                                        <SelectItem value="assigned">Assigned</SelectItem>
                                        <SelectItem value="activated">Activated</SelectItem>
                                        <SelectItem value="expired">Expired</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-500">
                                Showing {Math.min(filteredStocks.length, ITEMS_PER_PAGE)} of {filteredStocks.length} entries
                            </span>
                        </div>
                    </div>
                    <div className="rounded-md border overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100">
                                    <TableHead className="min-w-[350px]">LPA String</TableHead>
                                    <TableHead>ICC ID</TableHead>
                                    <TableHead>SMDP Address</TableHead>
                                    <TableHead>Access Point Name</TableHead>
                                    <TableHead>Activation Code</TableHead>
                                    <TableHead>Phone Number</TableHead>
                                    <TableHead>Conf Code</TableHead>
                                    <TableHead>Pin</TableHead>
                                    <TableHead>Wallet Auth ID</TableHead>
                                    <TableHead>Order ID</TableHead>
                                    <TableHead>Order Date</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {paginatedStocks.map((stock) => (
                                    <TableRow key={stock.id}>
                                        <TableCell className="max-w-[200px] truncate" title={stock.lpaString}>
                                            {stock.lpaString}
                                        </TableCell>
                                        <TableCell>{stock.iccid}</TableCell>
                                        <TableCell>{stock.smdpAddress}</TableCell>
                                        <TableCell>{stock.accessPointName}</TableCell>
                                        <TableCell>{stock.activationCode}</TableCell>
                                        <TableCell>{stock.phoneNumber || '-'}</TableCell>
                                        <TableCell>{stock.confCode || '-'}</TableCell>
                                        <TableCell>{stock.pin || '-'}</TableCell>
                                        <TableCell>{stock.walletAuthTransactionId || '-'}</TableCell>
                                        <TableCell>{stock.orderId || '-'}</TableCell>
                                        <TableCell>
                                            {stock.orderDate
                                                ? new Date(stock.orderDate).toLocaleDateString()
                                                : '-'}
                                        </TableCell>
                                        <TableCell>{stock.status}</TableCell>
                                        <TableCell>
                                            <div className="flex space-x-2">
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => handleEditClick(stock)}
                                                    disabled={stock.status === 'assigned'}
                                                    className={stock.status === 'assigned' ? 'cursor-not-allowed opacity-50' : ''}
                                                    title={stock.status === 'assigned' ? 'Cannot edit assigned stock' : 'Edit stock'}
                                                >
                                                    <Edit className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => handleDeleteClick(stock)}
                                                    disabled={stock.status === 'assigned'}
                                                    className={stock.status === 'assigned' ? 'cursor-not-allowed opacity-50' : ''}
                                                    title={stock.status === 'assigned' ? 'Cannot delete assigned stock' : 'Delete stock'}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                    {totalPages > 1 && (
                        <div className="flex items-center justify-between mt-4">
                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="outline"
                                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                    disabled={currentPage === 1}
                                >
                                    Previous
                                </Button>
                                <span className="text-sm">
                                    Page {currentPage} of {totalPages}
                                </span>
                                <Button
                                    variant="outline"
                                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                                    disabled={currentPage === totalPages}
                                >
                                    Next
                                </Button>
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the stock item.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDeleteStock}>Delete</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
};

export default EsimStockManagement;
