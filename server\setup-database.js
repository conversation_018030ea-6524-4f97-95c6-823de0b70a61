// require('dotenv').config();
// const mysql = require('mysql2/promise');
// const initDb = require('./src/config/initDb');

// async function createDatabase() {
//     let connection;
//     try {
//         connection = await mysql.createConnection({
//             host: process.env.DB_HOST,
//             user: process.env.DB_USER,
//             password: process.env.DB_PASSWORD
//         });

//         await connection.query(`CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
//         await connection.query(`USE ${process.env.DB_NAME}`);

//         // Create Users table
//         await connection.query(`
//             CREATE TABLE IF NOT EXISTS Users (
//                 id CHAR(36) PRIMARY KEY,
//                 email VARCHAR(255) NOT NULL UNIQUE,
//                 password VARCHAR(255) NOT NULL,
//                 role ENUM('admin', 'user') NOT NULL DEFAULT 'user',
//                 firstName VARCHAR(100) NOT NULL,
//                 lastName VARCHAR(100) NOT NULL,
//                 company VARCHAR(200),
//                 isActive BOOLEAN DEFAULT true,
//                 lastLogin DATETIME,
//                 loginAttempts TINYINT UNSIGNED DEFAULT 0,
//                 lockUntil DATETIME,
//                 createdAt DATETIME NOT NULL,
//                 updatedAt DATETIME NOT NULL,
//                 INDEX idx_role_active (role, isActive),
//                 INDEX idx_login_attempts (loginAttempts, lockUntil)
//             )
//         `);

//         // Create OTPs table
//         await connection.query(`
//             CREATE TABLE IF NOT EXISTS OTPs (
//                 id CHAR(36) PRIMARY KEY,
//                 userId CHAR(36) NOT NULL,
//                 code CHAR(6) NOT NULL,
//                 expiresAt DATETIME NOT NULL,
//                 isUsed BOOLEAN DEFAULT false,
//                 createdAt DATETIME NOT NULL,
//                 updatedAt DATETIME NOT NULL,
//                 INDEX idx_user_expires (userId, expiresAt, isUsed),
//                 INDEX idx_cleanup (expiresAt, isUsed),
//                 FOREIGN KEY fk_user_id (userId) 
//                     REFERENCES Users(id) 
//                     ON DELETE CASCADE
//             )
//         `);

//         await connection.end();
//         await initDb();
        
//         console.log('\nDatabase setup completed successfully');
//         console.log('Admin credentials: <EMAIL> / admin123');
        
//         process.exit(0);
//     } catch (error) {
//         console.error('Database setup failed:', error.message);
//         if (connection) await connection.end();
//         process.exit(1);
//     }
// }

// createDatabase();
