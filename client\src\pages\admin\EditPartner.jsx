import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import api from '@/lib/axios';
import { useToast } from "@/components/ui/use-toast";
import { ArrowLeft } from 'lucide-react';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
    SelectGroup
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const formSchema = z.object({
    firstName: z.string().min(2, 'First name is required'),
    lastName: z.string().min(2, 'Last name is required'),
    email: z.string().email('Invalid email'),
    alternateEmail: z.string().email('Invalid alternate email').optional().or(z.literal('')),
    countryId: z.string().min(2, 'Country is required'),
    phoneNumber: z.string().min(10, 'Phone number is required'),
    alternatePhoneNumber: z.string().optional().or(z.literal('')),
    businessName: z.string().min(2, 'Business name is required'),
    businessEmail: z.string().email('Invalid business email'),
    billingAddressLine1: z.string().min(1, 'Billing address line 1 is required'),
    billingAddressLine2: z.string().optional().or(z.literal('')),
    billingCity: z.string().optional().or(z.literal('')),
    billingProvince: z.string().optional().or(z.literal('')),
    billingCountryId: z.string().optional().or(z.literal('')),
    billingPostalCode: z.string().optional().or(z.literal('')),
    markupPercentage: z.string()
        .min(1, 'Markup percentage is required')
        .transform((val) => Number(val))
        .refine((val) => !isNaN(val), {
            message: 'Must be a valid number'
        })
        .refine((val) => val >= 0 && val <= 100, {
            message: 'Must be between 0 and 100'
        })
});

export default function EditPartner() {
    const [countries, setCountries] = useState([]);
    const [loading, setLoading] = useState(true);
    const { toast } = useToast();
    const navigate = useNavigate();
    const { id } = useParams();

    const form = useForm({
        resolver: zodResolver(formSchema),
        defaultValues: {
            firstName: '',
            lastName: '',
            email: '',
            alternateEmail: '',
            countryId: '',
            phoneNumber: '',
            alternatePhoneNumber: '',
            businessName: '',
            businessEmail: '',
            billingAddressLine1: '',
            billingAddressLine2: '',
            billingCity: '',
            billingProvince: '',
            billingCountryId: '',
            billingPostalCode: '',
            markupPercentage: ''
        }
    });

    useEffect(() => {
        fetchPartner();
        fetchCountries();
    }, []);

    const fetchPartner = async () => {
        try {
            const response = await api.get(`/api/partners/${id}`);
            const partner = response.data;
            
            // Set form values
            form.reset({
                firstName: partner.firstName || '',
                lastName: partner.lastName || '',
                email: partner.email || '',
                alternateEmail: partner.alternateEmail || '',
                countryId: partner.countryId || '',
                phoneNumber: partner.phoneNumber || '',
                alternatePhoneNumber: partner.alternatePhoneNumber || '',
                businessName: partner.businessName || '',
                businessEmail: partner.businessEmail || '',
                billingAddressLine1: partner.billingAddressLine1 || '',
                billingAddressLine2: partner.billingAddressLine2 || '',
                billingCity: partner.billingCity || '',
                billingProvince: partner.billingProvince || '',
                billingCountryId: partner.billingCountryId || '',
                billingPostalCode: partner.billingPostalCode || '',
                markupPercentage: partner.markupPercentage !== null ? String(partner.markupPercentage) : ''
            });
        } catch (error) {
            // console.error('Error fetching partner:', error);
            toast({
                title: "Error",
                description: "Failed to fetch partner details",
                variant: "destructive"
            });
            navigate('/admin/partners');
        } finally {
            setLoading(false);
        }
    };

    const fetchCountries = async () => {
        try {
            const response = await api.get('/api/countries');
            setCountries(Array.isArray(response.data) ? response.data : []);
        } catch (error) {
            // console.error('Error fetching countries:', error);
            toast({
                title: "Error",
                description: "Failed to fetch countries",
                variant: "destructive"
            });
        }
    };

    const onSubmit = async (data) => {
        setLoading(true);
        try {
            // Convert markupPercentage to number if it exists
            const formData = {
                ...data,
                markupPercentage: Number(data.markupPercentage)
            };
            
            await api.put(`/api/partners/${id}`, formData);
            toast({
                title: "Success",
                description: "Partner updated successfully"
            });
            navigate('/admin/partners');
        } catch (error) {
            toast({
                title: "Error",
                description: error.response?.data?.message || "Failed to update partner",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <div className="h-full flex items-center justify-center">
                <p>Loading...</p>
            </div>
        );
    }

    return (
        <div className="h-full flex flex-col gap-6 p-6">
            <div className="flex items-center gap-4">
                <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => navigate('/admin/partners')}
                >
                    <ArrowLeft className="h-4 w-4" />
                </Button>
            <div>
                <h1 className="text-2xl font-bold">Edit Partner</h1>
                <p className="text-gray-600 mt-1">Update partner information</p>
            </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Account Information */}
                            <div className="space-y-4">
                                <h2 className="text-lg font-semibold">Account Information</h2>

                                <FormField
                                    control={form.control}
                                    name="email"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Email *</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} type="email" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                                
                                
                                <FormField
                                    control={form.control}
                                    name="firstName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>First Name *</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>   
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="lastName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Last Name *</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />


                                <FormField
                                    control={form.control}
                                    name="alternateEmail"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Alternate Email (Optional)</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} type="email" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="countryId"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Country *</FormLabel>
                                            <Select 
                                                value={field.value} 
                                                onValueChange={field.onChange} 
                                                required
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="w-full" id={`${field.name}-select`}>
                                                        <SelectValue placeholder="Select country" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent className="max-h-[200px] overflow-y-auto">
                                                    <SelectGroup>
                                                        {countries.map((country) => (
                                                            <SelectItem 
                                                                key={country.id} 
                                                                value={country.id}
                                                                className="cursor-pointer"
                                                            >
                                                                {country.name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectGroup>
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="phoneNumber"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Phone Number *</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="alternatePhoneNumber"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Alternate Phone Number (Optional)</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="markupPercentage"
                                    render={({ field: { value, onChange, ...field } }) => (
                                        <FormItem>
                                            <FormLabel>Markup % *</FormLabel>
                                            <FormControl>
                                                <Input 
                                                    id={`${field.name}-input`} 
                                                    type="number" 
                                                    min="0" 
                                                    max="100" 
                                                    step="0.01"
                                                    value={value || ''}
                                                    onChange={(e) => onChange(e.target.value === '' ? '' : e.target.value)}
                                                    {...field} 
                                                    placeholder="Enter markup percentage (0-100)"
                                                />
                                            </FormControl>
                                            <FormMessage />
                                            <span className="block text-xs text-gray-500">
                                                Applied when no specific selling price is set
                                            </span>
                                        </FormItem>
                                    )}
                                />
                            </div>

                            {/* Business Information */}
                            <div className="space-y-4">
                                <h2 className="text-lg font-semibold">Business Information</h2>
                                
                                <FormField
                                    control={form.control}
                                    name="businessName"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Business Name *</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="businessEmail"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Business Email *</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} type="email" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="billingAddressLine1"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Billing Address Line 1 *</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="billingAddressLine2"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Billing Address Line 2 (Optional)</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="billingCity"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>City</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="billingProvince"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Province/State</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="billingCountryId"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Billing Country</FormLabel>
                                            <Select 
                                                value={field.value} 
                                                onValueChange={field.onChange} 
                                                required
                                            >
                                                <FormControl>
                                                    <SelectTrigger className="w-full" id={`${field.name}-select`}>
                                                        <SelectValue placeholder="Select country" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent className="max-h-[200px] overflow-y-auto">
                                                    <SelectGroup>
                                                        {countries.map((country) => (
                                                            <SelectItem 
                                                                key={country.id} 
                                                                value={country.id}
                                                                className="cursor-pointer"
                                                            >
                                                                {country.name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectGroup>
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="billingPostalCode"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Postal Code</FormLabel>
                                            <FormControl>
                                                <Input id={`${field.name}-input`} {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </div>

                        <div className="flex justify-end gap-4">
                            <Button 
                                type="button" 
                                variant="outline" 
                                onClick={() => navigate('/admin/partners')}
                            >
                                Cancel
                            </Button>
                            <Button type="submit" disabled={loading}>
                                {loading ? 'Updating...' : 'Update Partner'}
                            </Button>
                        </div>
                    </form>
                </Form>
            </div>
        </div>
    );
}
