const User = require('../models/User');
const Country = require('../models/Country');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
const { Op } = require('sequelize');

// Get all admin staff
exports.getAllStaff = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const search = req.query.search || '';

        const whereClause = {
            role: 'admin',
            [Op.or]: [
                { firstName: { [Op.like]: `%${search}%` } },
                { lastName: { [Op.like]: `%${search}%` } },
                { email: { [Op.like]: `%${search}%` } }
            ]
        };

        const { count, rows: staff } = await User.findAndCountAll({
            where: whereClause,
            attributes: [
                'id',
                'firstName',
                'lastName',
                'email',
                'phoneNumber',
                'countryId',
                'isActive',
                'lastLogin',
                'createdAt'
            ],
            include: [{
                model: Country,
                as: 'country',
                attributes: ['name'],
                where: search ? {
                    name: { [Op.like]: `%${search}%` }
                } : undefined,
                required: false
            }],
            limit,
            offset,
            order: [['createdAt', 'DESC']]
        });

        res.json({
            staff,
            totalPages: Math.ceil(count / limit),
            currentPage: page,
            totalItems: count
        });
    } catch (error) {
        console.error('Error fetching staff:', error);
        res.status(500).json({ message: 'Error fetching staff' });
    }
};

// Get staff by ID
exports.getStaffById = async (req, res) => {
    try {
        const staff = await User.findOne({
            where: { 
                id: req.params.id,
                role: 'admin'
            },
            attributes: [
                'id',
                'firstName',
                'lastName',
                'email',
                'phoneNumber',
                'countryId',
                'isActive',
                'lastLogin',
                'createdAt'
            ],
            include: [{
                model: Country,
                as: 'country',
                attributes: ['name']
            }]
        });

        if (!staff) {
            return res.status(404).json({ message: 'Staff not found' });
        }

        res.json(staff);
    } catch (error) {
        console.error('Error fetching staff:', error);
        res.status(500).json({ message: 'Error fetching staff' });
    }
};

// Create new staff
exports.createStaff = async (req, res) => {
    try {
        const {
            email,
            password,
            firstName,
            lastName,
            phoneNumber,
            countryId
        } = req.body;

        // Check if email exists
        const existingEmail = await User.findOne({ where: { email } });
        if (existingEmail) {
            return res.status(400).json({ message: 'Email already registered' });
        }

        // Hash password
        const hashedPassword = await bcrypt.hash(password, 10);

         // Ensure phoneNumber is either a valid string or null
         const sanitizedPhoneNumber = phoneNumber && phoneNumber.trim() !== '' ? phoneNumber.trim() : null;

        const staff = await User.create({
            id: uuidv4(),
            email,
            password: hashedPassword,
            firstName,
            lastName,
            phoneNumber: sanitizedPhoneNumber,
            countryId,
            role: 'admin',
            isActive: true
        });

        res.status(201).json({
            message: 'Staff created successfully',
            staff: {
                id: staff.id,
                email: staff.email,
                firstName: staff.firstName,
                lastName: staff.lastName
            }
        });
    } catch (error) {
        console.error('Error creating staff:', error);
        res.status(500).json({ message: 'Error creating staff' });
    }
};

// Update staff
exports.updateStaff = async (req, res) => {
    try {
        const staff = await User.findOne({
            where: { 
                id: req.params.id,
                role: 'admin'
            }
        });

        if (!staff) {
            return res.status(404).json({ message: 'Staff not found' });
        }

        // Check email uniqueness if being updated
        if (req.body.email && req.body.email !== staff.email) {
            const existingEmail = await User.findOne({ where: { email: req.body.email } });
            if (existingEmail) {
                return res.status(400).json({ message: 'Email already registered' });
            }
        }

        // Hash password if it's being updated
        if (req.body.password) {
            req.body.password = await bcrypt.hash(req.body.password, 10);
        }

        await staff.update(req.body);

        res.json({ message: 'Staff updated successfully' });
    } catch (error) {
        console.error('Error updating staff:', error);
        res.status(500).json({ message: 'Error updating staff' });
    }
};

// Delete staff
exports.deleteStaff = async (req, res) => {
    try {
        const staff = await User.findOne({
            where: { 
                id: req.params.id,
                role: 'admin'
            }
        });

        if (!staff) {
            return res.status(404).json({ message: 'Staff not found' });
        }

        await staff.destroy();

        res.json({ message: 'Staff deleted successfully' });
    } catch (error) {
        console.error('Error deleting staff:', error);
        res.status(500).json({ message: 'Error deleting staff' });
    }
};

// Toggle staff status
exports.toggleStaffStatus = async (req, res) => {
    try {
        const staff = await User.findOne({
            where: { 
                id: req.params.id,
                role: 'admin'
            }
        });

        if (!staff) {
            return res.status(404).json({ message: 'Staff not found' });
        }

        await staff.update({ isActive: !staff.isActive });

        res.json({ 
            message: `Staff ${staff.isActive ? 'activated' : 'deactivated'} successfully`,
            isActive: staff.isActive
        });
    } catch (error) {
        console.error('Error toggling staff status:', error);
        res.status(500).json({ message: 'Error toggling staff status' });
    }
};
