describe('Registration Flow', () => {
  beforeEach(() => {
    cy.visit('/register');
  });

  it('should display registration form', () => {
    cy.get('form').within(() => {
      cy.get('input[name="email"]').should('exist');
      cy.get('input[name="password"]').should('exist');
      cy.get('input[name="confirmPassword"]').should('exist');
      cy.get('input[name="firstName"]').should('exist');
      cy.get('input[name="lastName"]').should('exist');
      cy.get('input[name="companyName"]').should('exist');
      cy.get('input[name="phone"]').should('exist');
      cy.get('button[type="submit"]').should('exist');
    });
  });

  it('should validate required fields', () => {
    cy.get('button[type="submit"]').click();
    cy.contains('This field is required').should('be.visible');
  });

  it('should validate email format', () => {
    cy.get('input[name="email"]').type('invalid-email');
    cy.get('button[type="submit"]').click();
    cy.contains('Please enter a valid email').should('be.visible');
  });

  it('should validate password requirements', () => {
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('12345');
    cy.get('button[type="submit"]').click();
    cy.contains('Password must be at least 6 characters').should('be.visible');
  });

  it('should validate password match', () => {
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('password123');
    cy.get('input[name="confirmPassword"]').type('password456');
    cy.get('button[type="submit"]').click();
    cy.contains('Passwords must match').should('be.visible');
  });

  it('should validate phone number format', () => {
    cy.get('input[name="phone"]').type('invalid');
    cy.get('button[type="submit"]').click();
    cy.contains('Invalid phone number').should('be.visible');
  });

  it('should handle successful registration', () => {
    cy.intercept('POST', '/api/auth/register', {
      statusCode: 201,
      body: { message: 'Registration successful' }
    }).as('register');

    const userData = {
      email: '<EMAIL>',
      password: 'password123',
      confirmPassword: 'password123',
      firstName: 'John',
      lastName: 'Doe',
      companyName: 'Test Company',
      phone: '**********'
    };

    Object.entries(userData).forEach(([field, value]) => {
      cy.get(`input[name="${field}"]`).type(value);
    });

    cy.get('button[type="submit"]').click();
    cy.wait('@register');

    cy.contains('Registration successful').should('be.visible');
    cy.contains('Please check your email for verification').should('be.visible');
    cy.url().should('include', '/login');
  });

  it('should handle existing email error', () => {
    cy.intercept('POST', '/api/auth/register', {
      statusCode: 400,
      body: { message: 'Email already registered' }
    }).as('registerError');

    const userData = {
      email: '<EMAIL>',
      password: 'password123',
      confirmPassword: 'password123',
      firstName: 'John',
      lastName: 'Doe',
      companyName: 'Test Company',
      phone: '**********'
    };

    Object.entries(userData).forEach(([field, value]) => {
      cy.get(`input[name="${field}"]`).type(value);
    });

    cy.get('button[type="submit"]').click();
    cy.wait('@registerError');
    cy.contains('Email already registered').should('be.visible');
  });

  it('should navigate to login page', () => {
    cy.get('a').contains('Already have an account?').click();
    cy.url().should('include', '/login');
  });

  it('should preserve form data on validation errors', () => {
    const userData = {
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      companyName: 'Test Company',
      phone: '**********'
    };

    Object.entries(userData).forEach(([field, value]) => {
      cy.get(`input[name="${field}"]`).type(value);
    });

    // Type mismatched passwords
    cy.get('input[name="password"]').type('password123');
    cy.get('input[name="confirmPassword"]').type('password456');
    cy.get('button[type="submit"]').click();

    // Verify form data is preserved
    Object.entries(userData).forEach(([field, value]) => {
      cy.get(`input[name="${field}"]`).should('have.value', value);
    });
  });
});
