const { DataTypes, Sequelize } = require('sequelize');
const bcrypt = require('bcrypt');
const sequelize = require('../config/database');

const User = sequelize.define('User', {
    id: {
        type: DataTypes.CHAR(36),
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    email: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
            isEmail: true
        },
        unique: {
            name: 'unique_email',
            msg: 'This email is already registered'
        }
    },
    alternateEmail: {
        type: DataTypes.STRING,
        allowNull: true,
        validate: {
            isEmailOrEmpty(value) {
                if (value && value.length > 0 && !(/^[^\s@]+@[^\s@]+\.[^\s@]+$/).test(value)) {
                    throw new Error('Invalid alternate email format');
                }
            }
        }
    },
    password: {
        type: DataTypes.STRING,
        allowNull: false
    },
    role: {
        type: DataTypes.ENUM('admin', 'partner'),
        allowNull: false,
        defaultValue: 'partner'
    },
    resetToken: {
        type: DataTypes.STRING,
        allowNull: true
    },
    resetTokenExpiry: {
        type: DataTypes.DATE,
        allowNull: true
    },
    firstName: {
        type: DataTypes.STRING,
        allowNull: false
    },
    lastName: {
        type: DataTypes.STRING,
        allowNull: false
    },
    countryId: {
        type: DataTypes.CHAR(2),
        allowNull: true,
        references: {
            model: 'countries',
            key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
    },
    phoneNumber: {
        type: DataTypes.STRING(20),
        allowNull: true,
        validate: {
            is: /^\+?[\d\s-]+$/
        }
    },
    alternatePhoneNumber: {
        type: DataTypes.STRING(20),
        allowNull: true,
        validate: {
            isPhoneOrEmpty(value) {
                if (value && value.length > 0 && !(/^\+?[\d\s-]+$/).test(value)) {
                    throw new Error('Invalid alternate phone number format');
                }
            }
        }
    },
    businessName: {
        type: DataTypes.STRING(200),
        allowNull: true
    },
    businessEmail: {
        type: DataTypes.STRING,
        allowNull: true,
        validate: {
            isEmail: true
        },
        unique: {
            name: 'unique_business_email',
            msg: 'This business email is already registered'
        }
    },
    billingAddressLine1: {
        type: DataTypes.STRING,
        allowNull: true
    },
    billingAddressLine2: {
        type: DataTypes.STRING,
        allowNull: true
    },
    billingCity: {
        type: DataTypes.STRING(100),
        allowNull: true
    },
    billingProvince: {
        type: DataTypes.STRING(100),
        allowNull: true
    },
    billingCountryId: {
        type: DataTypes.CHAR(2),
        allowNull: true,
        references: {
            model: 'countries',
            key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
    },
    billingPostalCode: {
        type: DataTypes.STRING(20),
        allowNull: true
    },
    markupPercentage: {
        type: DataTypes.DECIMAL(5, 2),
        allowNull: false,
        defaultValue: 0,
        validate: {
            isValid(value) {
                if (value === null || value === undefined) {
                    throw new Error('Markup percentage is required');
                }
                const num = Number(value);
                if (isNaN(num)) {
                    throw new Error('Markup percentage must be a valid number');
                }
                if (num < 0 || num > 100) {
                    throw new Error('Markup percentage must be between 0 and 100');
                }
            }
        }
    },
    isActive: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    },
    lastLogin: {
        type: DataTypes.DATE
    },
    loginAttempts: {
        type: DataTypes.TINYINT.UNSIGNED,
        defaultValue: 0
    },
    lockUntil: {
        type: DataTypes.DATE
    },
    // API key fields
    apiKey: {
        type: DataTypes.STRING(100),
        allowNull: true,
        unique: true
    },
    apiKeyHash: {
        type: DataTypes.STRING,
        allowNull: true
    },
    apiKeyLastReset: {
        type: DataTypes.DATE,
        allowNull: true
    }
}, {
    timestamps: true,
    tableName: 'users',
    indexes: [
        {
            name: 'fk_users_country',
            fields: ['countryId']
        },
        {
            name: 'fk_users_billing_country',
            fields: ['billingCountryId']
        },
        {
            name: 'idx_users_api_key_hash',
            fields: ['apiKeyHash']
        }
    ],
    hooks: {
        beforeSave: async (user) => {
            if (user.changed('password') && !user.password.startsWith('$2')) {
                const salt = await bcrypt.genSalt(10);
                user.password = await bcrypt.hash(user.password, salt);
            }

            if (user.billingCountryId === '') {
                user.billingCountryId = null;
            }
        }
    }
});

// Define associations
User.associate = (models) => {
    User.belongsTo(models.Country, {
        foreignKey: {
            name: 'countryId',
            allowNull: true
        },
        as: 'country',
        constraints: true,
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE'
    });
    User.belongsTo(models.Country, {
        foreignKey: {
            name: 'billingCountryId',
            allowNull: true
        },
        as: 'billingCountry',
        constraints: true,
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE'
    });
    User.hasOne(models.Wallet, {
        foreignKey: 'userId',
        as: 'wallet'
    });
};

// Instance Methods
User.prototype.validatePassword = async function(password) {
    return await bcrypt.compare(password, this.password);
};

User.prototype.incrementLoginAttempts = async function() {
    const maxAttempts = parseInt(process.env.MAX_LOGIN_ATTEMPTS || 3);
    const lockTime = parseInt(process.env.LOGIN_TIMEOUT || 45) * 60 * 1000;

    this.loginAttempts += 1;

    if (this.loginAttempts >= maxAttempts) {
        this.lockUntil = new Date(Date.now() + lockTime);
    }

    await this.save();
};

User.prototype.resetLoginAttempts = async function() {
    this.loginAttempts = 0;
    this.lockUntil = null;
    await this.save();
};

module.exports = User;
