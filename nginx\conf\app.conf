server {
    listen 80;
    server_name localhost api.vizlync.net;

    # For Let's Encrypt HTTP-01 challenge
    location ^~ /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files $uri =404;

        # Add debug headers
        add_header X-Debug-Info "Serving from ACME challenge handler" always;
        add_header Content-Type text/plain;
    }

    # Forward all other requests to the app
    location / {
        proxy_pass http://esim-backend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # Increase timeouts for long-running requests like sync operations
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;

        # Additional timeout settings for WebSocket connections
        proxy_buffering off;
        proxy_cache off;
    }
}
