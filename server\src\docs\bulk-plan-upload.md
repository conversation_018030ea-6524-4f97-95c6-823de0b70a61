# Bulk eSIM Plan Upload Documentation

## Overview
The bulk upload feature allows administrators to create multiple eSIM plans at once by uploading an Excel file. This feature is designed to streamline the process of adding multiple plans to the system.

## How to Use

### 1. Download Template
- Navigate to the eSIM Plans page in the admin portal
- Click the "Template" button to download the Excel template
- The template includes all required and optional fields with example data
- The template also includes a second sheet with all available country names for reference

### 2. Fill the Template
Fill in the Excel template with your plan data. Required fields are marked with an asterisk (*).

#### Required Fields:
- **Name*** - Plan name (max 100 characters)
- **Network Name*** - Network provider name (max 100 characters)
- **Buying Price*** - Cost price in USD (decimal)
- **Validity Days*** - Plan validity in days (integer)
- **Category*** - Plan category: `esim_realtime`, `esim_addon`, or `esim_replacement`
- **Plan Category*** - Plan type: `Voice and Data` or `Data Only`
- **Countries*** - Comma-separated country names (e.g., "United States,Canada,United Kingdom")
- **Activation Policy*** - One of: `Activation upon purchase`, `Activation upon first usage`, `Activation upon travel date`
- **Speed*** - `Restricted` or `Unrestricted`

#### Optional Fields:
- **Description** - Plan description
- **Network Type** - Default: `4G/LTE`
- **Region** - Geographic region
- **Selling Price** - Customer price in USD
- **Plan Type** - `Fixed`, `Unlimited`, or `Custom` (default: `Fixed`)
- **Plan Data** - Data amount (for Fixed plans)
- **Plan Data Unit** - `MB`, `GB`, or `TB`
- **Custom Plan Data** - Custom data description (for Custom plans)
- **Voice Available** - `Available` or `Not Available`
- **Voice Minutes** - Number of voice minutes
- **Voice Unit** - `Min`, `Hr`, or `Sec`
- **SMS Available** - `Available` or `Not Available`
- **SMS Count** - Number of SMS
- **Top Up Available** - `Available` or `Not Available`
- **Hotspot Available** - `Available` or `Not Available` (default: `Available`)
- **Stock Threshold** - Minimum stock level (default: 10)
- **Instructions** - Setup instructions
- **Plan Info** - Additional plan information
- **Additional Info** - Extra details

### 3. Upload File
- Click the "Upload Excel" button
- Select your filled template file
- The system will validate and process the data
- You'll receive feedback on successful uploads and any errors

## Validation Rules

### Data Validation:
- All required fields must be filled
- Numeric fields must contain valid numbers
- Country codes must exist in the system
- Enum fields must match allowed values
- For Voice and Data plans, voice minutes must be specified

### Business Rules:
- Selling price must be greater than buying price (except for replacement eSIMs)
- Validity days must be greater than 0 (except for replacement eSIMs)
- Plan data must be greater than 0 for Fixed plans (except for replacement/addon eSIMs)

## Error Handling
- The system processes each row individually
- If some rows fail validation, successful rows will still be created
- Detailed error messages indicate which rows failed and why
- Row numbers in error messages correspond to Excel row numbers

## Response Format
```json
{
  "message": "Bulk upload completed. Created: 5, Errors: 2",
  "plans": [...], // Array of successfully created plans
  "errors": [     // Array of validation errors
    {
      "row": 3,
      "error": "Missing required fields: name, networkName"
    }
  ],
  "summary": {
    "total": 7,
    "created": 5,
    "failed": 2
  }
}
```

## Best Practices

1. **Start Small**: Test with a few plans first
2. **Validate Data**: Double-check country names and enum values
3. **Use Template**: Always start with the downloaded template
4. **Reference Countries**: Use the "Available Countries" sheet in the template for exact country names
5. **Check Errors**: Review error messages carefully for failed rows
6. **Backup**: Keep a backup of your Excel file
7. **Batch Processing**: For large uploads (100+ plans), consider uploading during off-peak hours
8. **Network Stability**: Ensure stable internet connection for large uploads
9. **Monitor Progress**: Large uploads may take several minutes to complete

## Common Issues

### Invalid Country Names
- Use exact country names as they appear in the system (e.g., "United States", "United Kingdom", "Canada")
- Separate multiple countries with commas
- Ensure no extra spaces around country names
- Country names are case-insensitive but must match exactly

### Enum Value Errors
- Check that categorical fields match exactly (case-sensitive)
- Refer to the template for valid options

### Missing Required Fields
- Ensure all fields marked with * are filled
- Empty cells in required columns will cause validation errors

## API Endpoint
- **URL**: `POST /api/esim-plans/bulk`
- **Authentication**: Admin required
- **Content-Type**: `application/json`
- **Body**: Array of plan objects

## File Formats Supported
- Excel (.xlsx, .xls)
- CSV files are also accepted by the file input

## Limitations
- Maximum recommended: 200 plans per upload
- File size limit: 10MB
- Processing time depends on number of plans and server load
- Large uploads (150+ plans) may take 2-5 minutes to complete
- System will validate each plan individually and report detailed errors
