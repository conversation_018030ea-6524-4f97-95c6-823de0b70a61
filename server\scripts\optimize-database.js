#!/usr/bin/env node

/**
 * Database Optimization Script for eSIM Platform
 *
 * This script applies performance optimizations to the database:
 * 1. Creates optimized indexes
 * 2. Analyzes table statistics
 * 3. Checks current performance metrics
 * 4. Provides optimization recommendations
 */

const { sequelize } = require('../src/models');
const fs = require('fs');
const path = require('path');

async function runOptimizations() {
    try {
        console.log('🚀 Starting database optimization...\n');

        // 1. Apply performance indexes
        console.log('📊 Creating optimized indexes...');
        const indexSqlPath = path.join(__dirname, '../src/migrations/20240401_optimize_performance_indexes.sql');

        if (fs.existsSync(indexSqlPath)) {
            const indexSql = fs.readFileSync(indexSqlPath, 'utf8');

            // Split by semicolon and execute each statement
            const statements = indexSql
                .split(';')
                .map(stmt => stmt.trim())
                .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

            for (const statement of statements) {
                try {
                    await sequelize.query(statement);
                    console.log(`✅ Executed: ${statement.substring(0, 50)}...`);
                } catch (error) {
                    if (error.message.includes('Duplicate key name') ||
                        error.message.includes('already exists') ||
                        error.message.includes('Duplicate entry')) {
                        console.log(`⚠️  Index already exists: ${statement.substring(0, 50)}...`);
                    } else if (error.message.includes('syntax') && statement.includes('DROP INDEX')) {
                        console.log(`⚠️  DROP INDEX syntax not supported (safe to ignore): ${statement.substring(0, 50)}...`);
                    } else {
                        console.error(`❌ Error executing: ${statement.substring(0, 50)}...`);
                        console.error(`   ${error.message}`);
                    }
                }
            }
        } else {
            console.log('⚠️  Index optimization file not found');
        }

        // 2. Analyze tables
        console.log('\n🔍 Analyzing table statistics...');
        const tables = ['esimplans', 'esimplancountries', 'countries', 'providers', 'users'];

        for (const table of tables) {
            try {
                await sequelize.query(`ANALYZE TABLE ${table}`);
                console.log(`✅ Analyzed table: ${table}`);
            } catch (error) {
                console.error(`❌ Error analyzing ${table}: ${error.message}`);
            }
        }

        // 3. Check table sizes and row counts
        console.log('\n📈 Checking table statistics...');
        const tableStats = await sequelize.query(`
            SELECT
                table_name AS 'Table',
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size_MB',
                table_rows AS 'Rows',
                ROUND((index_length / 1024 / 1024), 2) AS 'Index_Size_MB'
            FROM information_schema.TABLES
            WHERE table_schema = DATABASE()
            AND table_name IN ('esimplans', 'esimplancountries', 'countries', 'providers', 'users')
            ORDER BY (data_length + index_length) DESC
        `, { type: sequelize.QueryTypes.SELECT });

        console.table(tableStats);

        // 4. Check index usage
        console.log('\n🔎 Checking index usage...');
        try {
            const indexUsage = await sequelize.query(`
                SELECT
                    OBJECT_NAME as 'Table',
                    INDEX_NAME as 'Index',
                    COUNT_FETCH as 'Reads',
                    COUNT_INSERT as 'Inserts',
                    COUNT_UPDATE as 'Updates'
                FROM performance_schema.table_io_waits_summary_by_index_usage
                WHERE OBJECT_SCHEMA = DATABASE()
                AND OBJECT_NAME IN ('esimplans', 'esimplancountries', 'countries')
                AND COUNT_FETCH > 0
                ORDER BY COUNT_FETCH DESC
                LIMIT 10
            `, { type: sequelize.QueryTypes.SELECT });

            if (indexUsage.length > 0) {
                console.table(indexUsage);
            } else {
                console.log('📊 Index usage statistics not available (performance_schema may be disabled)');
            }
        } catch (error) {
            console.log('📊 Index usage statistics not available');
        }

        // 5. Test query performance
        console.log('\n⚡ Testing query performance...');

        const testQueries = [
            {
                name: 'Default plans query',
                query: `
                    SELECT COUNT(*) as count
                    FROM esimplans
                    WHERE status = 'visible'
                    AND isActive = true
                    AND category = 'esim_realtime'
                `
            },
            {
                name: 'Country filtered query',
                query: `
                    SELECT COUNT(DISTINCT ep.id) as count
                    FROM esimplans ep
                    INNER JOIN esimplancountries epc ON ep.id = epc.esimPlanId
                    WHERE epc.countryId = 'US'
                    AND ep.status = 'visible'
                    AND ep.isActive = true
                    AND ep.category = 'esim_realtime'
                `
            },
            {
                name: 'Search query',
                query: `
                    SELECT COUNT(*) as count
                    FROM esimplans
                    WHERE status = 'visible'
                    AND isActive = true
                    AND category = 'esim_realtime'
                    AND (name LIKE '%data%' OR networkName LIKE '%data%')
                `
            }
        ];

        for (const test of testQueries) {
            const startTime = Date.now();
            try {
                const result = await sequelize.query(test.query, {
                    type: sequelize.QueryTypes.SELECT
                });
                const duration = Date.now() - startTime;
                console.log(`✅ ${test.name}: ${duration}ms (${result[0].count} records)`);
            } catch (error) {
                console.error(`❌ ${test.name}: ${error.message}`);
            }
        }

        // 6. Recommendations
        console.log('\n💡 Optimization Recommendations:');
        console.log('1. Monitor slow query log for queries taking > 2 seconds');
        console.log('2. Consider Redis caching for frequently accessed data');
        console.log('3. Use connection pooling with appropriate limits');
        console.log('4. Regular ANALYZE TABLE to keep statistics updated');
        console.log('5. Monitor index usage and remove unused indexes');

        console.log('\n✅ Database optimization completed successfully!');

    } catch (error) {
        console.error('❌ Error during optimization:', error);
        process.exit(1);
    } finally {
        await sequelize.close();
    }
}

// Run if called directly
if (require.main === module) {
    runOptimizations();
}

module.exports = { runOptimizations };
