const express = require('express');
const router = express.Router();
const { isAuthenticated, isAdmin } = require('../middleware/auth');
const Provider = require('../models/Provider');

// Get all providers
router.get('/', isAuthenticated, isAdmin, async (req, res) => {
    try {
        const providers = await Provider.findAll({
            order: [['createdAt', 'DESC']]
        });
        res.json(providers);
    } catch (error) {
        console.error('Error fetching providers:', error);
        res.status(500).json({ message: 'Failed to fetch providers' });
    }
});

// Get single provider
router.get('/:id', isAuthenticated, isAdmin, async (req, res) => {
    try {
        const provider = await Provider.findByPk(req.params.id);
        if (!provider) {
            return res.status(404).json({ message: 'Provider not found' });
        }
        res.json(provider);
    } catch (error) {
        console.error('Error fetching provider:', error);
        res.status(500).json({ message: 'Failed to fetch provider' });
    }
});

// Create provider
router.post('/', isAuthenticated, isAdmin, async (req, res) => {
    try {
        const provider = await Provider.create(req.body);
        res.status(201).json(provider);
    } catch (error) {
        console.error('Error creating provider:', error);
        res.status(500).json({ message: 'Failed to create provider' });
    }
});

// Update provider
router.put('/:id', isAuthenticated, isAdmin, async (req, res) => {
    try {
        const provider = await Provider.findByPk(req.params.id);
        if (!provider) {
            return res.status(404).json({ message: 'Provider not found' });
        }
        await provider.update(req.body);
        res.json(provider);
    } catch (error) {
        console.error('Error updating provider:', error);
        res.status(500).json({ message: 'Failed to update provider' });
    }
});

// Delete provider
router.delete('/:id', isAuthenticated, isAdmin, async (req, res) => {
    try {
        const provider = await Provider.findByPk(req.params.id);
        if (!provider) {
            return res.status(404).json({ message: 'Provider not found' });
        }
        await provider.destroy();
        res.json({ message: 'Provider deleted successfully' });
    } catch (error) {
        console.error('Error deleting provider:', error);
        res.status(500).json({ message: 'Failed to delete provider' });
    }
});

module.exports = router;


