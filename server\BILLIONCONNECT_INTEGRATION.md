# BillionConnect Integration

This document describes the integration of BillionConnect API provider into the eSIM management system.

## Overview

BillionConnect is a global eSIM provider that offers various trade types for managing eSIM products. This integration currently implements the **F002 tradeType** for retrieving available commodities.

## API Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```env
# BillionConnect API Configuration
BILLIONCONNECT_API_URL=https://api-flow-ts.billionconnect.com/Flow/saler/2.0/invoke
BILLIONCONNECT_CHANNEL_ID=your_channel_id_here
BILLIONCONNECT_APP_SECRET=your_app_secret_here
```

### Authentication

BillionConnect uses MD5 signature-based authentication:

- **x-channel-id**: Your unique channel identifier
- **x-sign-method**: Always "md5"
- **x-sign-value**: MD5 hash of `appSecret + JSON.stringify(requestBody)`

## Implemented Features

### F002 - Get Available Commodities

Retrieves all available eSIM products from BillionConnect.

**Request Format:**
```json
{
  "tradeType": "F002",
  "tradeTime": "YYYY-MM-DD HH:mm:ss",  // GMT+8 timezone
  "tradeData": {
    "salesMethod": "5",   // 5 = distribution
    "language": "2"       // 2 = English
  }
}
```

**Response Mapping:**

| BillionConnect Field | Our Database Field | Description |
|---------------------|-------------------|-------------|
| `operator` | `networkName` | Network operator name |
| `network` | `networkType` | Network type (4G, 5G, etc.) |
| `name` (country) | Country name | Country name |
| `mcc` | Country id | Country identifier |
| `skuId` | `externalProductId` | External product identifier |
| `skuId` | `externalSkuId` | External SKU identifier |
| `hotspotSupport` | `hotspot` | Hotspot support (1=Available, 0=Not Available) |
| `highFlowSize` | `planData` | Data capacity (converted from KB to MB/GB, -1=Unlimited) |
| `name` (product) | `name` | Plan name |
| `days` | `validityDays` | Validity period in days |
| `desc` | `description` | Product description |

### Plan Types

- **Fixed Plans**: When `highFlowSize` contains a positive number (data capacity in KB)
- **Unlimited Plans**: When `highFlowSize` is `-1` (unlimited data)

### F003 - Get Price for Plans

Retrieves pricing information for specific eSIM plans.

**Request Format:**
```json
{
  "tradeType": "F003",
  "tradeTime": "YYYY-MM-DD HH:mm:ss",  // GMT+8 timezone
  "tradeData": {
    "salesMethod": "5",   // 5 = distribution
    "language": "2",      // 2 = English
    "planList": [
      {
        "skuId": "1090",
        "copies": 1
      }
    ]
  }
}
```

**Response Mapping:**

| BillionConnect Field | Our Database Field | Description |
|---------------------|-------------------|-------------|
| `settlementPrice` | `buyingPrice` | Unit price for the plan |
| `copies` | `quantity` | Number of plans requested |
| `skuId` | `externalSkuId` | Plan identifier |

## File Structure

```
server/src/
├── services/
│   ├── billionconnect.service.js     # Main BillionConnect API service
│   └── provider.factory.js           # Updated to include BillionConnect
├── seeders/
│   └── add_billionconnect_provider.js # Database seeder
└── test_billionconnect.js            # Test script
```

## Usage

### 1. Database Setup

Run the seeder to add BillionConnect provider to the database:

```bash
npx sequelize-cli db:seed --seed add_billionconnect_provider.js
```

### 2. Fetch Products

```javascript
const billionconnectService = require('./src/services/billionconnect.service');

// Get all commodities
const products = await billionconnectService.getProducts();
console.log(`Retrieved ${products.length} products`);

// Get raw commodities data
const commodities = await billionconnectService.getCommodities();
```

### 3. Fetch Prices

```javascript
const billionconnectService = require('./src/services/billionconnect.service');

// Get price for a single plan
const price = await billionconnectService.getSinglePlanPrice('1090', 1);
console.log('Price:', price.buyingPrice);

// Get prices for multiple plans
const priceRequests = [
  { skuId: '1090', copies: 1 },
  { skuId: '1091', copies: 2 }
];
const prices = await billionconnectService.getPlanPrices(priceRequests);

// Get products with integrated price data
const productsWithPrices = await billionconnectService.getProductsWithPrices();
```

### 4. Provider Factory Integration

```javascript
const providerFactory = require('./src/services/provider.factory');

// Get BillionConnect provider
const provider = providerFactory.getProvider('billionconnect');

// Standardize a product
const standardizedProduct = await providerFactory.standardizeProduct('billionconnect', product);
```

## Testing

Run the test scripts to verify the integration:

```bash
# Test basic integration
node server/test_billionconnect.js

# Test price functionality
node server/test_billionconnect_prices.js
```

The test scripts will:
1. Check service configuration
2. Test signature generation
3. Test GMT+8 time generation
4. Test product transformation
5. Test provider factory integration
6. Test product standardization
7. Test F003 price functionality
8. Test price data transformation

## Future Implementation

The following BillionConnect trade types are planned for future implementation:

- **F040** - Create eSIM order
- **N009** - eSIM QR code info
- **F012** - Query data plan usage info
- **F007** - Create recharge order
- **F008** - Cancel Order
- **F011** - Query order information

## Error Handling

The service includes comprehensive error handling:

- API authentication errors
- Network connectivity issues
- Invalid response formats
- Product transformation errors

All errors are logged with detailed information for debugging.

## Security Notes

- The app secret is used for signature generation and should be kept secure
- All API requests are made over HTTPS
- Request bodies are compacted (no extra spaces) for signature consistency
- Signatures are generated using MD5 as required by BillionConnect

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify `BILLIONCONNECT_CHANNEL_ID` and `BILLIONCONNECT_APP_SECRET`
   - Ensure request body is properly formatted (compact JSON)

2. **Time Zone Issues**
   - BillionConnect requires GMT+8 timezone for `tradeTime`
   - The service automatically handles timezone conversion

3. **Product Transformation Errors**
   - Check that the API response format matches expected structure
   - Verify country and operator information is present

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
```

This will provide detailed logs of API requests and responses.
