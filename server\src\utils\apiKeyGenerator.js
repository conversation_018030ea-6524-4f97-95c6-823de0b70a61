const crypto = require('crypto');
const bcrypt = require('bcrypt');

/**
 * Generates a secure random API key with a prefix
 * @returns {string} The generated API key
 */
const generateApiKey = () => {
  const prefix = 'vlz_api_';
  const randomBytes = crypto.randomBytes(24).toString('hex');
  return `${prefix}${randomBytes}`;
};

/**
 * Hashes an API key for secure storage
 * @param {string} apiKey - The API key to hash
 * @returns {Promise<string>} - The hashed API key
 */
const hashApiKey = async (apiKey) => {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(apiKey, salt);
};

/**
 * Verify if an API key matches the stored hash
 * @param {string} apiKey - The API key to verify
 * @param {string} hash - The stored hash
 * @returns {Promise<boolean>} - Whether the API key is valid
 */
const verifyApiKey = async (apiKey, hash) => {
  return await bcrypt.compare(apiKey, hash);
};

/**
 * Generates a partially hidden API key for display
 * @param {string} apiKey - The full API key
 * @returns {string} - The masked API key (e.g., vlz_api_****_****_abc4)
 */
const maskApiKey = (apiKey) => {
  if (!apiKey) return '';

  // Keep the prefix and last 4 characters visible
  const prefix = apiKey.substring(0, 8); // "vlz_api_"
  const suffix = apiKey.substring(apiKey.length - 4);
  const middleLength = apiKey.length - prefix.length - suffix.length;
  const maskedMiddle = '*'.repeat(Math.min(middleLength, 12));

  return `${prefix}${maskedMiddle}${suffix}`;
};

module.exports = {
  generateApiKey,
  hashApiKey,
  verifyApiKey,
  maskApiKey
};