import React, { useState, useRef, useEffect } from 'react';
import { X, ChevronDown, ChevronUp, Check } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export function MultiSelect({
  value = [],
  onChange,
  options = [],
  placeholder = 'Select items...',
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const containerRef = useRef(null);

  const selectedItems = value.map(v =>
    options.find(option => option.value === v)
  ).filter(Boolean);

  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleRemove = (itemValue) => {
    onChange(value.filter(v => v !== itemValue));
  };

  const handleSelect = (option) => {
    const newValue = value.includes(option.value)
      ? value.filter(v => v !== option.value)
      : [...value, option.value];
    onChange(newValue);
    setSearchTerm('');
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData('text');
    
    // Split by common separators (comma, newline, semicolon)
    const countryNames = pastedText
      .split(/[,;\n]/)
      .map(name => name.trim())
      .filter(Boolean); // Remove empty strings

    if (countryNames.length > 0) {
      const matchedOptions = options.filter(option => 
        countryNames.some(name => 
          option.label.toLowerCase().includes(name.toLowerCase()) ||
          name.toLowerCase().includes(option.label.toLowerCase())
        )
      );

      if (matchedOptions.length > 0) {
        const newValues = [...new Set([
          ...value,
          ...matchedOptions.map(opt => opt.value)
        ])];
        onChange(newValues);
        setSearchTerm('');
      }
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const renderFlag = (countryId) => (
    <img
      src={`https://flagcdn.com/w20/${countryId.toLowerCase()}.png`}
      alt=""
      className="w-4 h-3 object-cover rounded-sm"
      onError={(e) => {
        e.target.onerror = null;
        e.target.src = 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/No_flag.svg/32px-No_flag.svg.png';
      }}
    />
  );

  return (
    <div ref={containerRef} className="relative">
      <div
        className={cn(
          "flex min-h-[38px] w-full flex-wrap gap-1 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",
          isOpen && "ring-2 ring-ring ring-offset-2"
        )}
        onClick={() => setIsOpen(true)}
      >
        {selectedItems.map((item) => (
          <Badge
            key={item.value}
            variant="secondary"
            className="flex items-center gap-1.5 px-2"
          >
            {renderFlag(item.value)}
            {item.label}
            <button
              type="button"
              className="bg-secondary/80 rounded-full p-0.5 hover:bg-secondary"
              onClick={(e) => {
                e.stopPropagation();
                handleRemove(item.value);
              }}
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        ))}
        <input
          type="text"
          className="flex-1 bg-transparent outline-none placeholder:text-muted-foreground min-w-[120px]"
          placeholder={selectedItems.length === 0 ? placeholder : "Type or paste country names..."}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onPaste={handlePaste}
        />
        <button
          type="button"
          className="ml-auto self-center"
          onClick={(e) => {
            e.stopPropagation();
            setIsOpen((prev) => !prev);
          }}
        >
          {isOpen ? (
            <ChevronUp className="h-4 w-4 opacity-50" />
          ) : (
            <ChevronDown className="h-4 w-4 opacity-50" />
          )}
        </button>
      </div>
      {isOpen && filteredOptions.length > 0 && (
        <div className="absolute z-10 mt-1 max-h-[300px] w-full overflow-auto rounded-md border bg-popover p-1 text-popover-foreground shadow-md">
          {filteredOptions.map((option) => (
            <div
              key={option.value}
              className="relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground"
              onClick={() => handleSelect(option)}
            >
              <div className="flex items-center gap-2 flex-1">
                <div className={cn(
                  "flex h-4 w-4 items-center justify-center rounded border border-primary",
                  value.includes(option.value) && "bg-primary text-primary-foreground"
                )}>
                  {value.includes(option.value) && (
                    <Check className="h-3 w-3 text-white" />
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {renderFlag(option.value)}
              {option.label}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
