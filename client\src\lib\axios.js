import axios from 'axios';

const api = axios.create({
    baseURL: import.meta.env.VITE_API_URL,
    withCredentials: true,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    // Removed validateStatus to use default behavior (2xx = success, 4xx/5xx = error)
});

// Add a request interceptor to add the auth token
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        // Detailed request logging
        // console.log('Full Request Config:', {
        //     baseURL: config.baseURL,
        //     url: config.url,
        //     fullURL: config.baseURL + config.url,
        //     method: config.method,
        //     headers: config.headers,
        //     data: config.data
        // });
        return config;
    },
    (error) => {
        // console.error('Request error:', error);
        return Promise.reject(error);
    }
);

// Add a response interceptor to handle token expiration and refresh
api.interceptors.response.use(
    (response) => {
        // Detailed response logging
        // console.log('Full Response:', {
        //     url: response.config.url,
        //     fullURL: response.config.baseURL + response.config.url,
        //     status: response.status,
        //     statusText: response.statusText,
        //     headers: response.headers,
        //     data: response.data
        // });
        return response;
    },
    async (error) => {
        // Suppress console errors in production for network requests
        if (import.meta.env.PROD) {
            // Create a custom error object without the noisy console output
            const cleanError = new Error(error.message);
            cleanError.response = error.response;
            cleanError.config = error.config;
            cleanError.request = error.request;
            cleanError.code = error.code;
            cleanError.status = error.response?.status;

            // Prevent the default axios console error logging in production
            error.isAxiosError = false;
        }

        // Detailed error logging (only in development)
        if (import.meta.env.DEV) {
            // console.error('Full Response Error:', {
            //     config: error.config,
            //     status: error.response?.status,
            //     statusText: error.response?.statusText,
            //     headers: error.response?.headers,
            //     data: error.response?.data,
            //     fullError: error
            // });
        }

        const originalRequest = error.config;

        // If the error is 401 and we haven't retried yet
        if (error.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            try {
                // Try to refresh the token
                const response = await api.post('/api/auth/refresh');
                const { token } = response.data;

                if (token) {
                    localStorage.setItem('token', token);
                    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
                    return api(originalRequest);
                }
            } catch (refreshError) {
                // If refresh fails, logout
                if (import.meta.env.DEV) {
                    // console.error('Token refresh failed:', refreshError);
                }
                localStorage.removeItem('token');
                window.location.href = '/login';
            }
        }

        return Promise.reject(error);
    }
);

// // Log the initial configuration
// console.log('API Base Configuration:', {
//     baseURL: api.defaults.baseURL,
//     headers: api.defaults.headers,
//     withCredentials: api.defaults.withCredentials
// });

export default api;
