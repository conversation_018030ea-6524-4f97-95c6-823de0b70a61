# Default server configuration
# This is the main configuration file that Nginx loads first

server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name _;

    # For Let's Encrypt HTTP-01 challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files $uri =404;

        # Add debug headers
        add_header X-Debug-Info "Serving from default server ACME challenge handler" always;
        add_header Content-Type text/plain;
    }

    # Redirect all other HTTP traffic to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}
