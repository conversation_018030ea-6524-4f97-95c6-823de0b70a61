# Server Configuration
PORT=3000
NODE_ENV=development

# Client URL
CLIENT_URL=http://localhost:5000

# Database Configuration
DB_HOST=
DB_USER=
DB_PASSWORD=
DB_NAME=

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# ZeptoMail Configuration
ZEPTO_API_URL=https://api.zeptomail.in/v1.1/email
ZEPTO_API_KEY=
ZEPTO_FROM_EMAIL=<EMAIL>

# Security
MAX_LOGIN_ATTEMPTS=3
LOGIN_TIMEOUT=45m

# Mobimatter API Configuration
MOBIMATTER_API_URL=https://api.mobimatter.com/mobimatter/api/v2
MOBIMATTER_API_KEY=
MOBIMATTER_MERCHANT_ID=

# BillionConnect API Configuration
BILLIONCONNECT_API_URL=https://api-flow-ts.billionconnect.com/Flow/saler/2.0/invoke
BILLIONCONNECT_CHANNEL_ID=
BILLIONCONNECT_APP_SECRET=

# API Configuration
API_BASE_URL=http://localhost:3000/api/v1
DEV_API_URL=http://localhost:3000/api/v1
PROD_API_URL=https://esim-app.onrender.com/api/v1


