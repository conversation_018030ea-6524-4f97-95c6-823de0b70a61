FROM nginx:1.21-alpine

# Remove default Nginx configuration
RUN rm /etc/nginx/conf.d/default.conf

# Copy our custom configuration
COPY ./conf/app.conf /etc/nginx/conf.d/

# Create ACME challenge directory
RUN mkdir -p /var/www/certbot/.well-known/acme-challenge/

# Create a test file for ACME challenge verification
RUN echo "This is a test file for ACME challenge verification" > /var/www/certbot/.well-known/acme-challenge/test-file

# Set proper permissions
RUN chmod -R 755 /var/www/certbot

# Expose ports
EXPOSE 80 443

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]
