const { EsimPlan, Country, Order, EsimStock, User, Wallet, WalletTransaction, Provider, EsimPlanCountries, EsimPlanStockHistory } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const generateOrderId = require('../utils/generateOrderId');
const { v4: uuidv4 } = require('uuid');
const QRCode = require('qrcode');
const providerFactory = require('../services/provider.factory');
const mobimatterService = require('../services/mobimatter.service');
const { getCachedPlan, setCachePlan, invalidatePlanCache } = require('../utils/cacheManager');

// Helper function to validate ISO date format (YYYY-MM-DD)
const isValidISODate = (dateString) => {
    if (!/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
        return false;
    }
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
        return false;
    }
    
    return date.toISOString().slice(0, 10) === dateString;
};

/**
 * Get all available products
 * @route GET /api/v1/products
 */
exports.getProducts = async (req, res) => {
    try {
        const { region, country } = req.query;
        
        // Get the partner from the request (added by authenticateApiKey middleware)
        const partner = req.partner;
        
        // Build the query - no pagination, only esim_realtime and esim_addon plans
        const query = {
            where: {
                status: 'visible',
                isActive: true,
                category: {
                    [Op.in]: ['esim_realtime', 'esim_addon']
                }
            },
            include: [
                {
                    model: Country,
                    as: 'countries',
                    attributes: ['id', 'name'],
                    through: { attributes: [] }
                }
            ],
            order: [['createdAt', 'DESC']]
            // No limit or offset - return all plans at once
        };
        
        // Add region filter if provided
        if (region) {
            query.where.region = {
                [Op.like]: `%${region}%`
            };
        }
        
        // Add country filter if provided
        if (country) {
            query.include[0].where = {
                id: country.toUpperCase()
            };
        }
        
        // Fetch the products
        const { count, rows: products } = await EsimPlan.findAndCountAll(query);
        
        // Transform the products for the API response
        const formattedProducts = products.map(product => {
            // Calculate price based on markup percentage if selling price is null
            let price = product.sellingPrice;
            if (price === null && partner && partner.markupPercentage) {
                // Calculate price using partner's markup percentage
                const markup = parseFloat(partner.markupPercentage) / 100;
                price = parseFloat(product.buyingPrice) * (1 + markup);
                // Round to 2 decimal places
                price = Math.round(price * 100) / 100;
            }
            
            return {
                productId: product.productId,
                name: product.name,
                description: product.description,
                planInfo: product.planInfo,
                instructions: product.instructions,
                price: price,
                validityDays: product.validityDays,
                countries: product.countries.map(c => c.id),
                region: typeof product.region === 'string' 
                    ? product.region.split(',').map(r => r.trim()) 
                    : product.region,
                dataAmount: product.planData,
                dataUnit: product.planDataUnit,
                customPlanData: product.customPlanData,
                voiceMin: product.voiceMin,
                voiceMinUnit: product.voiceMinUnit,
                speed: product.speed,
                planType: product.planType,
                category: product.category,
                networkType: product.networkType,
                isVoiceAvailable: product.is_voice === 'Available',
                isSmsAvailable: product.is_sms === 'Available',
                hotspotAvailable: product.hotspot === 'Available',
                topUpAvailable: product.top_up === 'Available',
                profile: product.profile,
                activationPolicy: product.activationPolicy,
                startDateEnabled: product.startDateEnabled,
                features: product.features || []
            };
        });
        
        // Return the response - without pagination info
        res.json({
            success: true,
            data: {
                products: formattedProducts,
                total: count
            }
        });
    } catch (error) {
        console.error('Error fetching products:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: 'Failed to fetch products'
            }
        });
    }
};

// Helper function to generate QR code as data URL
const generateQRCode = async (text) => {
    try {
        return await QRCode.toDataURL(text, {
            errorCorrectionLevel: 'H',
            type: 'image/png',
            margin: 2,
            width: 400,
            color: {
                dark: '#000000',
                light: '#ffffff'
            },
            rendererOpts: {
                quality: 1
            }
        });
    } catch (err) {
        console.error('Error generating QR code:', err);
        return null;
    }
};

/**
 * Get order details
 * @route GET /api/v1/order/:orderId
 */
exports.getOrderDetails = async (req, res) => {
    try {
        // Get partner from request (added by authenticateApiKey middleware)
        const partner = req.partner;
        const userId = partner.id;
        
        // Get orderId from URL parameters
        const { orderId } = req.params;
        
        if (!orderId) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_REQUEST',
                    message: 'Order ID is required'
                }
            });
        }
        
        // Find the order and ensure it belongs to the partner
        const order = await Order.findOne({
            where: { 
                id: orderId,
                userId // Ensure the order belongs to the partner
            },
            include: [{
                model: EsimPlan,
                as: 'plan',
                include: [{
                    model: Country,
                    as: 'countries',
                    through: { attributes: [] }
                }, {
                    model: Provider,
                    as: 'provider'
                }]
            }, {
                model: EsimStock,
                as: 'stock'
            }]
        });
        
        if (!order) {
            // Check if the order exists but doesn't belong to this partner
            const anyOrder = await Order.findByPk(orderId);
            if (anyOrder) {
                // Order exists but doesn't belong to this partner
                return res.status(403).json({
                    success: false,
                    error: {
                        code: 'ACCESS_DENIED',
                        message: 'You do not have access to this order'
                    }
                });
            }
            
            // Order doesn't exist at all
            return res.status(404).json({
                success: false,
                error: {
                    code: 'ORDER_NOT_FOUND',
                    message: 'Order not found'
                }
            });
        }
        
        // Determine if this is an external order from a provider like Mobimatter
        const isExternalOrder = order.plan?.provider?.type === 'API';
        
        // For external orders, extract eSIM data from the provider response
        let esimData = {};
        
        if (isExternalOrder) {
            // For Mobimatter orders, the data is in orderLineItem.lineItemDetails
            if (order.providerResponse?.orderLineItem?.lineItemDetails) {
                const lineItems = order.providerResponse.orderLineItem.lineItemDetails;
                
                // Extract data from line items
                esimData = {
                    iccid: lineItems.find(item => item.name === 'ICCID')?.value,
                    smdpAddress: lineItems.find(item => item.name === 'SMDP_ADDRESS')?.value,
                    lpaString: lineItems.find(item => item.name === 'LOCAL_PROFILE_ASSISTANT')?.value,
                    accessPointName: lineItems.find(item => item.name === 'ACCESS_POINT_NAME')?.value,
                    activationCode: lineItems.find(item => item.name === 'ACTIVATION_CODE')?.value,
                    phoneNumber: lineItems.find(item => item.name === 'PHONE_NUMBER')?.value,
                    qrCodeUrl: lineItems.find(item => item.name === 'QR_CODE')?.value || null
                };
            } else {
                // If it's not in the expected format, use providerMetadata
                esimData = order.providerMetadata || {};
            }
        } else {
            // For local stock orders, use the stock data
            esimData = {
                iccid: order.stock?.iccid,
                smdpAddress: order.stock?.smdpAddress,
                lpaString: order.stock?.lpaString,
                accessPointName: order.stock?.accessPointName,
                activationCode: order.stock?.activationCode,
                phoneNumber: order.stock?.phoneNumber,
                qrCodeUrl: order.stock?.qrCodeUrl
            };
        }
        
        // Format the response
        return res.json({
            success: true,
            data: {
                orderId: order.id,
                status: order.status,
                product: {
                    productId: order.plan.productId,
                    name: order.plan.name
                },
                orderTotal: parseFloat(order.orderTotal),
                quantity: order.quantity,
                startDate: order.startDate,
                expiryDate: order.expiryDate,
                iccid: esimData.iccid || order.providerMetadata?.iccid || null,
                smdpAddress: esimData.smdpAddress || order.providerMetadata?.smdpAddress || null,
                accessPointName: esimData.accessPointName || order.providerMetadata?.accessPointName || null,
                lpaString: esimData.lpaString || order.providerMetadata?.lpaString || null,
                activationCode: esimData.activationCode || order.providerMetadata?.activationCode || null,
                qrCodeUrl: esimData.qrCodeUrl || order.providerMetadata?.qrCodeUrl || null,
                walletAuthTransactionId: order.walletAuthTransactionId,
                createdAt: order.createdAt
            }
        });
    } catch (error) {
        console.error('Error fetching order details:', error);
        return res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'Error retrieving order details'
            }
        });
    }
};

/**
 * Create a new order
 * @route POST /api/v1/order
 */
exports.createOrder = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        // Get partner from request (added by authenticateApiKey middleware)
        const partner = req.partner;
        const userId = partner.id;
        
        // Validate request body
        const { productId, startDate } = req.body;
        
        if (!productId) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_REQUEST',
                    message: 'Product ID is required'
                }
            });
        }
        
        // Validate productId format (should be a string of alphanumeric characters)
        if (typeof productId !== 'string' || !/^[A-Z0-9]{6,12}$/.test(productId)) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_PRODUCT_ID',
                    message: 'Invalid product ID format'
                }
            });
        }
        
        // Validate startDate format if provided
        if (startDate && !isValidISODate(startDate)) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_DATE_FORMAT',
                    message: 'Start date must be in ISO format (YYYY-MM-DD)'
                }
            });
        }
        
        // Find the eSIM plan by productId
        const plan = await EsimPlan.findOne({
            where: { 
                productId,
                status: 'visible',
                isActive: true
            },
            include: [{
                model: Country,
                as: 'countries',
                through: { attributes: [] }
            }]
        }, { transaction: t });
        
        if (!plan) {
            await t.rollback();
            return res.status(404).json({
                success: false,
                error: {
                    code: 'PRODUCT_NOT_FOUND',
                    message: 'Product not found or not available'
                }
            });
        }
        
        // Calculate the price based on the partner's markup if sellingPrice is null
        let orderTotal = plan.sellingPrice;
        if (orderTotal === null && partner.markupPercentage) {
            const markup = parseFloat(partner.markupPercentage) / 100;
            orderTotal = parseFloat(plan.buyingPrice) * (1 + markup);
            // Round to 2 decimal places
            orderTotal = Math.round(orderTotal * 100) / 100;
        }
        
        // Check if the eSIM plan allows start date
        if (startDate && !plan.startDateEnabled) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'START_DATE_NOT_ALLOWED',
                    message: 'This product does not support setting a start date'
                }
            });
        }

        // Get partner's wallet
        const wallet = await Wallet.findOne({
            where: { userId },
            transaction: t,
            lock: true
        });

        if (!wallet) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'WALLET_NOT_FOUND',
                    message: 'Partner wallet not found'
                }
            });
        }

        // Check wallet balance
        if (parseFloat(wallet.balance) < orderTotal) {
            await t.rollback();
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INSUFFICIENT_BALANCE',
                    message: 'Insufficient wallet balance',
                    required: orderTotal,
                    available: parseFloat(wallet.balance)
                }
            });
        }

        let order = null;
        const walletAuthTransactionId = uuidv4();
        
        try {
            // Check if the plan is from an external provider (e.g., Mobimatter)
            const provider = await plan.getProvider({ transaction: t });
            
            if (provider && provider.type === 'API') {
                // Get provider service instance
                const providerService = providerFactory.getProvider(provider.name);
                
                // Create initial order record with pending status
                order = await Order.create({
                    userId,
                    esimPlanId: plan.id,
                    esimStockId: null,
                    quantity: 1,
                    orderTotal,
                    startDate: startDate || null,
                    status: 'pending',
                    walletAuthTransactionId
                }, { transaction: t });
                
                // Create external order with the provider
                const externalOrder = await providerService.createOrder({
                    productId: plan.externalProductId,
                    skuId: plan.externalSkuId,
                    quantity: 1,
                    customerReference: `api_${userId}_${Date.now()}`
                });
                
                if (!externalOrder || !externalOrder.orderId) {
                    throw new Error('Invalid response from provider: Missing order ID');
                }
                
                // Complete the external order
                const completedOrder = await providerService.completeOrder(externalOrder.orderId);
                
                // Extract eSIM details from the completed order response
                let iccid, smdpAddress, lpaString, accessPointName, activationCode, phoneNumber, qrCodeUrl;
                
                // Check if response contains orderLineItem.lineItemDetails
                if (completedOrder.orderLineItem?.lineItemDetails) {
                    const lineItems = completedOrder.orderLineItem.lineItemDetails;
                    const getLineItemValue = (name) => lineItems.find(item => item.name === name)?.value || '';
                    
                    iccid = getLineItemValue('ICCID');
                    smdpAddress = getLineItemValue('SMDP_ADDRESS');
                    lpaString = getLineItemValue('LOCAL_PROFILE_ASSISTANT');
                    accessPointName = getLineItemValue('ACCESS_POINT_NAME');
                    activationCode = getLineItemValue('ACTIVATION_CODE');
                    phoneNumber = getLineItemValue('PHONE_NUMBER');
                    qrCodeUrl = getLineItemValue('QR_CODE');
                    
                    // Generate QR code if not provided but LPA is available
                    if (!qrCodeUrl && lpaString) {
                        try {
                            qrCodeUrl = await generateQRCode(lpaString);
                        } catch (error) {
                            console.error('Error generating QR code:', error);
                        }
                    }
                } else {
                    // Direct properties in the response
                    iccid = completedOrder.iccid;
                    smdpAddress = completedOrder.smdpAddress;
                    lpaString = completedOrder.lpaString || completedOrder.activationCode;
                    accessPointName = completedOrder.accessPointName;
                    activationCode = completedOrder.activationCode;
                    phoneNumber = completedOrder.phoneNumber;
                    qrCodeUrl = completedOrder.qrCodeUrl;
                    
                    // Generate QR code if not provided
                    if (!qrCodeUrl && lpaString) {
                        try {
                            qrCodeUrl = await generateQRCode(lpaString);
                        } catch (error) {
                            console.error('Error generating QR code:', error);
                        }
                    }
                }
                
                // Create eSIM stock record
                const stockData = {
                    id: uuidv4(),
                    esimPlanId: plan.id,
                    iccid: iccid,
                    smdpAddress: smdpAddress,
                    lpaString: lpaString,
                    accessPointName: accessPointName,
                    activationCode: activationCode,
                    phoneNumber: phoneNumber,
                    qrCodeUrl: qrCodeUrl,
                    status: 'assigned',
                    externalStockId: completedOrder.orderId,
                    externalIccid: iccid,
                    providerStatus: completedOrder.status || 'completed',
                    providerMetadata: completedOrder,
                    walletAuthTransactionId: walletAuthTransactionId,
                    orderId: order.id,
                    orderDate: new Date()
                };
                
                // Save stock record
                const stock = await EsimStock.create(stockData, { transaction: t });
                
                // Create stock history record
                await EsimPlanStockHistory.create({
                    id: uuidv4(),
                    esimPlanId: plan.id,
                    esimStockId: stock.id,
                    iccid: iccid,
                    smdpAddress: smdpAddress,
                    lpaString: lpaString,
                    accessPointName: accessPointName,
                    activationCode: activationCode,
                    phoneNumber: phoneNumber,
                    orderId: order.id,
                    orderDate: new Date(),
                    quantity: 1,
                    status: 'assigned',
                    reason: 'API Order placement',
                    createdBy: userId
                }, { transaction: t });
                
                // Prepare provider metadata
                const providerMetadata = {
                    iccid: iccid,
                    smdpAddress: smdpAddress,
                    lpaString: lpaString,
                    accessPointName: accessPointName,
                    activationCode: activationCode,
                    phoneNumber: phoneNumber,
                    qrCodeUrl: qrCodeUrl,
                    providerStatus: completedOrder.status,
                    expiryDate: completedOrder.expiryDate
                };
                
                // Update our order record with the external order details
                await order.update({
                    status: 'completed',
                    esimStockId: stock.id,
                    externalOrderId: externalOrder.orderId,
                    providerResponse: completedOrder,
                    providerMetadata: providerMetadata,
                    expiryDate: completedOrder.expiryDate
                }, { transaction: t });
                
            } else {
                // Handle local stock order
                const stock = await EsimStock.findOne({
                    where: {
                        esimPlanId: plan.id,
                        status: 'available'
                    },
                    transaction: t,
                    lock: true
                });
                
                if (!stock) {
                    throw new Error(`No stock available for plan: ${plan.name}`);
                }
                
                // Create order record
                order = await Order.create({
                    userId,
                    esimPlanId: plan.id,
                    esimStockId: stock.id,
                    quantity: 1,
                    orderTotal,
                    startDate: startDate || null,
                    status: 'completed',
                    walletAuthTransactionId,
                    providerResponse: stock.providerMetadata || null,
                    providerMetadata: {
                        iccid: stock.iccid,
                        smdpAddress: stock.smdpAddress,
                        lpaString: stock.lpaString,
                        accessPointName: stock.accessPointName,
                        activationCode: stock.activationCode,
                        phoneNumber: stock.phoneNumber,
                        qrCodeUrl: stock.qrCodeUrl,
                        providerStatus: stock.providerStatus
                    }
                }, { transaction: t });
                
                // Update stock status to assigned
                await stock.update({
                    status: 'assigned',
                    orderId: order.id,
                    orderDate: new Date()
                }, { transaction: t });
            }
            
            // Process wallet transaction
            const newBalance = parseFloat(wallet.balance) - orderTotal;
            
            // Create wallet transaction record
            await WalletTransaction.create({
                id: uuidv4(),
                walletId: wallet.id,
                type: 'debit',
                amount: orderTotal,
                balance: newBalance,
                description: `Payment for API order: ${order.id}`,
                status: 'completed',
                referenceType: 'order',
                referenceId: order.id,
                metadata: {
                    orderId: order.id,
                    totalAmount: orderTotal,
                    api: true
                }
            }, { transaction: t });
            
            // Update wallet balance
            await wallet.update({
                balance: newBalance,
                updatedAt: new Date()
            }, { transaction: t });
            
            await t.commit();
            
            // Retrieve the completed order with relations
            const completedOrder = await Order.findByPk(order.id, {
                include: [{
                    model: EsimPlan,
                    as: 'plan',
                    include: [{
                        model: Country,
                        as: 'countries',
                        through: { attributes: [] }
                    }, {
                        model: Provider,
                        as: 'provider'
                    }]
                }, {
                    model: EsimStock,
                    as: 'stock'
                }]
            });
            
            // Determine if this is an external order from a provider like Mobimatter
            const isExternalOrder = completedOrder.plan?.provider?.type === 'API';
            
            // For external orders, extract eSIM data from the provider response
            let esimData = {};
            
            if (isExternalOrder) {
                // For Mobimatter orders, the data is in orderLineItem.lineItemDetails
                if (completedOrder.providerResponse?.orderLineItem?.lineItemDetails) {
                    const lineItems = completedOrder.providerResponse.orderLineItem.lineItemDetails;
                    
                    // Extract data from line items
                    esimData = {
                        iccid: lineItems.find(item => item.name === 'ICCID')?.value,
                        smdpAddress: lineItems.find(item => item.name === 'SMDP_ADDRESS')?.value,
                        lpaString: lineItems.find(item => item.name === 'LOCAL_PROFILE_ASSISTANT')?.value,
                        accessPointName: lineItems.find(item => item.name === 'ACCESS_POINT_NAME')?.value,
                        activationCode: lineItems.find(item => item.name === 'ACTIVATION_CODE')?.value,
                        phoneNumber: lineItems.find(item => item.name === 'PHONE_NUMBER')?.value,
                        qrCodeUrl: lineItems.find(item => item.name === 'QR_CODE')?.value || null
                    };
                } else {
                    // If it's not in the expected format, use providerMetadata
                    esimData = completedOrder.providerMetadata || {};
                }
            } else {
                // For local stock orders, use the stock data
                esimData = {
                    iccid: completedOrder.stock?.iccid,
                    smdpAddress: completedOrder.stock?.smdpAddress,
                    lpaString: completedOrder.stock?.lpaString,
                    accessPointName: completedOrder.stock?.accessPointName,
                    activationCode: completedOrder.stock?.activationCode,
                    phoneNumber: completedOrder.stock?.phoneNumber,
                    qrCodeUrl: completedOrder.stock?.qrCodeUrl
                };
            }
            
            // Format the response with eSIM data
            return res.status(201).json({
                success: true,
                data: {
                    orderId: completedOrder.id,
                    status: completedOrder.status,
                    qrCodeUrl: esimData.qrCodeUrl || completedOrder.providerMetadata?.qrCodeUrl || null,
                    iccid: esimData.iccid || completedOrder.providerMetadata?.iccid || null,
                    smdpAddress: esimData.smdpAddress || completedOrder.providerMetadata?.smdpAddress || null,
                    accessPointName: esimData.accessPointName || completedOrder.providerMetadata?.accessPointName || null,
                    lpaString: esimData.lpaString || completedOrder.providerMetadata?.lpaString || null,
                    activationCode: esimData.activationCode || completedOrder.providerMetadata?.activationCode || null,
                    expiryDate: completedOrder.expiryDate || null,
                    orderTotal: parseFloat(completedOrder.orderTotal),
                    product: {
                        productId: completedOrder.plan.productId,
                        name: completedOrder.plan.name
                    }
                }
            });
            
        } catch (error) {
            console.error('Error creating order:', error);
            if (order && order.status === 'pending') {
                // Update order to failed status if it exists but failed during processing
                await order.update({
                    status: 'failed',
                    providerErrorMessage: error.message
                }, { transaction: false }); // Outside transaction since it may have been rolled back
            }
            
            throw error; // Re-throw to be caught by outer try-catch
        }
        
    } catch (error) {
        console.error('Error in order creation:', error);
        await t.rollback();
        
        // Check for specific error types and return appropriate status codes
        if (error.message && error.message.includes('No stock available')) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'STOCK_UNAVAILABLE',
                    message: 'This product is currently out of stock. Please try again later or contact support.'
                }
            });
        }
        
        return res.status(500).json({
            success: false,
            error: {
                code: 'ORDER_CREATION_FAILED',
                message: error.message || 'Failed to create order'
            }
        });
    }
};

/**
 * Get usage information for an eSIM
 * @route GET /api/v1/usage/:orderId
 */
exports.getUsageInfo = async (req, res) => {
    try {
        // Get partner from request (added by authenticateApiKey middleware)
        const partner = req.partner;
        const userId = partner.id;
        
        // Get orderId from URL parameters
        const { orderId } = req.params;
        
        if (!orderId) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_REQUEST',
                    message: 'Order ID is required'
                }
            });
        }
        
        // Find the order with plan and provider details
        const order = await Order.findOne({
            where: { 
                id: orderId,
                userId // Ensure the order belongs to the partner
            },
            include: [{
                model: EsimPlan,
                as: 'plan',
                include: [
                    {
                        model: sequelize.models.Provider, // Use the correct Provider model
                        as: 'provider'
                    }
                ]
            }]
        });
        
        if (!order) {
            // Check if the order exists but doesn't belong to this partner
            const anyOrder = await Order.findByPk(orderId);
            if (anyOrder) {
                // Order exists but doesn't belong to this partner
                return res.status(403).json({
                    success: false,
                    error: {
                        code: 'ACCESS_DENIED',
                        message: 'You do not have access to this order'
                    }
                });
            }
            
            // Order doesn't exist at all
            return res.status(404).json({
                success: false,
                error: {
                    code: 'ORDER_NOT_FOUND',
                    message: 'Order not found'
                }
            });
        }
        
        // Check if we already have cached usage data that's less than 1 hour old
        const cachedData = order.usageData;
        const lastUsageCheck = order.lastUsageCheck;
        const now = new Date();
        
        if (cachedData && lastUsageCheck && (now - new Date(lastUsageCheck)) < 60 * 60 * 1000) {
            return res.json({
                success: true,
                data: {
                    orderId: order.id,
                    dataUsage: order.dataUsage,
                    dataAllowance: order.dataAllowance,
                    status: order.usageStatus || 'Unknown',
                    expiryDate: order.expiryDate,
                    lastUpdated: order.lastUsageCheck,
                    message: order.usageMessage || null,
                    isRealtime: (order.plan.provider?.type === 'API'),
                    fromCache: true
                }
            });
        }
        
        // Check if this is from a provider with usage capabilities (e.g., Mobimatter)
        if (!order.plan.provider || order.plan.provider.type !== 'API') {
            return res.json({
                success: true,
                data: {
                    orderId: order.id,
                    dataUsage: null,
                    dataAllowance: null,
                    status: 'Not Available',
                    expiryDate: order.expiryDate,
                    lastUpdated: null,
                    message: 'Usage data is not available for plans from this provider',
                    isRealtime: false,
                    fromCache: false
                }
            });
        }
        
        // Get the external order ID
        const externalOrderId = order.externalOrderId || order.providerResponse?.id;
        if (!externalOrderId) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'INVALID_ORDER',
                    message: 'External order ID not found'
                }
            });
        }
        
        // Get usage data from provider (e.g., Mobimatter)
        const providerService = providerFactory.getProvider(order.plan.provider.name);
        if (!providerService || typeof providerService.getUsageInfo !== 'function') {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'PROVIDER_NOT_SUPPORTED',
                    message: 'Usage data is not available for this provider'
                }
            });
        }
        
        const usageData = await providerService.getUsageInfo(externalOrderId);
        
        // Update the order with the new usage data
        await Order.update({
            usageData: usageData,
            dataUsage: usageData.dataUsage,
            dataAllowance: usageData.dataAllowance,
            usageStatus: usageData.status || 'Unknown',
            usageMessage: usageData.message,
            lastUsageCheck: now
        }, {
            where: { id: orderId }
        });
        
        return res.json({
            success: true,
            data: {
                orderId: order.id,
                dataUsage: usageData.dataUsage,
                dataAllowance: usageData.dataAllowance,
                status: usageData.status || 'Unknown',
                expiryDate: order.expiryDate || usageData.expiryDate,
                lastUpdated: now,
                message: usageData.message || null,
                isRealtime: true,
                fromCache: false
            }
        });
    } catch (error) {
        console.error('Error fetching usage info:', error);
        return res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'Error retrieving usage information'
            }
        });
    }
}; 