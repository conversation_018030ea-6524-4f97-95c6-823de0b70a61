#!/bin/bash

# Update system packages
sudo apt-get update
sudo apt-get upgrade -y

# Install Docker
sudo apt-get install -y apt-transport-https ca-certificates curl software-properties-common
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"
sudo apt-get update
sudo apt-get install -y docker-ce

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.3/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Add current user to docker group to run docker without sudo
sudo usermod -aG docker $USER

# Install Node.js and npm (for any local operations)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally for process management
sudo npm install -g pm2

# Create directory structure for the application
mkdir -p ~/esim-project/nginx/conf
mkdir -p ~/esim-project/nginx/certbot/conf
mkdir -p ~/esim-project/nginx/certbot/www

# Set up automatic SSL renewal with cron
(crontab -l 2>/dev/null; echo "0 12 * * * cd ~/esim-project && docker-compose run --rm certbot renew --quiet && docker exec nginx nginx -s reload") | crontab -

# Create a script to check and restart containers if needed
cat > ~/check-containers.sh << 'EOL'
#!/bin/bash

cd ~/esim-project

# Check if containers are running
if [ $(docker ps -q | wc -l) -lt 4 ]; then
  echo "Some containers are not running. Restarting..."
  docker-compose down
  docker-compose up -d
else
  echo "All containers are running."
fi
EOL

chmod +x ~/check-containers.sh

# Add to crontab to run every 5 minutes
(crontab -l 2>/dev/null; echo "*/5 * * * * ~/check-containers.sh >> ~/container-check.log 2>&1") | crontab -

echo "EC2 instance setup completed!"
echo "Next steps:"
echo "1. Set up GitHub repository secrets"
echo "2. Push code to the main branch to trigger deployment"
echo "3. Verify the application is running correctly"
