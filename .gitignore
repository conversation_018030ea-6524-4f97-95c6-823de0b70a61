# Environment variables
.env
.env.local
.env.*.local
.env.development
.env.test
.env.production

# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage
/test-results
/cypress/videos/
/cypress/screenshots/
test-results.json

# Production build
/build
/dist
/out

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
*.swn
*.bak
*.sublime-workspace
*.sublime-project

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# SSL/TLS certificates
*.pem
*.key
*.crt
*.cer
*.der
*.priv

# Database files
*.sqlite
*.sqlite3
*.db

# Temporary files
*.tmp
*.temp
.cache/

# API keys and secrets
*config.js
!jest.config.js
!vite.config.js
!tailwind.config.js
!postcss.config.js
!cypress.config.js
!eslint.config.js

# Sensitive documentation
private/
credentials/
secrets/

# Local development overrides
docker-compose.override.yml

# Payment processing related
stripe-webhook-secret.txt
payment-keys.json

# Backup files
*.bak
*.backup
*~

# Mobile app specific
*.keystore
*.jks
google-services.json
GoogleService-Info.plist
