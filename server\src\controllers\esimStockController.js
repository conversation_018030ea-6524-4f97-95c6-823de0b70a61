const { EsimStock, EsimPlan } = require('../models');
const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const QRCode = require('qrcode');
const { invalidatePlanCache } = require('../utils/cacheManager');

// Get all stock for a specific eSIM plan
exports.getStockByPlanId = async (req, res) => {
    try {
        const { planId } = req.params;
        
        // First check if the plan exists
        const plan = await EsimPlan.findByPk(planId);
        if (!plan) {
            return res.status(404).json({ message: 'eSIM plan not found' });
        }

        const stocks = await EsimStock.findAll({
            where: { esimPlanId: planId },
            order: [['createdAt', 'DESC']]
        });

        res.json(stocks);
    } catch (error) {
        console.error('Error getting eSIM stock:', error);
        res.status(500).json({ 
            message: 'Failed to get eSIM stock',
            error: error.message,
            stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
};

// Add new stock to an eSIM plan
exports.addStock = async (req, res) => {
    try {
        const { planId } = req.params;
        
        // First check if the plan exists
        const plan = await EsimPlan.findByPk(planId);
        if (!plan) {
            return res.status(404).json({ message: 'eSIM plan not found' });
        }

        // Validate required fields
        const requiredFields = ['lpaString', 'iccid', 'smdpAddress', 'accessPointName', 'activationCode'];
        const missingFields = requiredFields.filter(field => !req.body[field]);
        
        if (missingFields.length > 0) {
            return res.status(400).json({
                message: `Missing required fields: ${missingFields.join(', ')}`,
            });
        }

        // Generate QR code from lpaString
        const qrCodeUrl = await generateQRCode(req.body.lpaString);

        const stockData = {
            ...req.body,
            esimPlanId: planId,
            status: 'available',
            qrCodeUrl // Store the generated QR code URL
        };

        const stock = await EsimStock.create(stockData);
        invalidatePlanCache(planId);
        res.status(201).json(stock);
    } catch (error) {
        console.error('Error adding eSIM stock:', error);
        
        // Handle unique constraint violations
        if (error.name === 'SequelizeUniqueConstraintError') {
            return res.status(400).json({
                message: 'Duplicate entry',
                errors: error.errors.map(e => ({
                    field: e.path,
                    message: `${e.path} already exists`
                }))
            });
        }

        res.status(500).json({ 
            message: 'Failed to add eSIM stock',
            error: error.message,
            stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
};

// Helper function to generate QR code as data URL
const generateQRCode = async (text) => {
    try {
        return await QRCode.toDataURL(text, {
            errorCorrectionLevel: 'H',
            type: 'image/png',
            margin: 2,
            width: 400,
            color: {
                dark: '#000000',
                light: '#ffffff'
            },
            rendererOpts: {
                quality: 1
            }
        });
    } catch (err) {
        console.error('Error generating QR code:', err);
        return null;
    }
};

// Add bulk stock to an eSIM plan
exports.addBulkStock = async (req, res) => {
    try {
        const { planId } = req.params;
        const stockDataArray = req.body;

        // Check if plan exists
        const plan = await EsimPlan.findByPk(planId);
        if (!plan) {
            return res.status(404).json({ message: 'eSIM plan not found' });
        }

        // Validate required fields for each stock item
        const requiredFields = ['lpaString', 'iccid', 'smdpAddress', 'accessPointName', 'activationCode'];
        const invalidItems = stockDataArray.filter(item => 
            requiredFields.some(field => !item[field])
        );

        if (invalidItems.length > 0) {
            return res.status(400).json({
                message: 'Missing required fields in some items',
                invalidItems
            });
        }

        // Generate QR codes and prepare stock data
        const stocksWithQR = await Promise.all(stockDataArray.map(async (stock) => {
            // Generate QR code from lpaString
            const qrCodeUrl = await generateQRCode(stock.lpaString);
            
            return {
                ...stock,
                esimPlanId: planId,
                status: 'available',
                qrCodeUrl // Store the generated QR code URL
            };
        }));

        // Create all stocks in a transaction
        const createdStocks = await EsimStock.bulkCreate(stocksWithQR, {
            validate: true,
            fields: [
                'esimPlanId',
                'lpaString',
                'iccid',
                'smdpAddress',
                'accessPointName',
                'activationCode',
                'phoneNumber',
                'confCode',
                'pin',
                'walletAuthTransactionId',
                'orderId',
                'orderDate',
                'notes',
                'status',
                'qrCodeUrl' // Include qrCodeUrl in fields to be created
            ]
        });

        // Invalidate cache for this plan to ensure data consistency
        invalidatePlanCache(planId);

        res.status(201).json({
            message: `Successfully added ${createdStocks.length} stock items`,
            stocks: createdStocks
        });
    } catch (error) {
        console.error('Error adding bulk stock:', error);
        res.status(500).json({ 
            message: 'Failed to add stock items',
            error: error.message 
        });
    }
};

// Get stock details
exports.getStockDetails = async (req, res) => {
    try {
        const { id, planId } = req.params;
        const stock = await EsimStock.findOne({
            where: {
                id,
                esimPlanId: planId
            }
        });
        
        if (!stock) {
            return res.status(404).json({ message: 'Stock not found' });
        }

        res.json(stock);
    } catch (error) {
        console.error('Error getting stock details:', error);
        res.status(500).json({ 
            message: 'Failed to get stock details',
            error: error.message,
            stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
};

// Update stock details
exports.updateStock = async (req, res) => {
    try {
        const { stockId, planId } = req.params;
        const stock = await EsimStock.findOne({
            where: {
                id: stockId,
                esimPlanId: planId
            }
        });
        
        if (!stock) {
            return res.status(404).json({ message: 'Stock not found' });
        }

        // Generate new QR code if lpaString is being updated
        let updateData = { ...req.body };
        if (req.body.lpaString && req.body.lpaString !== stock.lpaString) {
            const qrCodeUrl = await generateQRCode(req.body.lpaString);
            updateData.qrCodeUrl = qrCodeUrl;
        }

        await stock.update(updateData);
        invalidatePlanCache(planId);
        res.json(stock);
    } catch (error) {
        console.error('Error updating stock:', error);
        
        // Handle unique constraint violations
        if (error.name === 'SequelizeUniqueConstraintError') {
            return res.status(400).json({
                message: 'Duplicate entry',
                errors: error.errors.map(e => ({
                    field: e.path,
                    message: `${e.path} already exists`
                }))
            });
        }

        res.status(500).json({ 
            message: 'Failed to update stock',
            error: error.message,
            stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
};

// Delete stock
exports.deleteStock = async (req, res) => {
    try {
        const { stockId, planId } = req.params;
        const stock = await EsimStock.findOne({
            where: {
                id: stockId,
                esimPlanId: planId
            }
        });

        if (!stock) {
            return res.status(404).json({ message: 'Stock not found' });
        }

        await stock.destroy();
        invalidatePlanCache(planId);
        res.json({ message: 'Stock deleted successfully' });
    } catch (error) {
        console.error('Error deleting stock:', error);
        res.status(500).json({ 
            message: 'Failed to delete stock',
            error: error.message,
            stack: process.env.NODE_ENV === 'development' ? error.stack : undefineD
        });
    }
};
