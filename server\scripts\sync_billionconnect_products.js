/**
 * <PERSON><PERSON><PERSON> to sync BillionConnect products with our database
 * This script fetches products from BillionConnect API and adds them to our database
 */

// Load environment variables
require('dotenv').config();

const { EsimPlan, Provider, Country } = require('../src/models');
const billionconnectService = require('../src/services/billionconnect.service');
const providerFactory = require('../src/services/provider.factory');
const generateProductId = require('../src/utils/generateProductId');
const { v4: uuidv4 } = require('uuid');

async function syncBillionConnectProducts() {
    console.log('🚀 Starting BillionConnect product sync...\n');

    try {
        // 1. Check if BillionConnect provider exists
        console.log('1. Checking BillionConnect provider...');
        const provider = await Provider.findOne({
            where: { name: 'billionconnect' }
        });

        if (!provider) {
            console.log('❌ BillionConnect provider not found in database');
            console.log('   Please run the seeder first: npx sequelize-cli db:seed --seed add_billionconnect_provider.js');
            return;
        }
        console.log('✅ BillionConnect provider found');

        // 2. Check API credentials
        console.log('\n2. Checking API credentials...');
        if (!billionconnectService.channelId || !billionconnectService.appSecret) {
            console.log('❌ BillionConnect API credentials not configured');
            console.log('   Please set BILLIONCONNECT_CHANNEL_ID and BILLIONCONNECT_APP_SECRET in your .env file');
            return;
        }
        console.log('✅ API credentials configured');

        // 3. Fetch products from BillionConnect
        console.log('\n3. Fetching products from BillionConnect API...');
        const products = await billionconnectService.getProducts();
        
        if (products.length === 0) {
            console.log('⚠️  No products returned from BillionConnect API');
            return;
        }
        console.log(`✅ Retrieved ${products.length} products from BillionConnect`);

        // 4. Process and standardize products
        console.log('\n4. Processing and standardizing products...');
        let addedCount = 0;
        let skippedCount = 0;
        let errorCount = 0;

        for (const product of products) {
            try {
                // Check if product already exists
                const existingPlan = await EsimPlan.findOne({
                    where: {
                        externalProductId: product.externalProductId,
                        providerId: provider.id
                    }
                });

                if (existingPlan) {
                    console.log(`   ⏭️  Skipping existing product: ${product.name}`);
                    skippedCount++;
                    continue;
                }

                // Standardize the product
                const standardizedProduct = await providerFactory.standardizeProduct('billionconnect', product);

                // Generate internal product ID
                const productId = generateProductId();

                // Create the plan in our database
                const newPlan = await EsimPlan.create({
                    id: uuidv4(),
                    productId: productId,
                    providerId: provider.id,
                    externalProductId: standardizedProduct.externalProductId,
                    externalSkuId: standardizedProduct.externalSkuId,
                    name: standardizedProduct.name,
                    description: standardizedProduct.description,
                    planInfo: standardizedProduct.planInfo,
                    additionalInfo: standardizedProduct.additionalInfo,
                    networkName: standardizedProduct.networkName,
                    networkType: standardizedProduct.networkType,
                    region: standardizedProduct.region,
                    buyingPrice: standardizedProduct.buyingPrice,
                    sellingPrice: standardizedProduct.sellingPrice,
                    validityDays: standardizedProduct.validityDays,
                    planType: standardizedProduct.planType,
                    planCategory: standardizedProduct.planCategory,
                    planData: standardizedProduct.planData,
                    planDataUnit: standardizedProduct.planDataUnit,
                    category: standardizedProduct.category,
                    status: standardizedProduct.status,
                    isActive: standardizedProduct.isActive,
                    startDateEnabled: standardizedProduct.startDateEnabled,
                    activationPolicy: standardizedProduct.activationPolicy,
                    features: standardizedProduct.features,
                    voiceMin: standardizedProduct.voiceMin,
                    voiceMinUnit: standardizedProduct.voiceMinUnit,
                    speed: standardizedProduct.speed,
                    hotspot: standardizedProduct.hotspot,
                    top_up: standardizedProduct.top_up,
                    is_sms: standardizedProduct.is_sms,
                    is_voice: standardizedProduct.is_voice,
                    stockThreshold: standardizedProduct.stockThreshold,
                    profile: standardizedProduct.profile,
                    providerMetadata: standardizedProduct.providerMetadata
                });

                // Associate with countries if available
                if (standardizedProduct.supportedCountries && standardizedProduct.supportedCountries.length > 0) {
                    const countries = await Country.findAll({
                        where: {
                            id: standardizedProduct.supportedCountries
                        }
                    });

                    if (countries.length > 0) {
                        await newPlan.setCountries(countries);
                    }
                }

                console.log(`   ✅ Added product: ${standardizedProduct.name} (${productId})`);
                addedCount++;

            } catch (error) {
                console.error(`   ❌ Error processing product ${product.name}:`, error.message);
                errorCount++;
            }
        }

        // 5. Summary
        console.log('\n📊 Sync Summary:');
        console.log(`   ✅ Products added: ${addedCount}`);
        console.log(`   ⏭️  Products skipped: ${skippedCount}`);
        console.log(`   ❌ Products with errors: ${errorCount}`);
        console.log(`   📦 Total products processed: ${products.length}`);

        if (addedCount > 0) {
            console.log('\n🎉 BillionConnect products successfully synced to database!');
            console.log('   You can now view them in the admin panel or fetch them via the API.');
        }

    } catch (error) {
        console.error('❌ Sync failed:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

// Run the sync if this file is executed directly
if (require.main === module) {
    syncBillionConnectProducts()
        .then(() => {
            console.log('\n✨ Sync process completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 Sync process failed:', error);
            process.exit(1);
        });
}

module.exports = { syncBillionConnectProducts };
