const { Model, DataTypes } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const sequelize = require('../config/database');

class EsimStock extends Model {
    static associate(models) {
        EsimStock.belongsTo(models.EsimPlan, {
            foreignKey: 'esimPlanId',
            as: 'plan',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        });

        // Add Order association
        EsimStock.hasMany(models.Order, {
            foreignKey: 'esimStockId',
            as: 'orders',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        });
    }
}

EsimStock.init({
    id: {
        type: DataTypes.STRING(36),
        defaultValue: uuidv4,
        primaryKey: true
    },
    externalStockId: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    externalIccid: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    externalProfileId: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    providerStatus: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    esimPlanId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        references: {
            model: 'esimplans',
            key: 'id'
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
    },
    lpaString: {
        type: DataTypes.STRING(255),
        allowNull: false
    },
    iccid: {
        type: DataTypes.STRING(255),
        allowNull: false,
        unique: true
    },
    smdpAddress: {
        type: DataTypes.STRING(255),
        allowNull: false
    },
    accessPointName: {
        type: DataTypes.STRING(100),
        allowNull: false
    },
    activationCode: {
        type: DataTypes.STRING(255),
        allowNull: false,
        unique: true
    },
    qrCodeUrl: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    phoneNumber: {
        type: DataTypes.STRING(100),
        allowNull: true
    },
    confCode: {
        type: DataTypes.STRING(100),
        allowNull: true
    },
    pin: {
        type: DataTypes.STRING(100),
        allowNull: true
    },
    walletAuthTransactionId: {
        type: DataTypes.STRING(100),
        allowNull: true
    },
    orderId: {
        type: DataTypes.STRING(20),
        allowNull: true,
        readOnly: true
    },
    orderDate: {
        type: DataTypes.DATE,
        defaultValue: null,
        readOnly: true
    },
    notes: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    providerMetadata: {
        type: DataTypes.JSON,
        allowNull: true
    },
    providerActivationDetails: {
        type: DataTypes.JSON,
        allowNull: true
    },
    lastProviderSync: {
        type: DataTypes.DATE,
        allowNull: true
    },
    lastUsageSync: {
        type: DataTypes.DATE,
        allowNull: true
    },
    status: {
        type: DataTypes.ENUM('available', 'assigned', 'activated', 'expired'),
        defaultValue: 'available',
        allowNull: false
    }
}, {
    sequelize,
    modelName: 'EsimStock',
    tableName: 'esimstocks',
    timestamps: true,
    underscored: false
});

module.exports = EsimStock;
