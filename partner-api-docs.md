# Partner API Documentation

## Overview

The Partner API allows authorized partners to fetch product data, place and track orders, and view usage information for eSIM products.

**API Version:** v1

## Base URLs
- Development: `http://localhost:5000/api/v1`
- Production: `https://api.vizlync.net/api/v1`

## Authentication

All API requests require authentication using the following headers:

```
Authorization: Bearer {API_KEY}
X-Partner-ID: {partner_id}
```

Where:
- `{API_KEY}` is your unique API key provided by the system administrator
- `{partner_id}` is your unique partner identifier

Failure to provide valid authentication will result in a `401 Unauthorized` response.

## Rate Limiting

The API is rate-limited to 100 requests per minute per partner. If you exceed this limit, you'll receive a `429 Too Many Requests` response.

## Response Format

All responses are returned in JSON format. A typical successful response includes:

```json
{
  "success": true,
  "data": { ... } // The response data
}
```

Error responses follow this structure:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "A description of what went wrong"
  }
}
```

## Common Error Codes

| Status Code | Error Code         | Description                                                   |
|-------------|-------------------|---------------------------------------------------------------|
| 400         | BAD_REQUEST       | The request was malformed or contained invalid parameters     |
| 401         | UNAUTHORIZED      | Authentication failed or was not provided                     |
| 403         | FORBIDDEN         | You don't have permission to access this resource             |
| 404         | NOT_FOUND         | The requested resource was not found                          |
| 429         | RATE_LIMIT_EXCEEDED | You've exceeded the rate limit                              |
| 500         | INTERNAL_SERVER_ERROR | Something went wrong on our end                           |

## API Endpoints

### List Products

Retrieve a list of available eSIM products.

```
GET /products
```

#### Query Parameters

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| region    | string | No       | Filter products by region (e.g., "Europe", "Asia") |
| country   | string | No       | Filter products by country code (e.g., "US", "GB") |
| limit     | number | No       | Limit the number of results (default: 20, max: 100) |
| page      | number | No       | Page number for pagination (default: 1) |

#### Example Request

```bash
curl -X GET "https://api.vizlync.net/api/v1/products?region=Europe&limit=10" \
  -H "Authorization: Bearer your_api_key" \
  -H "X-Partner-ID: your_partner_id"
```

<button onclick="tryRequest()">Try it</button>

#### Example Response

```json
{
  "success": true,
  "data": {
    "products": [
      {
        "productId": "ABCDEF123456",
        "name": "Europe 5GB / 30 Days",
        "description": "5GB data valid for 30 days across Europe",
        "instructions": null,
        "price": 29.99,
        "validityDays": 30,
        "countries": ["FR", "DE", "IT", "ES", "NL"],
        "region": ["Europe", "Asia"],
        "dataAmount": 5,
        "dataUnit": "GB",
        "customPlanData": "2gb 460 kb/sec",
        "voiceMin": 50,
        "voiceMinUnit": "Min",
        "speed": "Unrestricted",
        "planType": "Fixed",
        "category": "esim_realtime",
        "networkType": "4G/LTE",
        "isVoiceAvailable": true,
        "isSmsAvailable": false,
        "hotspotAvailable": true,
        "profile": "local",
        "activationPolicy": "Activation upon purchase",
        "startDateEnabled": false,
        "features": ["5G Support", "Unlimited Calls"]
      }
    ],
    "total": 25,
    "page": 1,
    "limit": 10,
    "totalPages": 3
  }
}
```

### Get Order Details

Retrieve details of a specific order.

```
GET /order/:orderId
```

#### Path Parameters

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| orderId   | string | Yes      | The unique order identifier |

#### Example Request

```bash
curl -X GET "https://api.vizlync.net/api/v1/order/VLZ123456" \
  -H "Authorization: Bearer your_api_key" \
  -H "X-Partner-ID: your_partner_id"
```

<button onclick="tryRequest()">Try it</button>

#### Example Response

```json
{
  "success": true,
  "data": {
    "orderId": "VLZ123456",
    "status": "completed",
    "product": {
      "productId": "ABCDEF123456",
      "name": "Europe 5GB / 30 Days"
    },
    "orderTotal": 29.99,
    "quantity": 1,
    "startDate": "2023-11-01",
    "expiryDate": "2023-12-01",
    "iccid": "8991000123456789012",
    "smdpAddress": "trl.prod.ondemandconnectivity.com",
    "accessPointName": "mbb",
    "lpaString": "LPA:1$trl.prod.ondemandconnectivity.com$AAA22",
    "activationCode": "LPA:1$smdp.example.com$123456789-abcdef-123456",
    "status": "completed",
    "top_up": "Available",
    "qrCodeUrl": "https://example.com/qr/VLZ123456.png",
    "createdAt": "2023-10-25T14:30:45Z"
  }
}
```

### Create Order

Place a new order for an eSIM product.

```
POST /order
```

#### Request Body

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| productId | string | Yes      | The unique product identifier |
| startDate | string | No       | The requested start date (YYYY-MM-DD). Required for products with startDateEnabled=true |
| category  | string | No       | Optional category |

#### Example Request

```bash
curl -X POST "https://api.vizlync.net/api/v1/order" \
  -H "Authorization: Bearer your_api_key" \
  -H "X-Partner-ID: your_partner_id" \
  -H "Content-Type: application/json" \
  -d '{
    "productId": "ABCDEF123456",
    "startDate": "2023-11-01",
    "category": "esim_realtime"
  }'
```

<button onclick="tryRequest()">Try it</button>

#### Example Response

```json
{
  "success": true,
  "data": {
    "orderId": "VLZ123456",
    "status": "completed",
    "qrCodeUrl": "https://example.com/qr/VLZ123456.png",
    "iccid": "8991000123456789012",
    "smdpAddress": "trl.prod.ondemandconnectivity.com",
    "accessPointName": "mbb",
    "lpaString": "LPA:1$trl.prod.ondemandconnectivity.com$AAA22",
    "activationCode": "LPA:1$smdp.example.com$123456789-abcdef-123456",
    "expiryDate": "2023-12-01",
    "orderTotal": 29.99,
    "product": {
      "productId": "ABCDEF123456",
      "name": "Europe 5GB / 30 Days"
    }
  }
}
```

### Get Usage Information

Retrieve usage information for a specific eSIM order.

```
GET /usage/:orderId
```

#### Path Parameters

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| orderId   | string | Yes      | The unique order identifier |

#### Example Request

```bash
curl -X GET "https://api.vizlync.net/api/v1/usage/VLZ123456" \
  -H "Authorization: Bearer your_api_key" \
  -H "X-Partner-ID: your_partner_id"
```

<button onclick="tryRequest()">Try it</button>

#### Example Response

```json
{
  "success": true,
  "data": {
    "orderId": "VLZ123456",
    "totalData": 5368709120,
    "usedData": 2147483648,
    "remainingData": 3221225472,
    "status": "Active",
    "activationDate": "2023-11-01T00:00:00Z",
    "expiryDate": "2023-12-01T00:00:00Z",
    "lastUpdated": "2023-11-15T09:45:22Z",
    "usageMessage": "The operator does not yet support package status check, please refer to your device settings to check your data usage"
  }
}
```

## Error Responses

### 401 Unauthorized

```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Invalid API key or partner ID"
  }
}
```

### 404 Not Found

```json
{
  
  "success": false,
  "error": {
    "code": "NOT_FOUND",
    "message": "Resource not found"
  }
}
```

### 500 Internal Server Error

```json
{
  "success": false,
  "error": {
    "code": "INTERNAL_SERVER_ERROR",
    "message": "An unexpected error occurred"
  }
}
```

## Implementation Notes

1. **Data Conversions**: Usage data is provided in bytes (totalData, usedData, remainingData).
   - 1 GB = 1,073,741,824 bytes (1024³)
   - 1 MB = 1,048,576 bytes (1024²)

2. **Authentication**: All requests require valid partner authentication using the API key.

3. **Rate Limiting**: The API is rate-limited to prevent abuse.

4. **Validation**:
   - Product IDs are 12-character alphanumeric strings
   - Order IDs follow the format `VLZ` followed by alphanumeric characters

5. **Security**:
   - Partners can only access their own orders
   - Sensitive information is omitted from API responses

<script>
function tryRequest() {
  alert('This is a documentation feature. In a real implementation, this would send a test request to the API.');
}
</script> 