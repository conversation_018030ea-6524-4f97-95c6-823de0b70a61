import http from 'k6/http';
import { sleep, check } from 'k6';
import { Counter } from 'k6/metrics';
import { CONFIG } from './config.js';
import { checkResponse, getAuthToken } from './helpers.js';

// Custom metrics
const registrationFailures = new Counter('registration_failures');
const loginFailures = new Counter('login_failures');
const planCreationFailures = new Counter('plan_creation_failures');
const connectionErrors = new Counter('connection_errors');

export const options = {
    thresholds: {
        'http_req_duration': ['p(95)<2500'],    // 95% of requests should be below 2.5s
        'login_failures': ['rate<0.4'],         // Allow up to 40% of login attempts to fail
        'registration_failures': ['rate<0.4'],   // Allow up to 40% of registration attempts to fail
        'plan_creation_failures': ['rate<0.4'],  // Allow up to 40% of plan creation attempts to fail
        'connection_errors': ['rate<0.15'],      // Allow up to 15% connection errors
    },
    scenarios: {
        load_test: {
            executor: 'ramping-vus',
            startVUs: 0,
            stages: [
                { duration: '2m', target: 50 },   // Ramp up to 50 users
                { duration: '3m', target: 100 },  // Ramp up to 100 users
                { duration: '5m', target: 100 },  // Stay at 100 users
                { duration: '2m', target: 50 },   // Ramp down to 50 users
                { duration: '1m', target: 0 }     // Ramp down to 0
            ]
        }
    }
};

export function setup() {
    return { baseUrl: CONFIG.baseUrl };
}

export default function (data) {
    const baseUrl = data.baseUrl;

    group('Load Test - Partner Registration', () => {
        try {
            // Generate unique email for registration
            const uniqueId = Math.floor(Math.random() * 1000000);
            const email = `partner${uniqueId}@test.com`;

            const registrationResponse = http.post(`${baseUrl}/api/partners`, JSON.stringify({
                name: 'Test Partner',
                email: email,
                password: 'password123',
                companyName: 'Test Company',
                address: '123 Test St',
                phoneNumber: '+1234567890'
            }), {
                headers: { 'Content-Type': 'application/json' }
            });

            if (!checkResponse(registrationResponse, 201)) {
                registrationFailures.add(1);
            }
        } catch (error) {
            console.error('Registration error:', error);
            registrationFailures.add(1);
            connectionErrors.add(1);
        }

        sleep(3);
    });

    group('Load Test - Login and 2FA', () => {
        try {
            const token = getAuthToken(http);
            if (!token) {
                loginFailures.add(1);
            }
        } catch (error) {
            console.error('Login error:', error);
            loginFailures.add(1);
            connectionErrors.add(1);
        }

        sleep(3);
    });

    group('Load Test - Create eSIM Plan', () => {
        try {
            const token = getAuthToken(http);
            if (!token) {
                planCreationFailures.add(1);
                return;
            }

            const planResponse = http.post(`${baseUrl}/api/plans`, JSON.stringify({
                name: 'Test Plan',
                description: 'Test Description',
                price: 9.99,
                dataLimit: 1000,
                validity: 30,
                features: ['5G', 'Roaming']
            }), {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!checkResponse(planResponse, 201)) {
                planCreationFailures.add(1);
            }
        } catch (error) {
            console.error('Plan creation error:', error);
            planCreationFailures.add(1);
            connectionErrors.add(1);
        }

        sleep(3);
    });
}
