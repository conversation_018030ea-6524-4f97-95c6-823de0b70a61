import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { 
    ArrowLeft, 
    Wifi, 
    Globe, 
    Signal, 
    Clock, 
    Check,
    AlertCircle,
    Info,
    MapPin,
    User,
    ArrowDownUp,
    ArrowUpToLine,
    ClipboardCheck,
    ListChecks,
    PhoneCall,
    Newspaper,
    Gauge,
    Layers,
    Calendar,
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import api from '@/lib/axios';

// Country code to name mapping
const countryCodeToName = {
    "AF": "Afghanistan", "AL": "Albania", "DZ": "Algeria", "AS": "American Samoa", "AD": "Andorra", "AO": "Angola", "AI": "Anguilla", "AQ": "Antarctica", "AG": "Antigua and Barbuda", "AR": "Argentina", "AM": "Armenia", "AW": "Aruba", "AU": "Australia", "AT": "Austria", "AZ": "Azerbaijan", "BS": "Bahamas", "BH": "Bahrain", "BD": "Bangladesh", "BB": "Barbados", "BY": "Belarus", "BE": "Belgium", "BZ": "Belize", "BJ": "Benin", "BM": "Bermuda", "BT": "Bhutan", "BO": "Bolivia", "BA": "Bosnia and Herzegovina", "BW": "Botswana", "BV": "Bouvet Island", "BR": "Brazil", "IO": "British Indian Ocean Territory", "BN": "Brunei Darussalam", "BG": "Bulgaria", "BF": "Burkina Faso", "BI": "Burundi", "KH": "Cambodia", "CM": "Cameroon", "CA": "Canada", "CV": "Cape Verde", "KY": "Cayman Islands", "CF": "Central African Republic", "TD": "Chad", "CL": "Chile", "CN": "China", "CX": "Christmas Island", "CC": "Cocos (Keeling) Islands", "CO": "Colombia", "KM": "Comoros", "CG": "Congo", "CD": "Congo, Democratic Republic of the", "CK": "Cook Islands", "CR": "Costa Rica", "CI": "Cote D'Ivoire", "HR": "Croatia", "CU": "Cuba", "CY": "Cyprus", "CZ": "Czech Republic", "DK": "Denmark", "DJ": "Djibouti", "DM": "Dominica", "DO": "Dominican Republic", "EC": "Ecuador", "EG": "Egypt", "SV": "El Salvador", "GQ": "Equatorial Guinea", "ER": "Eritrea", "EE": "Estonia", "ET": "Ethiopia", "FK": "Falkland Islands (Malvinas)", "FO": "Faroe Islands", "FJ": "Fiji", "FI": "Finland", "FR": "France", "GF": "French Guiana", "PF": "French Polynesia", "TF": "French Southern Territories", "GA": "Gabon", "GM": "Gambia", "GE": "Georgia", "DE": "Germany", "GH": "Ghana", "GI": "Gibraltar", "GR": "Greece", "GL": "Greenland", "GD": "Grenada", "GP": "Guadeloupe", "GU": "Guam", "GT": "Guatemala", "GN": "Guinea", "GW": "Guinea-Bissau", "GY": "Guyana", "HT": "Haiti", "HM": "Heard Island and Mcdonald Islands", "VA": "Holy See (Vatican City State)", "HN": "Honduras", "HK": "Hong Kong", "HU": "Hungary", "IS": "Iceland", "IN": "India", "ID": "Indonesia", "IR": "Iran, Islamic Republic Of", "IQ": "Iraq", "IE": "Ireland", "IL": "Israel", "IT": "Italy", "JM": "Jamaica", "JP": "Japan", "JO": "Jordan", "KZ": "Kazakhstan", "KE": "Kenya", "KI": "Kiribati", "KP": "Korea, Democratic People's Republic of", "KR": "Korea, Republic of", "KW": "Kuwait", "KG": "Kyrgyzstan", "LA": "Lao People's Democratic Republic", "LV": "Latvia", "LB": "Lebanon", "LS": "Lesotho", "LR": "Liberia", "LY": "Libyan Arab Jamahiriya", "LI": "Liechtenstein", "LT": "Lithuania", "LU": "Luxembourg", "MO": "Macao", "MK": "Macedonia, The Former Yugoslav Republic of", "MG": "Madagascar", "MW": "Malawi", "MY": "Malaysia", "MV": "Maldives", "ML": "Mali", "MT": "Malta", "MH": "Marshall Islands", "MQ": "Martinique", "MR": "Mauritania", "MU": "Mauritius", "YT": "Mayotte", "MX": "Mexico", "FM": "Micronesia, Federated States of", "MD": "Moldova, Republic of", "MC": "Monaco", "MN": "Mongolia", "MS": "Montserrat", "MA": "Morocco", "MZ": "Mozambique", "MM": "Myanmar", "NA": "Namibia", "NR": "Nauru", "NP": "Nepal", "NL": "Netherlands", "NC": "New Caledonia", "NZ": "New Zealand", "NI": "Nicaragua", "NE": "Niger", "NG": "Nigeria", "NU": "Niue", "NF": "Norfolk Island", "MP": "Northern Mariana Islands", "NO": "Norway", "OM": "Oman", "PK": "Pakistan", "PW": "Palau", "PS": "Palestinian Territory, Occupied", "PA": "Panama", "PG": "Papua New Guinea", "PY": "Paraguay", "PE": "Peru", "PH": "Philippines", "PN": "Pitcairn", "PL": "Poland", "PT": "Portugal", "PR": "Puerto Rico", "QA": "Qatar", "RE": "Reunion", "RO": "Romania", "RU": "Russian Federation", "RW": "Rwanda", "SH": "Saint Helena", "KN": "Saint Kitts and Nevis", "LC": "Saint Lucia", "PM": "Saint Pierre and Miquelon", "VC": "Saint Vincent and the Grenadines", "WS": "Samoa", "SM": "San Marino", "ST": "Sao Tome and Principe", "SA": "Saudi Arabia", "SN": "Senegal", "SC": "Seychelles", "SL": "Sierra Leone", "SG": "Singapore", "SK": "Slovakia", "SI": "Slovenia", "SB": "Solomon Islands", "SO": "Somalia", "ZA": "South Africa", "GS": "South Georgia and the South Sandwich Islands", "ES": "Spain", "LK": "Sri Lanka", "SD": "Sudan", "SR": "Suriname", "SJ": "Svalbard and Jan Mayen", "SZ": "Swaziland", "SE": "Sweden", "CH": "Switzerland", "SY": "Syrian Arab Republic", "TW": "Taiwan", "TJ": "Tajikistan", "TZ": "Tanzania, United Republic of", "TH": "Thailand", "TL": "Timor-Leste", "TG": "Togo", "TK": "Tokelau", "TO": "Tonga", "TT": "Trinidad and Tobago", "TN": "Tunisia", "TR": "Turkey", "TM": "Turkmenistan", "TC": "Turks and Caicos Islands", "TV": "Tuvalu", "UG": "Uganda", "UA": "Ukraine", "AE": "United Arab Emirates", "GB": "United Kingdom", "US": "United States", "UM": "United States Minor Outlying Islands", "UY": "Uruguay", "UZ": "Uzbekistan", "VU": "Vanuatu", "VE": "Venezuela", "VN": "Viet Nam", "VG": "Virgin Islands, British", "VI": "Virgin Islands, U.S.", "WF": "Wallis and Futuna", "EH": "Western Sahara", "YE": "Yemen", "ZM": "Zambia", "ZW": "Zimbabwe"
};

// Region code to name mapping
const regionNameMap = {
    'APAC': 'Asia Pacific',
    'EMEA': 'Europe, Middle East & Africa',
    'EU': 'Europe',
    'NAM': 'North America',
    'LATAM': 'Latin America',
    'Global': 'Global',
    'Asia': 'Asia',
    'Europe': 'Europe',
    'Africa': 'Africa',
    'Americas': 'Americas',
    'Oceania': 'Oceania',
    'Caribbean': 'Caribbean'
};

const getRegionName = (region) => {
    if (!region) return 'Global';
    const trimmedRegion = region.trim();
    return regionNameMap[trimmedRegion] || trimmedRegion;
};

export default function ViewEsimPlan() {
    const { id } = useParams();
    const navigate = useNavigate();
    const { toast } = useToast();
    const [plan, setPlan] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        fetchPlanDetails();
    }, [id]);

    const fetchPlanDetails = async () => {
        try {
            const response = await api.get(`/api/esim-plans/${id}`);
            setPlan(response.data);
        } catch (error) {
            console.error('Error fetching plan details:', error);
            toast({
                variant: 'destructive',
                title: 'Error',
                description: error.response?.data?.message || 'Failed to fetch plan details',
            });
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (!plan) {
        return (
            <div className="p-8">
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Plan Not Found</AlertTitle>
                    <AlertDescription>
                        The requested eSIM plan could not be found. Please check the URL and try again.
                    </AlertDescription>
                </Alert>
                <Button onClick={() => navigate('/admin/esim-plans')} className="mt-6">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Plans
                </Button>
            </div>
        );
    }

    const getStatusColor = (status) => {
        return status === 'visible' ? 'bg-green-50 text-green-700' : 'bg-gray-50 text-gray-700';
    };

    const getCountryName = (countryCode) => {
        // Convert to uppercase since our mapping uses uppercase codes
        const code = countryCode.toUpperCase();
        // Return the full name if found, otherwise return the code
        return countryCodeToName[code] || countryCode;
    };

    const formatRegions = (regions) => {
        if (!regions) return 'Global';
        
        if (typeof regions === 'string') {
            // Handle comma-separated string of regions
            return regions.split(',')
                .map(r => r.trim())
                .map(getRegionName)
                .filter(Boolean)
                .join(', ') || 'Global';
        }
        
        if (Array.isArray(regions)) {
            // Handle array of regions
            return regions.map(getRegionName).filter(Boolean).join(', ') || 'Global';
        }
        
        return 'Global';
    };

    return (
        <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="container mx-auto p-6 max-w-6xl"
        >
            <Button variant="outline" onClick={() => navigate(-1)}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back 
            </Button>
            <div className="mb-8 flex items-center justify-between bg-gradient-to-r from-blue-800 to-blue-600 p-6 rounded-t-lg">
                <div>
                <h1 className="text-3xl font-bold tracking-tight text-white">eSIM Plan Details</h1>
                <span className="text-muted-foreground mt-2 text-white">Manage and view detailed information about this eSIM plan</span>
                </div>
                <div className="space-x-4">
                        <Button onClick={() => navigate(`/admin/esim-plans/${id}/stock`)}
                        disabled={plan.provider?.type === 'API'}
                        className={`bg-purple-500 text-white ${plan.provider?.type === 'API' ? 'cursor-not-allowed opacity-50' : ''
                        }`}
                         title={plan.provider?.type === 'API' ? 'Cannot view stock for External plans' : 'View Stock'}
                            >
                            Manage Stock
                        </Button>
                    </div>
            </div>

            <motion.div 
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
            >
                <Card className="mb-6 shadow-lg hover:shadow-xl transition-shadow duration-300 ">
                    <CardHeader className="border-b bg-gradient-to-r from-blue-50 to-purple-50">
                        <div className="flex items-start justify-between">
                            <div>
                                <CardTitle className="text-3xl font-bold">{plan.name}</CardTitle>
                            </div>
                            <Badge className={`${getStatusColor(plan.status)} px-4 py-1 text-sm font-medium`}>
                                {plan.status}
                            </Badge>
                        </div>
                    </CardHeader>
                    <CardContent className="p-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div className="space-y-6">
                                <div className="bg-blue-50 p-6 rounded-lg hover:bg-blue-100 transition-colors duration-300">
                                    <h3 className="font-semibold text-xl mb-4 text-blue-900">Plan Information</h3>
                                    <div className="space-y-4">
                                        <div className="flex items-center gap-3 bg-white p-3 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                            <Globe className="w-6 h-6 text-blue-600" />
                                            <div>
                                                <p className="font-medium text-gray-900">Network Coverage</p>
                                                <p className="text-sm text-gray-600">{plan.networkName}</p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-3 bg-white p-3 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                            <Wifi className="w-6 h-6 text-blue-600" />
                                            <div>
                                                <p className="font-medium text-gray-900">Data Allowance</p>
                                                <p className="text-sm text-gray-600">
                                                    {plan.planType === 'Unlimited' ? (
                                                        'Unlimited Data'
                                                    ) : (
                                                        `${plan.planData} ${plan.planDataUnit}`
                                                    )}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-3 bg-white p-3 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                            <Clock className="w-6 h-6 text-blue-600" />
                                            <div>
                                                <p className="font-medium text-gray-900">Validity Period</p>
                                                <p className="text-sm text-gray-600">{plan.validityDays} Day{plan.validityDays > 1 ? 's' : ''}</p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-3 bg-white p-3 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                            <Signal className="w-6 h-6 text-blue-600" />
                                            <div>
                                                <p className="font-medium text-gray-900">Network Type</p>
                                                <p className="text-sm text-gray-600">{plan.networkType || '4G/5G'}</p>
                                            </div>
                                        </div>
                                        <div className='flex items-center gap-3 bg-white p-3 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300'>
                                            <User className='w-6 h-6 text-blue-600' />
                                            <div className="flex flex-col">
                                                <p className="font-medium text-gray-900">Provider</p>
                                                <p className="text-sm text-gray-600">{plan.provider?.name || 'No provider assigned'}</p>
                                            </div>
                                        </div>
                                        <div className='flex items-center gap-3 bg-white p-3 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300'>
                                            <Calendar className='w-6 h-6 text-blue-600' />
                                            <div className="flex flex-col">
                                                <p className="font-medium text-gray-900">Start Date</p>
                                                <p className="text-sm text-gray-600">{plan.startDateEnabled ? 'Enabled' : 'Disabled'}</p>
                                            </div>
                                        </div>
                                        <div className="flex items-start gap-3 bg-white p-4 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                            < ArrowUpToLine className="w-6 h-6 text-blue-600" />
                                            <div>
                                            <p className="font-medium text-gray-900">Top Up</p>
                                            <p className="text-sm text-gray-600">{plan.top_up}</p>
                                            </div>
                                        </div>
                                        <div className="flex items-start gap-3 bg-white p-4 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                            <Wifi className="w-6 h-6 text-blue-600" />
                                            <div>
                                            <p className="font-medium text-gray-900">Hotspot</p>
                                            <p className="text-sm text-gray-600">{plan.hotspot}</p>
                                            </div>
                                        </div>
                                        <div className="flex items-start gap-3 bg-white p-3 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                            <Globe className="w-6 h-6 text-blue-600" />
                                            <div>
                                                <p className="font-medium text-gray-900">Regions</p>
                                                <div className="flex flex-wrap gap-1 mt-1">
                                                    {plan.region ? (
                                                        typeof plan.region === 'string' ? (
                                                            plan.region.split(',').map((region, index) => (
                                                                <span key={`${region}-${index}`} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                                                                    {getRegionName(region)}
                                                                </span>
                                                            ))
                                                        ) : Array.isArray(plan.region) ? (
                                                            plan.region.map((region, index) => (
                                                                <span key={`${region}-${index}`} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                                                                    {getRegionName(region)}
                                                                </span>
                                                            ))
                                                        ) : (
                                                            <p className="text-sm text-gray-600">Global</p>
                                                        )
                                                    ) : (
                                                        <p className="text-sm text-gray-600">Global</p>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex items-start gap-3 bg-blue-50 p-3 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                            <MapPin className="w-6 h-6 text-blue-600" />
                                            <div>
                                                <p className="font-medium text-gray-900">Country Coverage</p>
                                                <div className="grid grid-cols-3 gap-3 mt-1">
                                                    {(plan.provider?.name === 'Mobimatter' 
                                                        ? (plan.providerMetadata?.originalData?.supportedCountries || []).map(countryCode => ({
                                                            id: countryCode.toLowerCase(),
                                                            name: getCountryName(countryCode),
                                                            flagUrl: `https://flagcdn.com/w20/${countryCode.toLowerCase()}.png`
                                                        }))
                                                        : plan.countries
                                                    ).map((country, index) => (
                                                        <div key={`${country.id}-${index}`} className="flex items-center gap-2">
                                                            <img
                                                                src={country.flagUrl || `https://flagcdn.com/w20/${country.id.toLowerCase()}.png`}
                                                                alt={`${country.name} flag`}
                                                                className="w-4 h-3 object-cover rounded-sm"
                                                                title={country.name}
                                                                onError={(e) => {
                                                                    e.target.onerror = null;
                                                                    e.target.src = 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/No_flag.svg/32px-No_flag.svg.png';
                                                                }}
                                                            />
                                                            <p className="text-sm text-gray-600">{country.name}</p>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="space-y-6">
                                <div className="bg-blue-50 p-6 rounded-lg hover:bg-blue-100 transition-colors duration-300">
                                    <h3 className="font-semibold text-xl mb-4 text-blue-900">Plan Informations</h3>
                                    <div className="space-y-4">
                                        <div className="flex items-start gap-3 bg-white p-4 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                        <ArrowDownUp className="w-6 h-6 text-blue-600" />
                                            <div>
                                            <p className="font-medium text-gray-900">Profile Type</p>
                                            <p className="text-sm text-gray-600">
                                                {plan.profile}
                                            </p>
                                            </div>
                                        </div>  
                                        <div className="flex items-start gap-3 bg-white p-4 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                        <ClipboardCheck className="w-6 h-6 text-blue-600" />
                                            <div>
                                            <p className="font-medium text-gray-900">Plan Type</p>
                                            <p className="text-sm text-gray-600">
                                                {plan.planType}
                                            </p>
                                            </div>
                                        </div>  
                                        <div className="flex items-start gap-3 bg-white p-4 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                        <ListChecks className="w-6 h-6 text-blue-600" />
                                            <div>
                                            <p className="font-medium text-gray-900">Plan Category</p>
                                            <p className="text-sm text-gray-600">
                                                {plan.planCategory}
                                            </p>
                                            </div>
                                        </div> 
                                        <div className="flex items-start gap-3 bg-white p-4 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                        <PhoneCall className="w-6 h-6 text-blue-600" />
                                            <div>
                                            <p className="font-medium text-gray-900">Voice Allowance</p>
                                            <p className="text-sm text-gray-600">
                                                {plan.voiceMin || 'N/A'} {plan.voiceMinUnit || ''}
                                            </p>
                                            </div>
                                        </div> 
                                        <div className="flex items-start gap-3 bg-white p-4 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                        <Layers className="w-6 h-6 text-blue-600" />
                                            <div>
                                            <p className="font-medium text-gray-900">Stock Threshold</p>
                                            <p className="text-sm text-gray-600">
                                                {plan.stockThreshold || 'N/A'}
                                            </p>
                                            </div>
                                        </div> 
                                        <div className="flex items-start gap-3 bg-white p-4 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                        <Newspaper className="w-6 h-6 text-blue-600" />
                                            <div>
                                            <p className="font-medium text-gray-900">Activation Policy</p>
                                            <p className="text-sm text-gray-600">
                                                {plan.activationPolicy || 'N/A'}
                                            </p>
                                            </div>
                                        </div> 
                                        <div className="flex items-start gap-3 bg-white p-4 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                        <Gauge className="w-6 h-6 text-blue-600" />
                                            <div>
                                            <p className="font-medium text-gray-900">Speed</p>
                                            <p className="text-sm text-gray-600">
                                                {plan.speed || 'N/A'}
                                            </p>
                                         </div>
                                        </div> 
                                                
                                    </div>
                                </div>
                                <div className="bg-green-50 p-6 rounded-lg hover:bg-green-100 transition-colors duration-300">
                                    <h3 className="font-semibold text-xl mb-4 text-green-900">Pricing Details</h3>
                                    <div className="space-y-4">
                                        <div className=" bg-white p-4 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                            <p className="font-medium text-gray-600">Buying Price</p>
                                            <p className="text-3xl font-bold text-green-600">${plan.buyingPrice}</p>
                                        </div>
                                        <div className="bg-white p-4 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                            <p className="font-medium text-gray-600">Selling Price</p>
                                            <p className="text-3xl font-bold text-green-600">
                                                {plan.sellingPrice ? `$${plan.sellingPrice}` : 'Not Set'}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                        </div>

                        {plan.features && plan.features.length > 0 && (
                            <div className="mt-8 bg-purple-50 p-6 rounded-lg hover:bg-purple-100 transition-colors duration-300">
                                <h3 className="font-semibold text-xl mb-4 text-purple-900">Features</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    {plan.features.map((feature, index) => (
                                        <div key={index} className="flex items-start gap-2 bg-white p-3 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                            <Check className="w-5 h-5 text-purple-600 mt-0.5" />
                                            <span className="text-gray-700">{feature}</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                        
                        <div className="mt-8 bg-amber-50 p-6 rounded-lg hover:bg-amber-100 transition-colors duration-300">
                            <div className="flex items-center gap-2 mb-4">
                                <Info className="w-6 h-6 text-amber-600" />
                                <h3 className="font-semibold text-xl text-amber-900">Plan Description</h3>
                            </div>
                            <div className="bg-white p-4 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                <div 
                                    className="prose prose-sm max-w-none"
                                    dangerouslySetInnerHTML={{ __html: plan.description || 'No description available' }}
                                />
                            </div>
                        </div>

                        <div className="mt-8 bg-amber-50 p-6 rounded-lg hover:bg-amber-100 transition-colors duration-300">
                            <div className="flex items-center gap-2 mb-4">
                                <Info className="w-6 h-6 text-amber-600" />
                                <h3 className="font-semibold text-xl text-amber-900">Instructions</h3>
                            </div>
                            <div className="bg-white p-4 rounded-md shadow-sm hover:shadow-md transition-shadow duration-300">
                                <div 
                                    className="prose prose-sm max-w-none"   
                                    dangerouslySetInnerHTML={{ __html: plan.instructions || 'No instructions available' }}
                                />  
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </motion.div>

            <div className="mt-8 space-y-4">
                <div className="flex justify-between items-center">
                    <Button onClick={() => navigate(-1)}>
                        Back to Plans
                    </Button>
                    <div className="space-x-4">
                        <Button onClick={() => navigate(`/admin/esim-plans/${id}/stock`)} disabled={plan.provider?.type === 'API'} className="bg-blue-700 text-white">
                            Manage Stock
                        </Button>
                    </div>
                </div>
            </div>
        </motion.div>
    );
}