require('dotenv').config();
const { Provider } = require('../models');
const sequelize = require('../config/database');

async function updateMobimatterProvider() {
    try {
        await sequelize.authenticate();
        console.log('Database connection established.');

        const provider = await Provider.findOne({
            where: {
                name: 'Mobimatter'
            }
        });

        if (!provider) {
            console.log('Mobimatter provider not found');
            return;
        }

        await provider.update({
            apiKey: process.env.MOBIMATTER_API_KEY,
            merchantId: process.env.MOBIMATTER_MERCHANT_ID,
            apiEndpoint: process.env.MOBIMATTER_API_URL || 'https://api.mobimatter.com/mobimatter/api/v2'
        });

        console.log('Mobimatter provider updated successfully');

    } catch (error) {
        console.error('Error updating provider:', error);
    } finally {
        await sequelize.close();
    }
}

updateMobimatterProvider(); 