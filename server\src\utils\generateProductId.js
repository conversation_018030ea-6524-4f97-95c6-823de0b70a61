const generateProductId = () => {
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const digits = '0123456789';
    
    // Generate 6 random letters
    let productId = '';
    for (let i = 0; i < 6; i++) {
        productId += letters.charAt(Math.floor(Math.random() * letters.length));
    }
    
    // Generate 6 random digits
    for (let i = 0; i < 6; i++) {
        productId += digits.charAt(Math.floor(Math.random() * digits.length));
    }
    
    return productId;
};

module.exports = generateProductId;
