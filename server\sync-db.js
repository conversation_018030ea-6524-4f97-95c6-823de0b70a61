// require('dotenv').config();
// const sequelize = require('./src/config/database');
// const { DataTypes } = require('sequelize');

// async function syncDatabase() {
//     try {
//         await sequelize.authenticate();
//         console.log('Database connection has been established successfully.');
        
//         // Disable foreign key checks
//         await sequelize.query('SET FOREIGN_KEY_CHECKS = 0');
        
//         // Drop all existing tables
//         await sequelize.query('DROP TABLE IF EXISTS OTPs');
//         await sequelize.query('DROP TABLE IF EXISTS Users');
//         await sequelize.query('DROP TABLE IF EXISTS Countries');
        
//         // Create Countries table
//         await sequelize.query(`
//             CREATE TABLE Countries (
//                 id CHAR(2) PRIMARY KEY,
//                 iso3 CHAR(3) NOT NULL,
//                 name VARCHAR(100) NOT NULL,
//                 phoneCode VARCHAR(10),
//                 currencyCode CHAR(3),
//                 currencySymbol VARCHAR(5),
//                 region VARCHAR(50),
//                 isActive BOOLEAN DEFAULT true,
//                 createdAt DATETIME NOT NULL,
//                 updatedAt DATETIME NOT NULL,
//                 UNIQUE KEY idx_country_iso3 (iso3),
//                 INDEX idx_country_search (name, region, isActive)
//             ) ENGINE=InnoDB;
//         `);
        
//         // Create Users table
//         await sequelize.query(`
//             CREATE TABLE Users (
//                 id CHAR(36) PRIMARY KEY,
//                 email VARCHAR(255) NOT NULL,
//                 alternateEmail VARCHAR(255),
//                 password VARCHAR(255) NOT NULL,
//                 role ENUM('admin', 'partner') NOT NULL DEFAULT 'partner',
//                 firstName VARCHAR(100) NOT NULL,
//                 lastName VARCHAR(100) NOT NULL,
//                 countryId CHAR(2),
//                 phoneNumber VARCHAR(20),
//                 alternatePhoneNumber VARCHAR(20),
//                 businessName VARCHAR(200),
//                 businessEmail VARCHAR(255),
//                 billingAddressLine1 VARCHAR(255),
//                 billingAddressLine2 VARCHAR(255),
//                 billingCity VARCHAR(100),
//                 billingProvince VARCHAR(100),
//                 billingCountryId CHAR(2),
//                 billingPostalCode VARCHAR(20),
//                 isActive BOOLEAN DEFAULT true,
//                 lastLogin DATETIME,
//                 loginAttempts TINYINT UNSIGNED DEFAULT 0,
//                 lockUntil DATETIME,
//                 createdAt DATETIME NOT NULL,
//                 updatedAt DATETIME NOT NULL,
//                 UNIQUE KEY unique_email (email),
//                 UNIQUE KEY unique_business_email (businessEmail),
//                 FOREIGN KEY (countryId) REFERENCES Countries(id) ON DELETE SET NULL ON UPDATE CASCADE,
//                 FOREIGN KEY (billingCountryId) REFERENCES Countries(id) ON DELETE SET NULL ON UPDATE CASCADE
//             ) ENGINE=InnoDB;
//         `);
        
//         // Create OTPs table
//         await sequelize.query(`
//             CREATE TABLE OTPs (
//                 id CHAR(36) PRIMARY KEY,
//                 userId CHAR(36) NOT NULL,
//                 code CHAR(6) NOT NULL,
//                 expiresAt DATETIME NOT NULL,
//                 isUsed BOOLEAN DEFAULT false,
//                 createdAt DATETIME NOT NULL,
//                 updatedAt DATETIME NOT NULL,
//                 INDEX idx_user_expires (userId, expiresAt, isUsed),
//                 INDEX idx_cleanup (expiresAt, isUsed),
//                 FOREIGN KEY (userId) REFERENCES Users(id) ON DELETE CASCADE ON UPDATE CASCADE
//             ) ENGINE=InnoDB;
//         `);
        
//         // Re-enable foreign key checks
//         await sequelize.query('SET FOREIGN_KEY_CHECKS = 1');
        
//         console.log('Database synced successfully');
//         process.exit(0);
//     } catch (error) {
//         console.error('Error:', error);
//         process.exit(1);
//     }
// }

// syncDatabase(); 