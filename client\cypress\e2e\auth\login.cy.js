describe('Login Flow', () => {
  beforeEach(() => {
    cy.visit('/auth/login');
  });

  it('should display login form with proper fields', () => {
    // Check header
    cy.contains('h1', 'eSIM Platform').should('be.visible');
    cy.contains('Enterprise eSIM Management Solution').should('be.visible');

    // Check form
    cy.get('form').within(() => {
      cy.get('input[type="email"]').should('have.attr', 'placeholder', '<EMAIL>');
      cy.get('input[type="password"]').should('have.attr', 'placeholder', '••••••••');
      cy.get('button[type="submit"]').contains('Sign in').should('exist');
    });
    cy.get('a').contains('Forgot your password?').should('be.visible');
  });

  it('should validate email format', () => {
    cy.get('input[type="email"]').type('invalid-email');
    cy.get('input[type="password"]').type('password123');
    cy.get('button[type="submit"]').click();
    cy.contains('Please enter a valid email').should('be.visible');
  });

  it('should validate password length', () => {
    cy.get('input[type="email"]').type('<EMAIL>');
    cy.get('input[type="password"]').type('12345');
    cy.get('button[type="submit"]').click();
    cy.contains('Password must be at least 6 characters').should('be.visible');
  });

  it('should show error for invalid credentials', () => {
    cy.intercept('POST', '/api/auth/login', {
      statusCode: 401,
      body: { message: 'Invalid credentials' }
    }).as('loginError');

    cy.get('input[type="email"]').type('<EMAIL>');
    cy.get('input[type="password"]').type('wrongpassword');
    cy.get('button[type="submit"]').click();

    cy.wait('@loginError');
    cy.contains('Login failed').should('be.visible');
  });

  describe('OTP Flow', () => {
    beforeEach(() => {
      // Intercept initial login
      cy.intercept('POST', '/api/auth/login', {
        statusCode: 200,
        body: { tempToken: 'temp-token-123' }
      }).as('login');

      // Fill and submit login form
      cy.get('input[type="email"]').type('<EMAIL>');
      cy.get('input[type="password"]').type('password123');
      cy.get('button[type="submit"]').click();
    });

    it('should show OTP step after successful login', () => {
      cy.wait('@login');
      cy.contains('h2', 'Verification Required').should('be.visible');
      cy.contains('Click Send OTP to receive a verification code').should('be.visible');
      cy.get('button').contains('Send OTP').should('be.visible');
    });

    it('should handle OTP sending', () => {
      cy.wait('@login');
      
      // Intercept OTP send request
      cy.intercept('POST', '/api/auth/send-otp', {
        statusCode: 200,
        body: { message: 'OTP sent successfully' }
      }).as('sendOTP');

      cy.get('button').contains('Send OTP').click();
      cy.wait('@sendOTP');
      cy.get('input[type="text"]').should('be.visible');
    });

    it('should verify OTP and redirect', () => {
      cy.wait('@login');
      
      // Intercept OTP send request
      cy.intercept('POST', '/api/auth/send-otp', {
        statusCode: 200,
        body: { message: 'OTP sent successfully' }
      }).as('sendOTP');

      // Intercept OTP verification
      cy.intercept('POST', '/api/auth/verify-otp', {
        statusCode: 200,
        body: {
          token: 'valid-token-123',
          user: { role: 'partner' }
        }
      }).as('verifyOTP');

      cy.get('button').contains('Send OTP').click();
      cy.wait('@sendOTP');
      
      cy.get('input[type="text"]').type('123456');
      cy.get('button[type="submit"]').click();
      cy.wait('@verifyOTP');

      // Should redirect to dashboard
      cy.url().should('include', '/dashboard');
    });

    it('should handle OTP verification errors', () => {
      cy.wait('@login');
      
      // Intercept OTP send request
      cy.intercept('POST', '/api/auth/send-otp', {
        statusCode: 200,
        body: { message: 'OTP sent successfully' }
      }).as('sendOTP');

      // Intercept OTP verification with error
      cy.intercept('POST', '/api/auth/verify-otp', {
        statusCode: 400,
        body: { message: 'Invalid OTP' }
      }).as('verifyOTPError');

      cy.get('button').contains('Send OTP').click();
      cy.wait('@sendOTP');
      
      cy.get('input[type="text"]').type('123456');
      cy.get('button[type="submit"]').click();
      cy.wait('@verifyOTPError');

      cy.contains('Invalid OTP').should('be.visible');
    });
  });

  describe('Forgot Password Flow', () => {
    it('should open forgot password dialog', () => {
      cy.get('a').contains('Forgot your password?').click();
      cy.get('[role="dialog"]').should('be.visible');
      cy.get('input[type="email"]').should('be.visible');
    });

    it('should handle forgot password request', () => {
      cy.intercept('POST', '/api/auth/forgot-password', {
        statusCode: 200,
        body: { message: 'Password reset instructions sent' }
      }).as('forgotPassword');

      cy.get('a').contains('Forgot your password?').click();
      cy.get('input[type="email"]').type('<EMAIL>');
      cy.get('[role="dialog"]').within(() => {
        cy.get('button').contains('Send Reset Link').click();
      });

      cy.wait('@forgotPassword');
      cy.contains('Password reset instructions have been sent to your email').should('be.visible');
      cy.get('[role="dialog"]').should('not.exist');
    });

    it('should handle forgot password errors', () => {
      cy.intercept('POST', '/api/auth/forgot-password', {
        statusCode: 400,
        body: { message: 'Email not found' }
      }).as('forgotPasswordError');

      cy.get('a').contains('Forgot your password?').click();
      cy.get('input[type="email"]').type('<EMAIL>');
      cy.get('[role="dialog"]').within(() => {
        cy.get('button').contains('Send Reset Link').click();
      });

      cy.wait('@forgotPasswordError');
      cy.contains('Email not found').should('be.visible');
    });
  });
});
