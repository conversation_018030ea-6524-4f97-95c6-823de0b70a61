#!/bin/bash

# This script initializes SSL certificates for your domain

# Check if domain name is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <domain-name> <email>"
  echo "Example: $0 api.yourdomain.com <EMAIL>"
  exit 1
fi

# Check if email is provided
if [ -z "$2" ]; then
  echo "Usage: $0 <domain-name> <email>"
  echo "Example: $0 api.yourdomain.com <EMAIL>"
  exit 1
fi

DOMAIN=$1
EMAIL=$2

# Create required directories
mkdir -p nginx/certbot/conf
mkdir -p nginx/certbot/www

# Create a temporary nginx config for SSL initialization
cat > nginx/conf/app.conf << EOL
server {
    listen 80;
    server_name ${DOMAIN};
    
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    location / {
        return 200 'SSL setup in progress. Please wait...';
        add_header Content-Type text/plain;
    }
}
EOL

# Start nginx container
docker-compose up -d nginx

# Wait for nginx to start
echo "Waiting for nginx to start..."
sleep 5

# Request SSL certificate
echo "Requesting SSL certificate for ${DOMAIN}..."
docker-compose run --rm certbot certonly --webroot --webroot-path=/var/www/certbot \
  --email ${EMAIL} --agree-tos --no-eff-email \
  -d ${DOMAIN}

# Check if certificate was issued successfully
if [ ! -d "nginx/certbot/conf/live/${DOMAIN}" ]; then
  echo "Failed to obtain SSL certificate. Please check the logs."
  exit 1
fi

# Create Diffie-Hellman parameters for enhanced security
echo "Generating Diffie-Hellman parameters (this may take a while)..."
openssl dhparam -out nginx/certbot/conf/ssl-dhparams.pem 2048

# Create options-ssl-nginx.conf if it doesn't exist
if [ ! -f "nginx/certbot/conf/options-ssl-nginx.conf" ]; then
  cat > nginx/certbot/conf/options-ssl-nginx.conf << EOL
ssl_session_cache shared:le_nginx_SSL:10m;
ssl_session_timeout 1440m;
ssl_session_tickets off;

ssl_protocols TLSv1.2 TLSv1.3;
ssl_prefer_server_ciphers off;

ssl_ciphers "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384";
EOL
fi

# Update nginx configuration with SSL settings
cat > nginx/conf/app.conf << EOL
server {
    listen 80;
    server_name ${DOMAIN};
    
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    location / {
        return 301 https://\$host\$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name ${DOMAIN};
    
    ssl_certificate /etc/letsencrypt/live/${DOMAIN}/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/${DOMAIN}/privkey.pem;
    
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";
    
    # Proxy to Node.js application
    location / {
        proxy_pass http://app:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    # Serve static files directly
    location /docs {
        proxy_pass http://app:3000/docs;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # Enable caching for static files
        expires 1d;
        add_header Cache-Control "public, max-age=86400";
    }
    
    # Limit request size
    client_max_body_size 10M;
    
    # Enable gzip compression
    gzip on;
    gzip_comp_level 5;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
    gzip_types
        application/javascript
        application/json
        application/xml
        text/css
        text/plain
        text/xml;
}
EOL

# Reload nginx to apply new configuration
docker exec nginx nginx -s reload

echo "SSL setup completed successfully!"
echo "Your application is now accessible at https://${DOMAIN}"
