FROM nginx:1.21-alpine

# Remove default Nginx configuration
RUN rm /etc/nginx/conf.d/default.conf

# Create a custom configuration
RUN echo 'server { \
    listen 80; \
    server_name localhost api.vizlync.net; \
    \
    # For Let'"'"'s Encrypt HTTP-01 challenge \
    location ^~ /.well-known/acme-challenge/ { \
        root /var/www/certbot; \
        try_files $uri =404; \
        \
        # Add debug headers \
        add_header X-Debug-Info "Serving from ACME challenge handler" always; \
        add_header Content-Type text/plain; \
    } \
    \
    # Forward all other requests to the app \
    location / { \
        proxy_pass http://app:3000; \
        proxy_http_version 1.1; \
        proxy_set_header Host $host; \
        proxy_set_header X-Real-IP $remote_addr; \
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; \
        proxy_set_header X-Forwarded-Proto $scheme; \
    } \
}' > /etc/nginx/conf.d/app.conf

# Create ACME challenge directory
RUN mkdir -p /var/www/certbot/.well-known/acme-challenge/

# Create a test file for ACME challenge verification
RUN echo "This is a test file for ACME challenge verification" > /var/www/certbot/.well-known/acme-challenge/test-file

# Set proper permissions
RUN chmod -R 755 /var/www/certbot

# Expose ports
EXPOSE 80 443

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]
