.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker__input-container input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #1a202c;
  background-color: #fff;
}

.react-datepicker__input-container input:focus {
  outline: none;
  border-color: #3182ce;
  box-shadow: 0 0 0 1px #3182ce;
}

.react-datepicker {
  font-family: inherit;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.react-datepicker__header {
  background-color: #f7fafc;
  border-bottom: 1px solid #e2e8f0;
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
  padding-top: 8px;
}

.react-datepicker__day--selected {
  background-color: #3182ce;
  color: white;
}

.react-datepicker__day--selected:hover {
  background-color: #2c5282;
}

.react-datepicker__day:hover {
  background-color: #edf2f7;
}

.react-datepicker__day--disabled {
  color: #cbd5e0;
}

.react-datepicker__navigation {
  top: 8px;
}

.react-datepicker__day-name {
  color: #4a5568;
  margin: 0.166rem;
  padding: 0.166rem;
  width: 1.7rem;
}
