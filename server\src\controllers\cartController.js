const Cart = require('../models/Cart');
const EsimPlan = require('../models/EsimPlan');
const Country = require('../models/Country');
const Provider = require('../models/Provider');
const User = require('../models/User');

// Add item to cart
exports.addToCart = async (req, res) => {
    try {
        const { esimPlanId } = req.body;
        const userId = req.user.id;

        // Delete any existing items in the cart for this user
        await Cart.destroy({ where: { userId } });

        // Add the new item
        const cartItem = await Cart.create({
            userId,
            esimPlanId,
            quantity: 1
        });

        res.status(201).json({
            message: 'Item added to cart successfully',
            cartItem
        });
    } catch (error) {
        console.error('Error adding item to cart:', error);
        res.status(500).json({ message: 'Failed to add item to cart' });
    }
};

// Get cart items
exports.getCart = async (req, res) => {
    try {
        const userId = req.user.id;

        // Get partner for markup calculation
        const partner = await User.findByPk(userId);
        if (!partner) {
            return res.status(404).json({ message: 'Partner not found' });
        }

        const cartItems = await Cart.findAll({
            where: { userId },
            include: [{
                model: EsimPlan,
                include: [{
                    model: Country,
                    as: 'countries',
                    through: { attributes: ['isDefault'] }
                }, {
                    model: Provider,
                    as: 'provider',
                    attributes: ['id', 'name', 'type', 'country']
                }]
            }]
        });

        // Apply markup-based pricing for plans without a specific selling price
        const processedCartItems = cartItems.map(item => {
            const cartItem = item.toJSON();
            
            // If selling price is not set and partner has a markup percentage,
            // calculate the price based on markup
            if (!cartItem.EsimPlan.sellingPrice && partner.markupPercentage) {
                const markup = 1 + (partner.markupPercentage / 100);
                cartItem.EsimPlan.sellingPrice = Number((cartItem.EsimPlan.buyingPrice * markup).toFixed(2));
            }

            return cartItem;
        });

        res.json({ cartItems: processedCartItems });
    } catch (error) {
        console.error('Error fetching cart:', error);
        res.status(500).json({ message: 'Failed to fetch cart' });
    }
};

// Remove item from cart
exports.removeFromCart = async (req, res) => {
    try {
        const userId = req.user.id;
        await Cart.destroy({ where: { userId } });
        res.json({ message: 'Cart cleared successfully' });
    } catch (error) {
        console.error('Error removing item from cart:', error);
        res.status(500).json({ message: 'Failed to remove item from cart' });
    }
};

// Get cart count
exports.getCartCount = async (req, res) => {
    try {
        const userId = req.user.id;
        const count = await Cart.count({ where: { userId } });
        res.json({ count });
    } catch (error) {
        console.error('Error getting cart count:', error);
        res.status(500).json({ message: 'Failed to get cart count' });
    }
};

// Update activation policy for cart item
exports.updateActivationPolicy = async (req, res) => {
    try {
        const userId = req.user.id;
        const { itemId } = req.params;
        const { activationPolicy } = req.body;

        // Find the cart item
        const cartItem = await Cart.findOne({
            where: { 
                id: itemId,
                userId 
            },
            include: [{
                model: EsimPlan
            }]
        });

        if (!cartItem) {
            return res.status(404).json({ message: 'Cart item not found' });
        }

        // Update the activation policy for the eSIM plan
        await EsimPlan.update(
            { activationPolicy },
            { where: { id: cartItem.esimPlanId } }
        );

        res.json({ 
            message: 'Activation policy updated successfully',
            activationPolicy 
        });
    } catch (error) {
        console.error('Error updating activation policy:', error);
        res.status(500).json({ message: 'Failed to update activation policy' });
    }
};
