const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../middleware/auth');
const models = require('../models');
const { Op } = require('sequelize');

router.get('/stats', isAuthenticated, async (req, res) => {
    try {
        const userId = req.user.id;

        // Get wallet balance
        const wallet = await models.Wallet.findOne({
            where: { userId }
        });

        // Get total orders
        const totalOrders = await models.Order.count({
            where: { userId }
        });

        // Get total available eSIM plans
        const totalPlans = await models.EsimPlan.count({
            where: { 
                status: 'visible',
                isActive: true
            }
        });

        // Get orders in the last 30 days
        const last30DaysOrders = await models.Order.count({
            where: {
                userId,
                createdAt: {
                    [Op.gte]: new Date(new Date() - 30 * 24 * 60 * 60 * 1000)
                }
            }
        });

        // Get all order trends by month
        const allMonthlyOrders = await models.Order.findAll({
            where: { userId },
            attributes: [
                [models.sequelize.fn('DATE_FORMAT', models.sequelize.col('createdAt'), '%Y-%m-01'), 'month'],
                [models.sequelize.fn('COUNT', '*'), 'count']
            ],
            group: [models.sequelize.fn('DATE_FORMAT', models.sequelize.col('createdAt'), '%Y-%m-01')],
            order: [[models.sequelize.fn('DATE_FORMAT', models.sequelize.col('createdAt'), '%Y-%m-01'), 'ASC']]
        });

        // Fill in missing months with zero counts
        const monthlyTrends = [];
        if (allMonthlyOrders.length > 0) {
            const firstOrder = allMonthlyOrders[0];
            const lastOrder = allMonthlyOrders[allMonthlyOrders.length - 1];
            const firstMonth = new Date(firstOrder.getDataValue('month'));
            const lastMonth = new Date(lastOrder.getDataValue('month'));

            for (let date = firstMonth; date <= lastMonth; date.setMonth(date.getMonth() + 1)) {
                const monthKey = date.toISOString().slice(0, 7) + '-01';
                const existingData = allMonthlyOrders.find(
                    order => order.getDataValue('month') === monthKey
                );
                monthlyTrends.push({
                    month: monthKey,
                    count: existingData ? parseInt(existingData.getDataValue('count')) : 0
                });
            }
        }

        // Get recent orders
        const recentOrders = await models.Order.findAll({
            attributes: ['id', 'quantity', 'orderTotal', 'status', 'createdAt'],
            where: { userId },
            include: [{
                model: models.EsimPlan,
                as: 'plan',
                attributes: ['name', 'planData', 'planDataUnit', 'validityDays', 'planType','customPlanData','planCategory']
            }],
            order: [['createdAt', 'DESC']],
            limit: 5
        });

        res.json({
            walletBalance: wallet ? parseFloat(wallet.balance) : 0,
            totalOrders,
            totalPlans,
            last30DaysOrders,
            monthlyTrends,
            recentOrders: recentOrders.map(order => ({
                id: order.id,
                planName: order.plan.name,
                planDetails: `${order.plan.planData} ${order.plan.planDataUnit} / ${order.plan.validityDays} Days`,
                quantity: order.quantity || 1,
                amount: parseFloat(order.orderTotal),
                status: order.status,
                planType: order.plan.planType,
                customPlanData: order.plan.customPlanData,
                planData: order.plan.planData,
                planDataUnit: order.plan.planDataUnit,
                validityDays: order.plan.validityDays,
                planCategory: order.plan.planCategory,
                createdAt: order.createdAt
            }))
        });
    } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        res.status(500).json({ message: 'Failed to fetch dashboard statistics' });
    }
});

module.exports = router;
