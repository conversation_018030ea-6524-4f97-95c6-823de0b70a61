import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { useToast } from '@/components/ui/use-toast';
import { Switch } from '@/components/ui/switch';
import { ArrowLeft } from 'lucide-react';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { MultiSelect } from '@/components/ui/multi-select';
import api from '@/lib/axios';
import { Loader2 } from 'lucide-react';
import { cn, formatPlanData } from "@/lib/utils";

// Form validation schema
const formSchema = z.object({
  name: z.string().min(1, 'Plan name is required'),
  description: z.string().optional(),
  instructions: z.string().optional(),
  providerId: z.string().min(1, 'Provider is required'),
  networkName: z.string().min(1, 'Network name is required'),
  region: z.string().optional(),
  buyingPrice: z.union([
    z.string().min(1, 'Buying price is required'),
    z.number()
  ]).transform((val) => Number(val)).refine((val) => val > 0, {
    message: 'Buying price must be greater than 0'
  }),
  validityDays: z.union([
    z.string().min(1, 'Validity days is required'),
    z.number()
  ]).transform((val) => Number(val)).refine((val) => val >= 1 && Number.isInteger(val), {
    message: 'Validity days must be at least 1'
  }),
  planType: z.enum(['Fixed', 'Unlimited', 'Custom']).default('Fixed'),
  category: z.enum(['esim_realtime', 'esim_addon', 'esim_replacement']).default('esim_realtime'),
  is_voice: z.enum(['Available', 'Not Available']).default('Not Available'),
  planCategory: z.enum(['Voice and Data', 'Data Only']).default('Data Only'),
  voiceMin: z.union([
    z.string(),
    z.number(),
    z.null()
  ]).transform((val) => val === '' ? null : Number(val))
    .refine(
      (val) => true, // Always pass the first refinement
      { message: 'Voice minutes is required for Voice and Data plans' }
    )
    .optional()
    .nullable(),
  voiceMinUnit: z.enum(['Min', 'Hr', 'Sec']).optional().nullable(),
  planData: z.union([
    z.string(),
    z.number(),
    z.null()
  ]).transform((val) => val === '' ? null : Number(val))
    .refine((val) => val === null || val > 0, {
      message: 'Plan data must be greater than 0'
    })
    .optional()
    .nullable(),
  planDataUnit: z.enum(['MB', 'GB', 'TB']).optional().nullable(),
  customPlanData: z.string().optional(),
  is_sms: z.enum(['Available', 'Not Available']).default('Not Available'),
  sms: z.union([
    z.string(),
    z.number(),
    z.null()
  ]).transform((val) => val === '' ? null : Number(val))
    .optional()
    .nullable(),
  profile: z.enum(['Local', 'Roaming']).default('Local'),
  status: z.enum(['visible', 'hidden']).default('visible'),
  countries: z.array(z.string()).min(1, 'At least one country is required'),
  startDateEnabled: z.boolean().default(false),
  hotspot: z.enum(['Available', 'Not Available']).default('Available'),
  top_up: z.enum(['Available', 'Not Available']).default('Not Available'),
  networkType: z.string().default('4G/LTE'),
  activationPolicy: z.enum([
    'Activation upon purchase',
    'Activation upon first usage',
    'Activation upon travel date'
  ]).default('Activation upon purchase'),
  speed: z.enum(['Restricted', 'Unrestricted']).default('Unrestricted'),
  stockThreshold: z.number().default(10).refine((val) => val >= 1 && val <= 1000, {
    message: 'Stock threshold must be between 1 and 1000'
  }),
}).superRefine((data, ctx) => {
  // Add custom validation for Voice and Data plans
  if (data.planCategory === 'Voice and Data') {
    if (!data.voiceMin || data.voiceMin <= 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Voice minutes must be greater than 0 for Voice and Data plans',
        path: ['voiceMin']
      });
    }
  }
});



export default function EsimPlanForm() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { id } = useParams(); 
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [countries, setCountries] = useState([]);
  const [selectedCountries, setSelectedCountries] = useState([]);
  const [providers, setProviders] = useState([]);

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      description: '',
      instructions: '',
      providerId: '',
      networkName: '',
      networkType: '4G/LTE',
      region: '',
      buyingPrice: '',
      validityDays: '',
      planType: 'Fixed',
      category: 'esim_realtime',
      planCategory: 'Data Only',
      is_voice: 'Not Available',
      voiceMin: '',
      voiceMinUnit: 'Min',
      planData: '',
      planDataUnit: 'GB',
      customPlanData: '',
      is_sms: 'Not Available',
      sms: '',
      profile: 'Local',
      status: 'visible',
      countries: [],
      startDateEnabled: false,
      hotspot: 'Available',
      top_up: 'Not Available',
      activationPolicy: 'Activation upon purchase',
      speed: 'Unrestricted',
      stockThreshold: 10
    },
    mode: 'onChange'
  });

  // Fetch countries, providers and plan data if editing
  useEffect(() => {
    const fetchData = async () => {
      try {
        const countriesRes = await api.get('/api/countries');
        setCountries(countriesRes.data);

        const providersRes = await api.get('/api/providers');
        setProviders(providersRes.data.filter(p => p.status === 'active'));

        if (id) {
          const response = await api.get(`/api/esim-plans/${id}`);
          const plan = response.data;
          // console.log('Fetched plan data:', plan); // Add this for debugging
          if (plan) {
            setSelectedCountries(plan.countries?.map(c => c.id) || []);
            form.reset({
              name: plan.name,
              description: plan.description || '',
              instructions: plan.instructions || '',
              providerId: plan.providerId,
              networkName: plan.networkName,
              networkType: plan.networkType || '4G/LTE',
              region: Array.isArray(plan.region) ? plan.region.join(', ') : plan.region || '',
              buyingPrice: plan.buyingPrice.toString(),
              validityDays: plan.validityDays.toString(),
              planData: plan.planData?.toString() || '',
              planType: plan.planType,
              category: plan.category,
              planCategory: plan.planCategory || 'Data Only',
              voiceMin: plan.voiceMin || '',
              voiceMinUnit: plan.voiceMinUnit || 'Min',
              planDataUnit: plan.planDataUnit || 'GB',
              customPlanData: plan.customPlanData || '',
              profile: plan.esimType || 'Local',
              status: plan.status,
              countries: plan.countries?.map(c => c.id) || [],
              startDateEnabled: Boolean(plan.startDateEnabled),
              hotspot: plan.hotspot || 'Available',
              top_up: plan.top_up || 'Not Available',
              activationPolicy: plan.activationPolicy || 'Activation upon purchase',
              speed: plan.speed || 'Unrestricted',
              is_voice: plan.is_voice || 'Not Available',
              is_sms: plan.is_sms || 'Not Available',
              sms: plan.sms || '',
              stockThreshold: plan.stockThreshold || 10
            });
          }
        }
      } catch (error) {
        // console.error('Error fetching data:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Failed to load form data',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id, form, toast]);

  useEffect(() => {
    const startDateEnabled = form.watch('startDateEnabled');
    if (startDateEnabled) {
      form.setValue('activationPolicy', 'Activation upon travel date');
    }
  }, [form.watch('startDateEnabled')]);

  const onSubmit = async (data) => {
    try {
      setSubmitting(true);
      // console.log('Form data being submitted:', data); 

      // Validate that countries are selected
      if (!selectedCountries.length) {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Please select at least one country',
        });
        return;
      }

      const formData = {
        ...data,
        countries: selectedCountries,
        defaultCountryId: selectedCountries[0],
        buyingPrice: Number(data.buyingPrice),
        validityDays: Number(data.validityDays),
        planData: data.planType === 'Unlimited' ? null : Number(data.planData),
        planDataUnit: data.planType === 'Unlimited' ? null : data.planDataUnit,
        region: data.region ? data.region.split(',').map(r => r.trim()).filter(Boolean) : [],
        startDateEnabled: Boolean(data.startDateEnabled),
        is_voice: data.planCategory === 'Voice and Data' ? 'Available' : data.is_voice,
        is_sms: data.is_sms || 'Not Available',
        sms: data.is_sms === 'Available' ? Number(data.sms) : null,
      };

      // console.log('Processed form data:', formData); 

      if (id) {
        await api.put(`/api/esim-plans/${id}`, formData);
        toast({
          title: 'Success',
          description: 'eSIM plan updated successfully',
        });
      } else {
        await api.post('/api/esim-plans', formData);
        toast({
          title: 'Success',   
          description: 'eSIM plan created successfully',
        });
      }

      navigate('/admin/esim-plans');
    } catch (error) {
      // console.error('Error submitting form:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to save eSIM plan',
      });
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="p-6">
      <Card>
        <CardHeader>
        <div className="flex items-center gap-4">
                <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => navigate('/admin/esim-plans')}
                >
                    <ArrowLeft className="h-4 w-4" />
                </Button>
          <CardTitle>{id ? 'Edit eSIM Plan' : 'Create New eSIM Plan'}</CardTitle>
        </div>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Plan Name</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="providerId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Provider</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select provider" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {providers.map((provider) => (
                            <SelectItem key={provider.id} value={provider.id}>
                              {provider.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="networkName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Network Name</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="networkType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Network Type</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="region"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Regions (comma-separated)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g. Asia, Europe, North America"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="countries"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Countries</FormLabel>
                      <FormControl>
                        <MultiSelect
                          value={selectedCountries}
                          onChange={(newValue) => {
                            setSelectedCountries(newValue);
                            field.onChange(newValue);
                          }}
                          options={countries.map(country => ({
                            label: country.name,
                            value: country.id
                          }))}
                          placeholder="Select countries..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="buyingPrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Buying Price ($)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          {...field}
                          value={field.value}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="validityDays"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Validity (Days)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          {...field}
                          value={field.value}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="planType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Plan Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select plan type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Fixed">Fixed Data</SelectItem>
                          <SelectItem value="Unlimited">Unlimited Data</SelectItem>
                          <SelectItem value="Custom">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="planCategory"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Plan Category</FormLabel>
                      <Select
                        value={form.watch('planCategory')}
                        onValueChange={(value) => {
                          form.setValue('planCategory', value);
                          if (value === 'Data Only') {
                            form.setValue('voiceMin', '');
                          }
                        }}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select plan category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Data Only">Data Only</SelectItem>
                          <SelectItem value="Voice and Data">Voice and Data</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {form.watch('planCategory') === 'Voice and Data' && (
                  <>
                    <FormField
                      control={form.control}
                      name="is_voice"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Voice</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue="Available">
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select voice availability" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="Available">Available</SelectItem>
                              <SelectItem value="Not Available">Not Available</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="is_sms"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>SMS</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select SMS availability" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="Available">Available</SelectItem>
                              <SelectItem value="Not Available">Not Available</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {form.watch('is_sms') === 'Available' && (
                      <FormField
                        control={form.control}
                        name="sms"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>SMS Count</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                {...field}
                                placeholder="Enter SMS count"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    <FormField
                      control={form.control}
                      name="voiceMin"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Voice Minutes</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              {...field}
                              placeholder="Enter voice minutes"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}  
                    />
                    <FormField
                      control={form.control}
                      name="voiceMinUnit"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Voice Unit</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select data unit" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="Min">Min</SelectItem>
                              <SelectItem value="Sec">Sec</SelectItem>
                              <SelectItem value="Hr">Hr</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}
                
                

                {form.watch('planType') === 'Fixed' && (
                  <>
                    <FormField
                      control={form.control}
                      name="planData"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Data Amount</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="0.1"
                              {...field} 
                              value={formatPlanData(field.value)}
                              onChange={(e) => {
                                const value = e.target.value;
                                field.onChange(value === '' ? '' : Number(value));
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="planDataUnit"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Data Unit</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select data unit" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="MB">MB</SelectItem>
                              <SelectItem value="GB">GB</SelectItem>
                              <SelectItem value="TB">TB</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                {form.watch('planType') === 'Custom' && (
                  <FormField
                    control={form.control}
                    name="customPlanData"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Custom Data Plan</FormLabel>
                        <FormControl>
                          <Input 
                            {...field} 
                            placeholder="e.g., 1 GB/day, 456 KB/s"
                          />
                        </FormControl>
                        <FormDescription>
                          Enter the custom data plan format (e.g., '1 GB/day', '456 KB/s', '2 GB + 512 KB/s')
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="esim_realtime">eSIM Plan</SelectItem>
                          <SelectItem value="esim_addon">Top-up Plan</SelectItem>
                          <SelectItem value="esim_replacement">Replacement eSIM</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Choose where this plan should be displayed
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="profile"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>eSIM Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select eSIM type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Local">Local</SelectItem>
                          <SelectItem value="Roaming">Roaming</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="visible">Visible</SelectItem>
                          <SelectItem value="hidden">Hidden</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />


                <FormField
                  control={form.control}
                  name="hotspot"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hotspot</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select hotspot" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Available">Available</SelectItem>
                          <SelectItem value="Not Available">Not Available</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

<FormField
                  control={form.control}
                  name="top_up"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Top-up</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select Top-up" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Available">Available</SelectItem>
                          <SelectItem value="Not Available">Not Available</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="activationPolicy"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Activation Policy</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select activation policy" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Activation upon purchase">Activation upon purchase</SelectItem>
                          <SelectItem value="Activation upon first usage">Activation upon first usage</SelectItem>
                          <SelectItem value="Activation upon travel date">Activation upon travel date</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="speed"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Speed</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select speed" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Restricted">Restricted</SelectItem>
                          <SelectItem value="Unrestricted">Unrestricted</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="stockThreshold"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Stock Threshold</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="1"
                          min="1"
                          max="1000"
                          {...field}
                          value={field.value ?? ""}
                          onChange={(e) => {
                            const value = e.target.value ? Number(e.target.value) : "";
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />


                

                <FormField
                  control={form.control}
                  name="startDateEnabled"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Start Date</FormLabel>
                        <FormDescription>
                          Enable if the plan should start from a specific date chosen by the user
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem className="col-span-2">
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <RichTextEditor
                          value={field.value || ''}
                          onChange={field.onChange}
                          placeholder="Enter plan description...

Example format:
• High-speed data connectivity
• Coverage in multiple countries
• 24/7 customer support

Features:
1. Instant activation
2. Easy QR code setup
3. Compatible with all eSIM devices"
                        />
                      </FormControl>
                      <FormDescription>
                        Use the toolbar for formatting. You can create lists, add headings, and more.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="instructions"
                  render={({ field }) => (
                    <FormItem className="col-span-2">
                      <FormLabel>Instructions</FormLabel>
                      <FormControl>
                        <RichTextEditor
                          value={field.value || ''}
                          onChange={field.onChange}
                          placeholder="Enter plan instructions...

Example format:
• Instant activation
• Easy QR code setup
• Compatible with all eSIM devices"

                        />
                      </FormControl>
                      <FormDescription>
                        Use the toolbar for formatting. You can create lists, add headings, and more.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
              </div>
              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate('/admin/esim-plans')}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={submitting}>
                  {submitting ? (
                    <>Loading...</>
                  ) : id ? (
                    'Update Plan'
                  ) : (
                    'Create Plan'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
