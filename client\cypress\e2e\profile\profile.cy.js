describe('Profile Management', () => {
  beforeEach(() => {
    cy.createTestPartner().then((partner) => {
      cy.login(partner.email, partner.password);
      cy.visit('/partner/profile');
    });
  });

  it('should display profile information', () => {
    cy.get('[data-testid="profile-form"]').should('exist');
    cy.get('input[name="firstName"]').should('not.be.empty');
    cy.get('input[name="lastName"]').should('not.be.empty');
    cy.get('input[name="companyName"]').should('not.be.empty');
    cy.get('input[name="phone"]').should('not.be.empty');
  });

  it('should update profile information', () => {
    const updates = {
      firstName: 'Updated',
      lastName: 'Name',
      companyName: 'Updated Company',
      phone: '9876543210'
    };

    Object.entries(updates).forEach(([field, value]) => {
      cy.get(`input[name="${field}"]`).clear().type(value);
    });

    cy.get('[data-testid="save-profile"]').click();
    cy.contains('Profile updated successfully').should('be.visible');

    // Verify persistence
    cy.reload();
    Object.entries(updates).forEach(([field, value]) => {
      cy.get(`input[name="${field}"]`).should('have.value', value);
    });
  });

  it('should change password', () => {
    const newPassword = 'NewPassword123!@#';

    cy.get('[data-testid="change-password-button"]').click();
    cy.get('input[name="currentPassword"]').type('Test123!@#');
    cy.get('input[name="newPassword"]').type(newPassword);
    cy.get('input[name="confirmPassword"]').type(newPassword);
    cy.get('[data-testid="save-password"]').click();

    cy.contains('Password updated successfully').should('be.visible');

    // Verify new password works
    cy.logout();
    cy.visit('/login');
    cy.get('input[type="email"]').type(Cypress.env('testPartnerEmail'));
    cy.get('input[type="password"]').type(newPassword);
    cy.get('button[type="submit"]').click();
    cy.url().should('include', '/partner/dashboard');
  });

  it('should validate required fields', () => {
    ['firstName', 'lastName', 'companyName', 'phone'].forEach((field) => {
      cy.get(`input[name="${field}"]`).clear();
    });

    cy.get('[data-testid="save-profile"]').click();
    cy.contains('This field is required').should('be.visible');
  });

  it('should validate phone number format', () => {
    cy.get('input[name="phone"]').clear().type('invalid');
    cy.get('[data-testid="save-profile"]').click();
    cy.contains('Invalid phone number').should('be.visible');
  });

  it('should handle API errors gracefully', () => {
    cy.intercept('PUT', '**/api/profile', {
      statusCode: 500,
      body: { message: 'Internal Server Error' }
    }).as('updateProfileError');

    cy.get('input[name="firstName"]').clear().type('Test');
    cy.get('[data-testid="save-profile"]').click();
    cy.contains('Error updating profile').should('be.visible');
  });
});
