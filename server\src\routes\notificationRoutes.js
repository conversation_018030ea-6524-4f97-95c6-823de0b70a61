const express = require('express');
const router = express.Router();
const { sendLowBalanceEmail, sendWalletCreditEmail } = require('../utils/emailService');

// Endpoint for sending low balance notifications
router.post('/low-balance', async (req, res) => {
    try {
        const { email, currentBalance, threshold } = req.body;

        if (!email || !currentBalance || !threshold) {
            return res.status(400).json({
                message: 'Missing required fields: email, currentBalance, threshold'
            });
        }

        // Validate balance values
        if (typeof currentBalance !== 'number' || typeof threshold !== 'number') {
            return res.status(400).json({
                message: 'currentBalance and threshold must be numbers'
            });
        }

        // Send low balance notification
        await sendLowBalanceEmail(email, currentBalance, threshold);

        res.status(200).json({
            message: 'Low balance notification sent successfully'
        });

    } catch (error) {
        console.error('Error sending low balance notification:', error);
        res.status(500).json({
            message: 'Failed to send low balance notification',
            error: error.message
        });
    }
});

// Endpoint for sending wallet credit notifications
router.post('/wallet-credit', async (req, res) => {
    try {
        const { email, amount, newBalance } = req.body;

        if (!email || !amount || !newBalance) {
            return res.status(400).json({
                message: 'Missing required fields: email, amount, newBalance'
            });
        }

        // Validate amount and balance values
        if (typeof amount !== 'number' || typeof newBalance !== 'number') {
            return res.status(400).json({
                message: 'amount and newBalance must be numbers'
            });
        }

        // Send wallet credit notification
        await sendWalletCreditEmail(email, amount, newBalance);

        res.status(200).json({
            message: 'Wallet credit notification sent successfully'
        });

    } catch (error) {
        console.error('Error sending wallet credit notification:', error);
        res.status(500).json({
            message: 'Failed to send wallet credit notification',
            error: error.message
        });
    }
});

module.exports = router;
