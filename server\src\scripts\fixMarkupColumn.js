const sequelize = require('../config/database');

async function fixMarkupColumn() {
    const transaction = await sequelize.transaction();
    
    try {
        // First update NULL values to 0
        await sequelize.query(`
            UPDATE Users 
            SET markupPercentage = 0 
            WHERE markupPercentage IS NULL;
        `, { transaction });
        console.log('Updated NULL values to 0');

        // Then drop the existing column
        await sequelize.query(`
            ALTER TABLE Users 
            DROP COLUMN markupPercentage;
        `, { transaction });
        console.log('Dropped existing column');

        // Add the column back with NOT NULL constraint
        await sequelize.query(`
            ALTER TABLE Users 
            ADD COLUMN markupPercentage DECIMAL(5,2) NOT NULL DEFAULT 0;
        `, { transaction });
        console.log('Added column back with NOT NULL constraint');

        await transaction.commit();
        console.log('All changes committed successfully');
        process.exit(0);
    } catch (error) {
        await transaction.rollback();
        console.error('Error:', error);
        process.exit(1);
    }
}

fixMarkupColumn();
