'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Modify planData and planDataUnit constraints
    await queryInterface.changeColumn('esimplans', 'planData', {
      type: Sequelize.DECIMAL(10),
      allowNull: true
    });

    await queryInterface.changeColumn('esimplans', 'planDataUnit', {
      type: Sequelize.ENUM('MB', 'GB', 'TB'),
      allowNull: true
    });

    // Add category 'esim_replacement' to the category ENUM if it doesn't exist
    await queryInterface.sequelize.query(`
      ALTER TABLE esimplans 
      MODIFY COLUMN category ENUM('esim_realtime', 'esim_addon', 'esim_replacement') 
      NOT NULL 
      DEFAULT 'esim_realtime'
    `);
  },

  down: async (queryInterface, Sequelize) => {
    // Revert planData and planDataUnit constraints
    await queryInterface.changeColumn('esimplans', 'planData', {
      type: Sequelize.DECIMAL(10),
      allowNull: false
    });

    await queryInterface.changeColumn('esimplans', 'planDataUnit', {
      type: Sequelize.ENUM('MB', 'GB', 'TB'),
      allowNull: false
    });

    // Remove 'esim_replacement' from category ENUM
    await queryInterface.sequelize.query(`
      ALTER TABLE esimplans 
      MODIFY COLUMN category ENUM('esim_realtime', 'esim_addon') 
      NOT NULL 
      DEFAULT 'esim_realtime'
    `);
  }
}; 