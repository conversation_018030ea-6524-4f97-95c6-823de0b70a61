const Country = require('../models/Country');

const countries = [
    {
        id: 'US',
        iso3: 'USA',
        name: 'United States',
        flagEmoji: '🇺🇸',
        flagUrl: 'https://flagcdn.com/us.svg',
        phoneCode: '1',
        currencyCode: 'USD',
        currencySymbol: '$'
    },
    {
        id: 'GB',
        iso3: 'GBR',
        name: 'United Kingdom',
        flagEmoji: '🇬🇧',
        flagUrl: 'https://flagcdn.com/gb.svg',
        phoneCode: '44',
        currencyCode: 'GBP',
        currencySymbol: '£'
    },
    {
        id: 'IN',
        iso3: 'IND',
        name: 'India',
        flagEmoji: '🇮🇳',
        flagUrl: 'https://flagcdn.com/in.svg',
        phoneCode: '91',
        currencyCode: 'INR',
        currencySymbol: '₹'
    },
    // Add more countries as needed
];

const seedCountries = async () => {
    try {
        // Delete existing records
        await Country.destroy({ truncate: true });

        // Insert new records
        await Country.bulkCreate(countries);
        console.log('Countries seeded successfully');
    } catch (error) {
        console.error('Error seeding countries:', error);
    }
};

module.exports = seedCountries;
