-- Insert data into EsimPlans
INSERT INTO EsimPlans (id, productId, name, description, providerName, networkName, countryId, region, buyingPrice, validityDays, planData, planDataUnit, threshold, status, sellingPrice, totalStocks, stocksLeft, isActive)
VALUES
(UUID(), NULL, 'Basic Plan', '5GB data valid for 30 days', 'Provider A', 'Network X', 'US', 'North America', 5.00, 30, 5.00, 'GB', 10, 'visible', 10.00, 100, 100, TRUE),
(UUID(), NULL, 'Standard Plan', '10GB data valid for 60 days', 'Provider B', 'Network Y', 'GB', 'Europe', 8.00, 60, 10.00, 'GB', 10, 'visible', 15.00, 50, 50, TRUE),
(UUID(), NULL, 'Premium Plan', 'Unlimited data for 90 days', 'Provider C', 'Network Z', 'IN', 'Asia', 15.00, 90, 1000.00, 'GB', 5, 'visible', 30.00, 20, 20, TRUE);

-- Insert data into EsimPlanStockHistory
INSERT INTO EsimPlanStockHistory (id, esimPlanId, quantity, type, reason, previousStock, newStock, createdBy)
VALUES
(UUID(), (SELECT id FROM EsimPlans WHERE name = 'Basic Plan'), 100, 'initial', 'Initial stock added', 0, 100, UUID()),
(UUID(), (SELECT id FROM EsimPlans WHERE name = 'Standard Plan'), 50, 'initial', 'Initial stock added', 0, 50, UUID()),
(UUID(), (SELECT id FROM EsimPlans WHERE name = 'Premium Plan'), 20, 'initial', 'Initial stock added', 0, 20, UUID());