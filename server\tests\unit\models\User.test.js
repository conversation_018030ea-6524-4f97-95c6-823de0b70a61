const { User } = require('../../../src/models');
const sequelize = require('../../config/database');
const bcrypt = require('bcrypt');

describe('User Model Tests', () => {
    beforeAll(async () => {
        await sequelize.authenticate();
        await sequelize.sync({ force: true });
    });

    afterAll(async () => {
        await sequelize.close();
    });

    beforeEach(async () => {
        await User.destroy({ where: {}, force: true });
    });

    describe('User Creation', () => {
        it('should create a user with valid data', async () => {
            const userData = {
                email: '<EMAIL>',
                password: 'password123',
                role: 'partner',
                firstName: 'John',
                lastName: 'Doe',
                companyName: 'Test Company',
                phone: '1234567890',
                isActive: true
            };

            const user = await User.create(userData);
            expect(user).toBeTruthy();
            expect(user.email).toBe(userData.email);
            expect(user.role).toBe(userData.role);
            expect(user.isActive).toBe(userData.isActive);
        });

        it('should hash password before saving', async () => {
            const password = 'password123';
            const user = await User.create({
                email: '<EMAIL>',
                password,
                role: 'partner'
            });

            expect(user.password).not.toBe(password);
            const isMatch = await bcrypt.compare(password, user.password);
            expect(isMatch).toBe(true);
        });

        it('should not allow duplicate emails', async () => {
            await User.create({
                email: '<EMAIL>',
                password: 'password123',
                role: 'partner'
            });

            await expect(User.create({
                email: '<EMAIL>',
                password: 'password456',
                role: 'partner'
            })).rejects.toThrow();
        });

        it('should validate email format', async () => {
            await expect(User.create({
                email: 'invalid-email',
                password: 'password123',
                role: 'partner'
            })).rejects.toThrow();
        });

        it('should require password', async () => {
            await expect(User.create({
                email: '<EMAIL>',
                role: 'partner'
            })).rejects.toThrow();
        });

        it('should validate role', async () => {
            await expect(User.create({
                email: '<EMAIL>',
                password: 'password123',
                role: 'invalid-role'
            })).rejects.toThrow();
        });
    });

    describe('User Methods', () => {
        it('should verify correct password', async () => {
            const user = await User.create({
                email: '<EMAIL>',
                password: 'password123',
                role: 'partner'
            });

            const isValid = await user.verifyPassword('password123');
            expect(isValid).toBe(true);
        });

        it('should reject incorrect password', async () => {
            const user = await User.create({
                email: '<EMAIL>',
                password: 'password123',
                role: 'partner'
            });

            const isValid = await user.verifyPassword('wrongpassword');
            expect(isValid).toBe(false);
        });

        it('should update password and hash it', async () => {
            const user = await User.create({
                email: '<EMAIL>',
                password: 'password123',
                role: 'partner'
            });

            const newPassword = 'newpassword123';
            await user.update({ password: newPassword });

            const isValid = await user.verifyPassword(newPassword);
            expect(isValid).toBe(true);
        });
    });

    describe('User Queries', () => {
        beforeEach(async () => {
            await User.bulkCreate([
                {
                    email: '<EMAIL>',
                    password: 'password123',
                    role: 'partner',
                    isActive: true
                },
                {
                    email: '<EMAIL>',
                    password: 'password123',
                    role: 'partner',
                    isActive: false
                },
                {
                    email: '<EMAIL>',
                    password: 'password123',
                    role: 'admin',
                    isActive: true
                }
            ]);
        });

        it('should find active partners', async () => {
            const activePartners = await User.findAll({
                where: {
                    role: 'partner',
                    isActive: true
                }
            });

            expect(activePartners).toHaveLength(1);
            expect(activePartners[0].email).toBe('<EMAIL>');
        });

        it('should find by email case-insensitive', async () => {
            const user = await User.findOne({
                where: sequelize.where(
                    sequelize.fn('LOWER', sequelize.col('email')),
                    sequelize.fn('LOWER', '<EMAIL>')
                )
            });

            expect(user).toBeTruthy();
            expect(user.email.toLowerCase()).toBe('<EMAIL>');
        });
    });
});
