// Load environment variables
require('dotenv').config();

const billionconnectService = require('./src/services/billionconnect.service');
const providerFactory = require('./src/services/provider.factory');

async function testBillionConnect() {
    console.log('Testing BillionConnect Service...\n');

    try {
        // Test 1: Check if service is properly configured
        console.log('1. Testing service configuration...');
        if (!billionconnectService.channelId || !billionconnectService.appSecret) {
            console.log('⚠️  BillionConnect credentials not configured in environment variables');
            console.log('   Please set BILLIONCONNECT_CHANNEL_ID and BILLIONCONNECT_APP_SECRET');
            return;
        }
        console.log('✅ Service configured with credentials');

        // Test 2: Test signature generation
        console.log('\n2. Testing signature generation...');
        const testBody = {
            tradeType: "F002",
            tradeTime: "2024-01-01 12:00:00",
            tradeData: {
                salesMethod: "5",
                language: "2"
            }
        };
        const signature = billionconnectService.generateSignature(testBody);
        console.log('✅ Signature generated:', signature.substring(0, 8) + '...');

        // Test 3: Test GMT+8 time generation
        console.log('\n3. Testing GMT+8 time generation...');
        const gmt8Time = billionconnectService.getGMT8Time();
        console.log('✅ GMT+8 time:', gmt8Time);

        // Test 4: Test product transformation
        console.log('\n4. Testing product transformation...');
        const mockProduct = {
            skuId: "1090",
            name: "Hong Kong-4G-1 day-200M",
            type: "110",
            days: "1",
            highFlowSize: "204800", // 200MB in KB
            limitFlowSpeed: "128",
            hotspotSupport: "0", // 0 = Available, 1 = Not Available
            desc: "Hong Kong 1-day package with 200MB high-speed data",
            country: [
                {
                    mcc: "HK",
                    name: "Hong Kong",
                    apn: "3gnet",
                    operatorInfo: [
                        {
                            operator: "cuhk",
                            network: "4G",
                            priority: "1"
                        }
                    ]
                }
            ]
        };

        const transformedProduct = billionconnectService.transformProduct(mockProduct);
        console.log('✅ Product transformed successfully');
        console.log('   Name:', transformedProduct.name);
        console.log('   Data:', transformedProduct.planData, transformedProduct.planDataUnit);
        console.log('   Plan Type:', transformedProduct.isUnlimited ? 'Unlimited' : 'Fixed');
        console.log('   Validity:', transformedProduct.validityDays, 'days');
        console.log('   Network:', transformedProduct.networkName, '(' + transformedProduct.networkType + ')');
        console.log('   Hotspot:', transformedProduct.hotspot ? 'Supported' : 'Not Supported');

        // Test 4b: Test unlimited plan transformation
        console.log('\n4b. Testing unlimited plan transformation...');
        const mockUnlimitedProduct = {
            skuId: "1091",
            name: "Global-Unlimited-30days",
            type: "110",
            days: "30",
            highFlowSize: "-1", // Unlimited
            limitFlowSpeed: "128",
            hotspotSupport: "0", // 0 = Available, 1 = Not Available
            desc: "Global unlimited data plan for 30 days",
            country: [
                {
                    mcc: "US",
                    name: "United States",
                    apn: "internet",
                    operatorInfo: [
                        {
                            operator: "T-Mobile",
                            network: "5G",
                            priority: "1"
                        }
                    ]
                }
            ]
        };

        const transformedUnlimitedProduct = billionconnectService.transformProduct(mockUnlimitedProduct);
        console.log('✅ Unlimited product transformed successfully');
        console.log('   Name:', transformedUnlimitedProduct.name);
        console.log('   Data:', transformedUnlimitedProduct.planData, transformedUnlimitedProduct.planDataUnit);
        console.log('   Plan Type:', transformedUnlimitedProduct.isUnlimited ? 'Unlimited' : 'Fixed');
        console.log('   Validity:', transformedUnlimitedProduct.validityDays, 'days');
        console.log('   Network:', transformedUnlimitedProduct.networkName, '(' + transformedUnlimitedProduct.networkType + ')');
        console.log('   Hotspot:', transformedUnlimitedProduct.hotspot ? 'Supported' : 'Not Supported');

        // Test 5: Test provider factory integration
        console.log('\n5. Testing provider factory integration...');
        const provider = providerFactory.getProvider('billionconnect');
        console.log('✅ BillionConnect provider found in factory');

        // Test 6: Test product standardization
        console.log('\n6. Testing product standardization...');
        const standardizedProduct = await providerFactory.standardizeProduct('billionconnect', transformedProduct);
        console.log('✅ Product standardized successfully');
        console.log('   External Product ID:', standardizedProduct.externalProductId);
        console.log('   Plan Category:', standardizedProduct.planCategory);
        console.log('   Plan Type:', standardizedProduct.planType);

        // Test 6b: Test unlimited product standardization
        console.log('\n6b. Testing unlimited product standardization...');
        const standardizedUnlimitedProduct = await providerFactory.standardizeProduct('billionconnect', transformedUnlimitedProduct);
        console.log('✅ Unlimited product standardized successfully');
        console.log('   External Product ID:', standardizedUnlimitedProduct.externalProductId);
        console.log('   Plan Category:', standardizedUnlimitedProduct.planCategory);
        console.log('   Plan Type:', standardizedUnlimitedProduct.planType);
        console.log('   Plan Data:', standardizedUnlimitedProduct.planData, standardizedUnlimitedProduct.planDataUnit);

        // Test 7: Test price functionality (F003)
        console.log('\n7. Testing F003 price functionality...');

        // Test single plan price
        console.log('\n7a. Testing single plan price...');
        const mockSkuId = "1090";
        const singlePrice = await billionconnectService.getSinglePlanPrice(mockSkuId, 1);
        if (singlePrice) {
            console.log('✅ Single plan price retrieved successfully');
            console.log('   SKU ID:', singlePrice.skuId);
            console.log('   Settlement Price:', singlePrice.settlementPrice);
            console.log('   Copies:', singlePrice.copies);
        } else {
            console.log('⚠️  No price data returned (this is expected if SKU doesn\'t exist)');
        }

        // Test multiple plan prices
        console.log('\n7b. Testing multiple plan prices...');
        const mockPriceRequests = [
            { skuId: "1090", copies: 1 },
            { skuId: "1091", copies: 2 }
        ];
        const multiplePrices = await billionconnectService.getPlanPrices(mockPriceRequests);
        console.log(`✅ Multiple plan prices request completed (returned ${multiplePrices.length} results)`);

        // Test price data transformation
        console.log('\n7c. Testing price data transformation...');
        const mockPriceData = {
            skuId: "1090",
            settlementPrice: "5.50",
            copies: "2",
            currency: "USD"
        };
        const transformedPrice = billionconnectService.transformPriceData(mockPriceData);
        console.log('✅ Price data transformed successfully');
        console.log('   SKU ID:', transformedPrice.skuId);
        console.log('   Buying Price:', transformedPrice.buyingPrice);
        console.log('   Quantity:', transformedPrice.quantity);
        console.log('   Total Price:', transformedPrice.totalPrice);
        console.log('   Currency:', transformedPrice.currency);

        console.log('\n🎉 All tests passed! BillionConnect integration is ready.');
        console.log('\n📝 Next steps:');
        console.log('   1. Set up environment variables (BILLIONCONNECT_CHANNEL_ID, BILLIONCONNECT_APP_SECRET)');
        console.log('   2. Run the database seeder to add BillionConnect provider');
        console.log('   3. Test with real API calls to fetch commodities and prices');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

// Run the test if this file is executed directly
if (require.main === module) {
    testBillionConnect();
}

module.exports = { testBillionConnect };
