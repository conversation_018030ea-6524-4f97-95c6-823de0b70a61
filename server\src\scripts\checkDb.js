const { Provider, EsimPlan } = require('../models');
const sequelize = require('../config/database');

async function checkDatabase() {
    try {
        // Check database connection
        await sequelize.authenticate();
        console.log('Database connection has been established successfully.');

        // Check Providers table
        const providers = await Provider.findAll();
        console.log('\nProviders in database:', providers.length);
        providers.forEach(provider => {
            console.log(`\nProvider: ${provider.name}`);
            console.log(`Status: ${provider.status}`);
            console.log(`Type: ${provider.type}`);
        });

        // Check EsimPlans table
        const plans = await EsimPlan.findAll({
            where: {
                providerId: providers.find(p => p.name.toLowerCase() === 'mobimatter')?.id
            }
        });
        console.log('\nMobimatter Plans in database:', plans.length);
        plans.forEach(plan => {
            console.log(`\nPlan: ${plan.name}`);
            console.log(`External Product ID: ${plan.externalProductId}`);
            console.log(`External SKU ID: ${plan.externalSkuId}`);
            console.log(`Status: ${plan.status}`);
            console.log(`Provider ID: ${plan.providerId}`);
        });

    } catch (error) {
        console.error('Error checking database:', error);
    } finally {
        await sequelize.close();
    }
}

checkDatabase(); 