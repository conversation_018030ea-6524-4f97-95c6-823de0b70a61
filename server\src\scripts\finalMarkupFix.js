const sequelize = require('../config/database');

async function finalMarkupFix() {
    try {
        // Update NULL values only
        await sequelize.query(`
            UPDATE Users 
            SET markupPercentage = 0 
            WHERE markupPercentage IS NULL
        `);
        console.log('Updated NULL values to 0');

        // Modify column
        await sequelize.query(`
            ALTER TABLE Users 
            MODIFY markupPercentage DECIMAL(5,2) NOT NULL DEFAULT 0
        `);
        console.log('Modified column to NOT NULL with default 0');

        // Verify the change
        const [results] = await sequelize.query(`
            SELECT 
                COLUMN_NAME,
                COLUMN_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT
            FROM 
                INFORMATION_SCHEMA.COLUMNS 
            WHERE 
                TABLE_SCHEMA = 'esim_platform' 
                AND TABLE_NAME = 'Users'
                AND COLUMN_NAME = 'markupPercentage'
        `);
        
        console.log('Column definition:', results[0]);
        process.exit(0);
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}

finalMarkupFix();
