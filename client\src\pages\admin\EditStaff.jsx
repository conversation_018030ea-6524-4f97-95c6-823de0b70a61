import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { ArrowLeft } from 'lucide-react';
import api from '@/lib/axios';

export default function EditStaff() {
    const { id } = useParams();
    const [formData, setFormData] = useState({
        firstName: '',
        lastName: '',
        email: '',
        phoneNumber: '',
        countryId: ''
    });
    const [countries, setCountries] = useState([]);
    const [loading, setLoading] = useState(false);
    const { toast } = useToast();
    const navigate = useNavigate();

    useEffect(() => {
        fetchStaff();
        fetchCountries();
    }, [id]);

    const fetchStaff = async () => {
        try {
            const response = await api.get(`/api/staff/${id}`);
            const staff = response.data;
            setFormData({
                firstName: staff.firstName,
                lastName: staff.lastName,
                email: staff.email,
                phoneNumber: staff.phoneNumber || '',
                countryId: staff.countryId
            });
        } catch (error) {
            // console.error('Error fetching staff:', error);
            toast({
                title: "Error",
                description: "Failed to fetch staff details",
                variant: "destructive"
            });
            navigate('/admin/staffs');
        }
    };

    const fetchCountries = async () => {
        try {
            const response = await api.get('/api/countries');
            setCountries(response.data);
        } catch (error) {
            // console.error('Error fetching countries:', error);
            toast({
                title: "Error",
                description: "Failed to fetch countries",
                variant: "destructive"
            });
        }
    };

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleCountryChange = (value) => {
        setFormData(prev => ({
            ...prev,
            countryId: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);

        try {
            await api.put(`/api/staff/${id}`, formData);
            toast({
                title: "Success",
                description: "Staff updated successfully"
            });
            navigate('/admin/staffs');
        } catch (error) {
            // console.error('Error updating staff:', error);
            toast({
                title: "Error",
                description: error.response?.data?.message || "Failed to update staff",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="h-full flex flex-col gap-6 p-6 max-w-3xl mx-auto">
            <div className="flex items-center gap-4">
                <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => navigate('/admin/staffs')}
                >
                    <ArrowLeft className="h-4 w-4" />
                </Button>
                <div>
                    <h1 className="text-2xl font-bold">Edit Staff</h1>
                    <p className="text-gray-600 mt-1">Update staff account details</p>
                </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
                <form onSubmit={handleSubmit} className="space-y-6 max-w-2xl">
                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="firstName">First Name *</Label>
                            <Input
                                id="firstName"
                                name="firstName"
                                value={formData.firstName}
                                onChange={handleChange}
                                required
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="lastName">Last Name *</Label>
                            <Input
                                id="lastName"
                                name="lastName"
                                value={formData.lastName}
                                onChange={handleChange}
                                required
                            />
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="email">Email *</Label>
                        <Input
                            id="email"
                            name="email"
                            type="email"
                            value={formData.email}
                            onChange={handleChange}
                            required
                        />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="phoneNumber">Phone Number</Label>
                            <Input
                                id="phoneNumber"
                                name="phoneNumber"
                                value={formData.phoneNumber}
                                onChange={handleChange}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="countryId">Country *</Label>
                            <Select
                                value={formData.countryId}
                                onValueChange={handleCountryChange}
                                required
                            >
                                <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Select country" />
                                </SelectTrigger>
                                <SelectContent className="max-h-[200px] overflow-y-auto">
                                    <SelectGroup>
                                        {countries.map(country => (
                                            <SelectItem 
                                                key={country.id} 
                                                value={country.id}
                                                className="cursor-pointer"
                                            >
                                                {country.name}
                                            </SelectItem>
                                        ))}
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    <div className="flex justify-end gap-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => navigate('/admin/staffs')}
                        >
                            Cancel
                        </Button>
                        <Button type="submit" disabled={loading}>
                            {loading ? 'Updating...' : 'Update Staff'}
                        </Button>
                    </div>
                </form>
            </div>
        </div>
    );
}
