'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Add API key fields to users table
    await queryInterface.sequelize.query(
      `ALTER TABLE users
      ADD COLUMN IF NOT EXISTS apiKey VARCHAR(255) NULL,
      ADD COLUMN IF NOT EXISTS apiKeyHash VARCHAR(255) NULL,
      ADD COLUMN IF NOT EXISTS apiKeyLastReset TIMESTAMP NULL,
      ADD INDEX IF NOT EXISTS idx_api_key_hash (apiKeyHash);`
    );

    console.log('Added API key fields to users table');
  },

  async down(queryInterface, Sequelize) {
    // Remove API key fields from users table
    await queryInterface.sequelize.query(
      `ALTER TABLE users
      DROP COLUMN IF EXISTS apiKey,
      DROP COLUMN IF EXISTS apiKeyHash,
      DROP COLUMN IF EXISTS apiKeyLastReset,
      DROP INDEX IF EXISTS idx_api_key_hash;`
    );

    console.log('Removed API key fields from users table');
  }
}; 