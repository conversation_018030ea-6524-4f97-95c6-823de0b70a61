const NodeCache = require('node-cache');

class CacheService {
    constructor() {
        // Cache with default TTL of 1 hour and check period of 10 minutes
        this.cache = new NodeCache({ 
            stdTTL: 3600, // 1 hour in seconds
            checkperiod: 600 // 10 minutes in seconds
        });

        // Cache keys
        this.KEYS = {
            COUNTRIES: 'countries',
            REGIONS: 'regions'
        };
    }

    // Get data from cache
    get(key) {
        return this.cache.get(key);
    }

    // Set data in cache with optional TTL
    set(key, value, ttl = 3600) {
        return this.cache.set(key, value, ttl);
    }

    // Check if key exists in cache
    has(key) {
        return this.cache.has(key);
    }

    // Delete key from cache
    del(key) {
        return this.cache.del(key);
    }

    // Clear all cache
    flush() {
        return this.cache.flushAll();
    }

    // Get stats
    getStats() {
        return this.cache.getStats();
    }
}

module.exports = new CacheService(); 