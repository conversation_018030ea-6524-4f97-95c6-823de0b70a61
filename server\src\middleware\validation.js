const { body } = require('express-validator');

// Validation middleware
const loginValidation = [
    body('email').isEmail().withMessage('Please enter a valid email'),
    body('password').notEmpty().withMessage('Password is required')
];

const otpValidation = [
    body('otp').isLength({ min: 6, max: 6 }).withMessage('OTP must be 6 digits'),
    body('tempToken').notEmpty().withMessage('Temporary token is required')
];

module.exports = {
    loginValidation,
    otpValidation
};
