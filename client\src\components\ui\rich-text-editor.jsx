import React from 'react'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'

const RichTextEditor = React.forwardRef(({ value, onChange, placeholder, className }, ref) => {
  const editor = useEditor({
    extensions: [StarterKit],
    content: value,
    editorProps: {
      attributes: {
        class: 'prose prose-sm max-w-none focus:outline-none min-h-[200px] px-3 py-2',
      },
    },
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML())
    },
  })

  // Expose editor methods through ref
  React.useImperativeHandle(ref, () => ({
    // Add any methods you want to expose
    focus: () => editor?.chain().focus().run(),
    blur: () => editor?.chain().blur().run(),
    clear: () => editor?.chain().clearContent().run(),
  }))

  return (
    <div className={`relative border rounded-md bg-background ${className}`}>
      <EditorContent editor={editor} />
      {!value && !editor?.isFocused && (
        <div className="absolute top-0 left-0 p-3 text-muted-foreground text-sm pointer-events-none">
          {placeholder}
        </div>
      )}
    </div>
  )
})

RichTextEditor.displayName = 'RichTextEditor'

export { RichTextEditor }
