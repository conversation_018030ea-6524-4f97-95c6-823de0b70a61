const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const OTP = sequelize.define('OTP', {
    id: {
        type: DataTypes.CHAR(36),
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    userId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        references: {
            model: 'users',
            key: 'id'
        }
    },
    code: {
        type: DataTypes.STRING(6),
        allowNull: false
    },
    expiresAt: {
        type: DataTypes.DATE,
        allowNull: false
    },
    isUsed: {
        type: DataTypes.BOOLEAN,
        defaultValue: false
    }
}, {
    tableName: 'OTPs',
    timestamps: true,
    indexes: [
        {
            name: 'idx_user_expires',
            fields: ['userId', 'expiresAt', 'isUsed']
        },
        {
            name: 'idx_cleanup',
            fields: ['expiresAt', 'isUsed']
        }
    ]
});

// Set expiration time to 5 minutes from creation
OTP.beforeCreate(async (otp) => {
    otp.expiresAt = new Date(Date.now() + 5 * 60 * 1000);
});

module.exports = OTP;
