const { Model, DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const { v4: uuidv4 } = require('uuid');

class KnowledgeBase extends Model {
    static associate(models) {
        KnowledgeBase.belongsTo(models.User, {
            foreignKey: 'authorId',
            as: 'author'
        });
    }
}

KnowledgeBase.init({
    id: {
        type: DataTypes.STRING(36),
        defaultValue: () => uuidv4(),
        primaryKey: true
    },
    title: {
        type: DataTypes.STRING(255),
        allowNull: false,
        validate: {
            notEmpty: true
        }
    },
    content: {
        type: DataTypes.TEXT,
        allowNull: false,
        validate: {
            notEmpty: true
        }
    },
    category: {
        type: DataTypes.ENUM('general', 'technical', 'billing', 'support', 'faq'),
        allowNull: false,
        defaultValue: 'general'
    },
    visibility: {
        type: DataTypes.ENUM('public', 'partner', 'admin'),
        allowNull: false,
        defaultValue: 'public'
    },
    status: {
        type: DataTypes.ENUM('draft', 'published', 'archived'),
        allowNull: false,
        defaultValue: 'draft'
    },
    authorId: {
        type: DataTypes.STRING(36),
        allowNull: false,
        references: {
            model: 'users',
            key: 'id'
        }
    }
}, {
    sequelize,
    modelName: 'KnowledgeBase',
    tableName: 'knowledgebase',
    timestamps: true
});

module.exports = KnowledgeBase;
