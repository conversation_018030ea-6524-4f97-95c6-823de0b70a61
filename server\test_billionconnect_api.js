// Load environment variables
require('dotenv').config();

const billionconnectService = require('./src/services/billionconnect.service');

async function testBillionConnectAPI() {
    console.log('🚀 Testing BillionConnect API with real credentials...\n');

    try {
        // Test real API call to fetch commodities
        console.log('📡 Making real API call to BillionConnect F002 (Get Commodities)...');
        console.log('   API URL:', billionconnectService.baseURL);
        console.log('   Channel ID:', billionconnectService.channelId);
        console.log('   App Secret:', billionconnectService.appSecret ? '[CONFIGURED]' : '[NOT CONFIGURED]');
        
        const commodities = await billionconnectService.getCommodities();
        
        if (commodities.length === 0) {
            console.log('⚠️  No commodities returned from API');
            console.log('   This could mean:');
            console.log('   - Invalid credentials');
            console.log('   - No products available');
            console.log('   - API endpoint issues');
            return;
        }

        console.log(`✅ Successfully retrieved ${commodities.length} commodities from BillionConnect!`);
        
        // Show first few products as examples
        console.log('\n📦 Sample Products:');
        commodities.slice(0, 3).forEach((product, index) => {
            console.log(`\n   ${index + 1}. ${product.name}`);
            console.log(`      SKU ID: ${product.skuId}`);
            console.log(`      Type: ${product.type}`);
            console.log(`      Days: ${product.days}`);
            console.log(`      Data: ${product.highFlowSize} KB`);
            console.log(`      Hotspot: ${product.hotspotSupport === '1' ? 'Yes' : 'No'}`);
            console.log(`      Countries: ${product.country?.length || 0} supported`);
            if (product.country && product.country.length > 0) {
                console.log(`      Primary Country: ${product.country[0].name} (${product.country[0].mcc})`);
                console.log(`      Primary Operator: ${product.country[0].operatorInfo?.[0]?.operator || 'N/A'}`);
            }
        });

        // Test product transformation
        console.log('\n🔄 Testing product transformation...');
        const transformedProducts = commodities.slice(0, 2).map(product => {
            return billionconnectService.transformProduct(product);
        }).filter(p => p !== null);

        console.log(`✅ Successfully transformed ${transformedProducts.length} products`);
        
        transformedProducts.forEach((product, index) => {
            console.log(`\n   Transformed Product ${index + 1}:`);
            console.log(`      Name: ${product.name}`);
            console.log(`      Data: ${product.planData} ${product.planDataUnit}`);
            console.log(`      Validity: ${product.validityDays} days`);
            console.log(`      Network: ${product.networkName} (${product.networkType})`);
            console.log(`      Country: ${product.country.name} (${product.country.id})`);
            console.log(`      Hotspot: ${product.hotspot ? 'Supported' : 'Not Supported'}`);
        });

        console.log('\n🎉 BillionConnect API integration is working perfectly!');
        console.log('\n📝 Next steps:');
        console.log('   1. Run the database seeder to add BillionConnect provider');
        console.log('   2. Use the sync script to import all products to your database');
        console.log('   3. Products will then be available via your existing API endpoints');

    } catch (error) {
        console.error('❌ API test failed:', error.message);
        
        if (error.response) {
            console.error('   HTTP Status:', error.response.status);
            console.error('   Response Data:', error.response.data);
        }
        
        if (error.message.includes('BillionConnect API Error')) {
            console.log('\n💡 Troubleshooting tips:');
            console.log('   - Verify your BILLIONCONNECT_CHANNEL_ID is correct');
            console.log('   - Verify your BILLIONCONNECT_APP_SECRET is correct');
            console.log('   - Check if your account has access to the F002 trade type');
            console.log('   - Ensure your account is active and not suspended');
        }
    }
}

// Run the test if this file is executed directly
if (require.main === module) {
    testBillionConnectAPI()
        .then(() => {
            console.log('\n✨ API test completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 API test failed:', error);
            process.exit(1);
        });
}

module.exports = { testBillionConnectAPI };
