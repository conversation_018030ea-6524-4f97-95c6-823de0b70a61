import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import LoginPage from './pages/auth/LoginPage';
import ResetPassword from './pages/auth/ResetPassword';
import AdminLayout from './components/layout/AdminLayout';
import PartnerLayout from './components/layout/PartnerLayout';
import Dashboard from './pages/admin/Dashboard';
import Orders from './pages/admin/Orders';
import Partners from './pages/admin/Partners';
import AddPartner from './pages/admin/AddPartner';
import EditPartner from './pages/admin/EditPartner';
import PartnerDetails from './pages/admin/PartnerDetails';
import EsimPlans from './pages/admin/EsimPlans';
import EsimPlanForm from './pages/admin/EsimPlanForm';
import ViewEsimPlan from './pages/admin/ViewEsimPlan';
import EsimStockManagement from './pages/admin/EsimStockManagement';
import Staffs from './pages/admin/Staffs';
import AddStaff from './pages/admin/AddStaff';
import EditStaff from './pages/admin/EditStaff';
import KnowledgeBase from './pages/admin/KnowledgeBase';
import UserDashboard from './pages/partner/Dashboard';
import UserPlans from './pages/partner/Plans';
import PlanDetails from './pages/partner/PlanDetails';
import Account from './pages/partner/Account';
import UserOrders from './pages/partner/Orders';
import UserWallet from './pages/partner/Wallet';
import UserKnowledgeBase from './pages/partner/KnowledgeBase';
import ApiAccess from './pages/partner/ApiAccess';
import { Toaster } from "@/components/ui/toaster";
import CentralWalletManagement from './pages/admin/CentralWalletManagement';
import Providers from './pages/admin/Providers';
import Cart from './pages/partner/Cart';
import OrderDetails from './pages/partner/OrderDetails';
import AdminOrderDetails from './pages/admin/OrderDetails';

import "./styles/globals.css";
import "./styles/calendar.css";
import "./styles/datepicker.css";

const PrivateRoute = ({ children, roles }) => {
    const { user, loading } = useAuth();        

    if (loading) {
        return <div>Loading...</div>;
    }

    if (!user) {
        return <Navigate to="/login" />;
    }

    if (roles && !roles.includes(user.role)) {
        return <Navigate to={user.role === 'admin' ? '/admin/dashboard' : '/dashboard'} />;
    }

    return children;
};

function App() {
    return (
        <>
            <Router>
                <AuthProvider>
                    <Routes>
                        {/* Public Routes */}
                        <Route path="/login" element={<LoginPage />} />
                        <Route path="/reset-password" element={<ResetPassword />} />

                        {/* Admin Routes */}
                        <Route path="/admin" element={
                            <PrivateRoute roles={['admin']}>
                                <AdminLayout />
                            </PrivateRoute>
                        }>
                            <Route index element={<Navigate to="/admin/dashboard" replace />} />
                            <Route path="dashboard" element={<Dashboard />} />
                            <Route path="orders" element={<Orders />} />
                            <Route path="orders/:id" element={<AdminOrderDetails />} />
                            <Route path="partners" element={<Partners />} />
                            <Route path="partners/add" element={<AddPartner />} />
                            <Route path="partners/:id" element={<PartnerDetails />} />
                            <Route path="partners/edit/:id" element={<EditPartner />} />
                            <Route path="esim-plans" element={<EsimPlans />} />
                            <Route path="esim-plans/:id" element={<ViewEsimPlan />} />
                            <Route path="esim-plans/:id/stock" element={<EsimStockManagement />} />
                            <Route path="esim-plans/edit/:id" element={<EsimPlanForm />} />
                            <Route path="esim-plans/new" element={<EsimPlanForm />} />
                            <Route path="staffs" element={<Staffs />} />
                            <Route path="staffs/add" element={<AddStaff />} />
                            <Route path="staffs/edit/:id" element={<EditStaff />} />
                            <Route path="knowledge-base" element={<KnowledgeBase />} />
                            <Route path="central-wallet" element={<CentralWalletManagement />} />
                            <Route path="providers" element={<Providers />} />
                        </Route>

                        {/* User Routes */}
                        <Route path="/dashboard" element={
                            <PrivateRoute roles={['partner']}>
                                <PartnerLayout />
                            </PrivateRoute>
                        }>
                            <Route index element={<UserDashboard />} />
                            <Route path="plans" element={<UserPlans />} />
                            <Route path="plans/:id" element={<PlanDetails />} />
                            <Route path="orders" element={<UserOrders />} />
                            <Route path="wallet" element={<UserWallet />} />
                            <Route path="account" element={<Account />} />
                            <Route path="knowledge-base" element={<UserKnowledgeBase />} />
                            <Route path="api-access" element={<ApiAccess />} />
                            <Route path="cart" element={<Cart />} />
                            <Route path="orders" element={<Orders />} />
                            <Route path="orders/:id" element={<OrderDetails />} />
                        </Route>

                        {/* Default Routes */}
                        <Route path="/" element={
                            <Navigate to="/login" replace />
                        } />
                        <Route path="*" element={
                            <Navigate to="/login" replace />
                        } />
                    </Routes>
                </AuthProvider>
            </Router>
            <Toaster />
        </>
    );
}

export default App;
