const express = require('express');
const router = express.Router();
const { isAuthenticated, isAdmin } = require('../middleware/auth');
const models = require('../models');
const { Op } = require('sequelize');

router.get('/stats', isAuthenticated, isAdmin, async (req, res) => {
    try {
        // Get total partners count
        const totalPartners = await models.User.count({
            where: { role: 'partner' }
        });

        // Get total orders and revenue
        const orderStats = await models.Order.findOne({
            attributes: [
                [models.sequelize.fn('COUNT', '*'), 'totalOrders'],
                [models.sequelize.fn('SUM', models.sequelize.col('orderTotal')), 'totalRevenue']
            ]
        });

        // Get total active plans
        const totalPlans = await models.EsimPlan.count({
            where: { 
                status: 'visible',
                isActive: true
            }
        });

        // Get orders in the last 30 days
        const last30DaysOrders = await models.Order.count({
            where: {
                createdAt: {
                    [Op.gte]: new Date(new Date() - 30 * 24 * 60 * 60 * 1000)
                }
            }
        });

        // Get monthly order trends
        const monthlyTrends = await models.Order.findAll({
            attributes: [
                [models.sequelize.fn('DATE_FORMAT', models.sequelize.col('createdAt'), '%Y-%m-01'), 'month'],
                [models.sequelize.fn('COUNT', '*'), 'count'],
                [models.sequelize.fn('SUM', models.sequelize.col('orderTotal')), 'revenue']
            ],
            group: [models.sequelize.fn('DATE_FORMAT', models.sequelize.col('createdAt'), '%Y-%m-01')],
            order: [[models.sequelize.fn('DATE_FORMAT', models.sequelize.col('createdAt'), '%Y-%m-01'), 'ASC']]
        });

        // Get top partners
        const topPartners = await models.Order.findAll({
            attributes: [
                'userId',
                [models.sequelize.fn('COUNT', '*'), 'orderCount'],
                [models.sequelize.fn('SUM', models.sequelize.col('orderTotal')), 'totalSpent']
            ],
            include: [{
                model: models.User,
                as: 'user',
                attributes: ['email', 'businessName', 'id']
            }],
            group: ['userId'],
            order: [[models.sequelize.fn('COUNT', '*'), 'DESC']],
            limit: 5
        });

        // Get recent orders with partner details
        const recentOrders = await models.Order.findAll({
            attributes: ['id', 'quantity', 'orderTotal', 'status', 'createdAt'],
            include: [
                {
                    model: models.User,
                    as: 'user',
                    attributes: ['email', 'businessName', 'id']
                },
                {
                    model: models.EsimPlan,
                    as: 'plan',
                    attributes: ['name', 'planData', 'planDataUnit', 'validityDays', 'planType', 'customPlanData', 'planCategory']
                }
            ],
            order: [['createdAt', 'DESC']],
            limit: 5
        });

        // Get recent signups
        const recentSignups = await models.User.findAll({
            where: { role: 'partner' },
            order: [['createdAt', 'DESC']],
            limit: 5,
            attributes: ['id', 'email', 'businessName', 'createdAt']
        });

        res.json({
            totalPartners,
            totalOrders: parseInt(orderStats.getDataValue('totalOrders')),
            totalRevenue: parseFloat(orderStats.getDataValue('totalRevenue') || 0),
            totalPlans,
            last30DaysOrders,
            monthlyTrends: monthlyTrends.map(trend => ({
                month: trend.getDataValue('month'),
                count: parseInt(trend.getDataValue('count')),
                revenue: parseFloat(trend.getDataValue('revenue') || 0)
            })),
            topPartners: topPartners.map(partner => ({
                email: partner.user.email,
                businessName: partner.user.businessName,
                orderCount: parseInt(partner.getDataValue('orderCount')),
                totalSpent: parseFloat(partner.getDataValue('totalSpent'))
            })),
            recentOrders: recentOrders.map(order => ({
                id: order.id,
                partnerName: order.user.businessName || order.user.email,
                planName: order.plan.name,
                planType: order.plan.planType,
                customPlanData: order.plan.customPlanData,
                planData: order.plan.planData,
                planDataUnit: order.plan.planDataUnit,
                validityDays: order.plan.validityDays,
                planCategory: order.plan.planCategory,
                quantity: order.quantity || 1,
                amount: parseFloat(order.orderTotal),
                status: order.status,
                createdAt: order.createdAt
            })),
            recentSignups: recentSignups.map(user => ({
                email: user.email,
                businessName: user.businessName,
                createdAt: user.createdAt,
                id: user.id
            }))
        });
    } catch (error) {
        console.error('Error fetching admin dashboard stats:', error);
        res.status(500).json({ message: 'Failed to fetch dashboard statistics' });
    }
});

module.exports = router;
