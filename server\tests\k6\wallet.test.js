import http from 'k6/http';
import { sleep, group } from 'k6';
import { Counter } from 'k6/metrics';
import { CONFIG } from './config.js';
import { checkResponse, getAuthToken } from './helpers.js';

// Custom metrics
const emailFailures = new Counter('email_failures');
const connectionErrors = new Counter('connection_errors');
const totalEmails = new Counter('total_emails');

export const options = {
    thresholds: {
        'http_req_duration': ['p(95)<2000'],    // 95% of requests should be below 2s
        'email_failures': ['rate<0.1'],         // Allow up to 10% email failures
        'connection_errors': ['rate<0.05'],      // Allow up to 5% connection errors
        'total_emails': ['count>=500'],        // Ensure we send at least 500 emails
    },
    scenarios: {
        wallet_test: {
            executor: 'ramping-vus',
            startVUs: 10,
            stages: [
                { duration: '30s', target: 20 },  // Quick ramp up
                { duration: '1m', target: 20 },   // Maintain load
                { duration: '30s', target: 0 }    // Ramp down
            ]
        }
    }
};

export function setup() {
    const token = getAuthToken(http);
    return { 
        baseUrl: CONFIG.baseUrl,
        token: token,
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        }
    };
}

export default function (data) {
    const baseUrl = data.baseUrl;
    const headers = data.headers;

    group('Load Test - Wallet Credit Notification', () => {
        try {
            // Generate unique email and balance data
            const uniqueId = Math.floor(Math.random() * 1000000);
            const email = `user${uniqueId}@test.com`;
            const amount = Math.floor(Math.random() * 1000) + 1; // Random amount between 1 and 1000
            const newBalance = Math.floor(Math.random() * 2000) + amount; // Random new balance higher than amount

            // Send wallet credit notification
            const response = http.post(`${baseUrl}/api/notifications/wallet-credit`, JSON.stringify({
                email: email,
                amount: amount,
                newBalance: newBalance
            }), {
                headers: headers
            });

            // Check response and increment total emails counter
            const success = checkResponse(response);
            totalEmails.add(1);
            
            if (!success) {
                emailFailures.add(1);
                console.error(`Wallet credit notification failed: ${response.status} ${response.body}`);
            }

        } catch (error) {
            console.error('Wallet credit notification error:', error);
            emailFailures.add(1);
            connectionErrors.add(1);
        }

        sleep(0.1); // Reduced wait time to achieve higher throughput
    });
}
