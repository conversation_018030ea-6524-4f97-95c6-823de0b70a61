const planCache = new Map();
const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes in milliseconds
const MAX_CACHE_SIZE = 1000; // Maximum number of cache entries

// Helper function to invalidate cache for a specific plan
// If planId is null, invalidate all cached plans
const invalidatePlanCache = (planId) => {
    if (planId === null) {
        // Clear the entire cache
        planCache.clear();
        return;
    }

    // Remove all cache entries that contain this plan ID
    for (const key of planCache.keys()) {
        if (key.includes(planId)) {
            planCache.delete(key);
        }
    }
};

// Helper function to clean up expired cache entries
const cleanupExpiredCache = () => {
    const now = Date.now();
    for (const [key, value] of planCache.entries()) {
        if (now - value.timestamp > CACHE_DURATION) {
            planCache.delete(key);
        }
    }
};

// Helper function to manage cache size
const manageCacheSize = () => {
    if (planCache.size > MAX_CACHE_SIZE) {
        // Remove oldest entries (LRU-style)
        const entries = Array.from(planCache.entries());
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

        // Remove oldest 20% of entries
        const toRemove = Math.floor(entries.length * 0.2);
        for (let i = 0; i < toRemove; i++) {
            planCache.delete(entries[i][0]);
        }
    }
};

// Helper function to get cached plan with timestamp validation
const getCachedPlan = (cacheKey) => {
    if (planCache.has(cacheKey)) {
        const { plan, timestamp } = planCache.get(cacheKey);
        const now = Date.now();

        // Return plan if cache is still valid
        if (now - timestamp < CACHE_DURATION) {
            // Update timestamp for LRU behavior
            planCache.set(cacheKey, { plan, timestamp: now });
            return plan;
        }

        // Remove expired cache entry
        planCache.delete(cacheKey);
    }
    return null;
};

// Helper function to set cache with timestamp
const setCachePlan = (cacheKey, plan, duration = CACHE_DURATION) => {
    // Clean up expired entries periodically
    if (Math.random() < 0.1) { // 10% chance to trigger cleanup
        cleanupExpiredCache();
    }

    // Manage cache size
    manageCacheSize();

    planCache.set(cacheKey, {
        plan,
        timestamp: Date.now()
    });

    // Set timeout to automatically clear cache after duration
    setTimeout(() => planCache.delete(cacheKey), duration * 1000); // Convert to milliseconds if duration is in seconds
};

// Cache warming function for common queries
const warmCache = async () => {
    try {
        // This would be called by a background job or on server startup
        // to pre-populate cache with common queries
        console.log('Cache warming completed');
    } catch (error) {
        console.error('Error warming cache:', error);
    }
};

// Get cache statistics
const getCacheStats = () => {
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const [key, value] of planCache.entries()) {
        if (now - value.timestamp > CACHE_DURATION) {
            expiredEntries++;
        } else {
            validEntries++;
        }
    }

    return {
        totalEntries: planCache.size,
        validEntries,
        expiredEntries,
        hitRate: planCache.hitRate || 0
    };
};

module.exports = {
    planCache,
    CACHE_DURATION,
    invalidatePlanCache,
    getCachedPlan,
    setCachePlan,
    cleanupExpiredCache,
    warmCache,
    getCacheStats
};