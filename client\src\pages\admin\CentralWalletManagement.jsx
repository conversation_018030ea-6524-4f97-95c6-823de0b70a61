import React, { useState, useEffect } from 'react';
import api from '@/lib/axios';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Loader2, WalletCards, ArrowRightLeft, TrendingUp, BarChart3 } from 'lucide-react';

const StatCard = ({ icon: Icon, label, value, valueColor = "text-primary" }) => (
    <div className="p-6 bg-card rounded-lg border hover:shadow-md transition-shadow">
        <div className="flex items-start gap-4">
            <div className="p-3 rounded-lg bg-muted">
                <Icon className="h-6 w-6 text-muted-foreground" />
            </div>
            <div>
                <p className="text-sm font-medium text-muted-foreground">{label}</p>
                <p className={`text-2xl font-bold mt-1 ${valueColor}`}>{value}</p>
            </div>
        </div>
    </div>
);

export default function CentralWalletManagement() {
    const { toast } = useToast();
    const [centralWallet, setCentralWallet] = useState(null);
    const [loading, setLoading] = useState(true);
    const [newTotalBalance, setNewTotalBalance] = useState("");
    const [partnerId, setPartnerId] = useState("");
    const [transferAmount, setTransferAmount] = useState("");
    const [transferDescription, setTransferDescription] = useState("");

    useEffect(() => {
        const fetchCentralWallet = async () => {
            try {
                const response = await api.get('/api/central-wallet');
                setCentralWallet(response.data);
            } catch (error) {
                console.error('Error fetching central wallet:', error);
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: error.response?.data?.message || 'Failed to fetch central wallet'
                });
            } finally {
                setLoading(false);
            }
        };
        fetchCentralWallet();
    }, []);

    const handleUpdateTotalBalance = async (e) => {
        e.preventDefault();
        const total = Number(newTotalBalance);
        if (isNaN(total) || total < 0) {
            toast({
                variant: 'destructive',
                title: 'Invalid Amount',
                description: 'Please enter a valid non-negative number.'
            });
            return;
        }
        try {
            const response = await api.put('/api/central-wallet', { totalBalance: total });
            setCentralWallet(response.data.totalBalance ? { totalBalance: response.data.totalBalance } : centralWallet);
            toast({
                title: 'Success',
                description: response.data.message || 'Central wallet updated successfully.'
            });
            setNewTotalBalance("");
        } catch (error) {
            console.error('Error updating central wallet:', error);
            toast({
                variant: 'destructive',
                title: 'Error',
                description: error.response?.data?.message || 'Failed to update central wallet'
            });
        }
    };

    const handleTransferFunds = async (e) => {
        e.preventDefault();
        if (!partnerId) {
            toast({ variant: 'destructive', title: 'Error', description: 'Partner ID required' });
            return;
        }
        const amount = Number(transferAmount);
        if (isNaN(amount) || amount <= 0) {
            toast({ variant: 'destructive', title: 'Invalid Amount', description: 'Please enter a valid amount greater than 0' });
            return;
        }
        try {
            const response = await api.post(`/api/partners/${partnerId}/central-wallet/add-funds`, {
                amount,
                description: transferDescription
            });
            setCentralWallet(response.data.centralWallet);
            toast({
                title: 'Funds Transferred',
                description: response.data.message || 'Funds transferred successfully'
            });
            setPartnerId("");
            setTransferAmount("");
            setTransferDescription("");
        } catch (error) {
            console.error('Error transferring funds:', error);
            toast({
                variant: 'destructive',
                title: 'Error',
                description: error.response?.data?.message || 'Failed to transfer funds'
            });
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
                <div className="flex flex-col items-center gap-4">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <p className="text-sm text-muted-foreground">Loading wallet details...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto py-8 max-w-6xl px-4 space-y-8">
            <div className="flex items-center justify-between mb-6 bg-gradient-to-r from-blue-800 to-blue-600 rounded-t-lg p-4">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight text-white">Central Wallet</h1>
                    <p className="text-muted-foreground mt-1 text-white/80">Manage and monitor central wallet funds</p>
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <StatCard 
                    icon={WalletCards}
                    label="Total Available Funds"
                    value={`$${Number(centralWallet?.totalBalance || 0).toFixed(2)}`}
                />
                <StatCard 
                    icon={ArrowRightLeft}
                    label="Total Transferred to Partners"
                    value={`$${Number(centralWallet?.statistics?.totalTransferredToPartners || 0).toFixed(2)}`}
                    valueColor="text-green-500"
                />
                <StatCard 
                    icon={TrendingUp}
                    label="Total Received Back"
                    value={`$${Number(centralWallet?.statistics?.totalReceivedBack || 0).toFixed(2)}`}
                    valueColor="text-blue-500"
                />
                <StatCard 
                    icon={BarChart3}
                    label="Net Amount with Partners"
                    value={`$${Number(centralWallet?.statistics?.netAmountWithPartners || 0).toFixed(2)}`}
                    valueColor="text-orange-500"
                />
            </div>
            <div className="p-4 bg-card rounded-lg border hover:shadow-md transition-shadow">
              <Label>Total Completed Transactions</Label>
              <div className="text-lg font-semibold">
                {centralWallet?.statistics?.totalTransactions || 0}
              </div>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
                {/* <Card>
                    <CardHeader>
                        <CardTitle>Update Balance</CardTitle>
                        <CardDescription>Modify the total available balance</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleUpdateTotalBalance} className="space-y-4">
                            <div className="space-y-2">
                                <Label>New Total Balance</Label>
                                <div className="relative">
                                    <span className="absolute left-3 top-2.5 text-muted-foreground">$</span>
                                    <Input
                                        type="number"
                                        step="0.01"
                                        placeholder="Enter new total balance"
                                        value={newTotalBalance}
                                        onChange={(e) => setNewTotalBalance(e.target.value)}
                                        className="pl-8"
                                    />
                                </div>
                            </div>
                            <Button type="submit" className="w-full bg-blue-700 text-white">Update Balance</Button>
                        </form>
                    </CardContent>
                </Card> */}

                <Card>
                    <CardHeader>
                        <CardTitle>Transfer Funds</CardTitle>
                        <CardDescription>Allocate funds to partner wallets</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleTransferFunds} className="space-y-4">
                            <div className="space-y-2">
                                <Label>Partner ID</Label>
                                <Input
                                    type="text"
                                    placeholder="Enter partner ID"
                                    value={partnerId}
                                    onChange={(e) => setPartnerId(e.target.value)}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label>Amount</Label>
                                <div className="relative">
                                    <span className="absolute left-3 top-2.5 text-muted-foreground">$</span>
                                    <Input
                                        type="number"
                                        step="0.01"
                                        placeholder="Enter amount to transfer"
                                        value={transferAmount}
                                        onChange={(e) => setTransferAmount(e.target.value)}
                                        className="pl-8"
                                    />
                                </div>
                            </div>
                            <div className="space-y-2">
                                <Label>Description</Label>
                                <Input
                                    type="text"
                                    placeholder="Enter description"
                                    value={transferDescription}
                                    onChange={(e) => setTransferDescription(e.target.value)}
                                />
                            </div>
                            <Button type="submit" className="w-full bg-blue-700 text-white">Transfer Funds</Button>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}