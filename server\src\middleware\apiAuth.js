const { User } = require('../models');
const { verifyApi<PERSON>ey } = require('../utils/apiKeyGenerator');
const rateLimit = require('express-rate-limit');

// Rate limiter for API requests
const apiLimiter = rateLimit({
    windowMs: 60 * 1000, // 1 minute
    max: 500, // 500 requests per minute
    message: { 
        success: false, 
        error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: 'Too many requests, please try again later.'
        }
    },
    keyGenerator: (req) => {
        // Use the Partner ID for rate limiting
        return req.headers['x-partner-id'] || req.ip;
    }
});

/**
 * Middleware to authenticate API requests using API key
 */
const authenticateApiKey = async (req, res, next) => {
    try {
        // Get API key from Authorization header
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Missing API key'
                }
            });
        }

        const apiKey = authHeader.split(' ')[1];
        
        // Get Partner ID from header
        const partnerId = req.headers['x-partner-id'];
        if (!partnerId) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Missing Partner ID'
                }
            });
        }

        // Find the partner by ID
        const partner = await User.findOne({
            where: {
                id: partnerId,
                role: 'partner',
                isActive: true
            }
        });

        if (!partner) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Invalid Partner ID'
                }
            });
        }

        // Check if partner has an API key configured
        if (!partner.apiKeyHash) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Partner has no API key configured'
                }
            });
        }

        // Verify the API key
        const isValid = await verifyApiKey(apiKey, partner.apiKeyHash);
        if (!isValid) {
            return res.status(401).json({
                success: false,
                error: {
                    code: 'UNAUTHORIZED',
                    message: 'Invalid API key'
                }
            });
        }

        // Attach the partner to the request
        req.partner = partner;
        next();
    } catch (error) {
        console.error('API authentication error:', error);
        return res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'Server error during authentication'
            }
        });
    }
};

module.exports = { authenticateApiKey, apiLimiter }; 