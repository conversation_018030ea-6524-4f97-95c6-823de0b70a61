FROM nginx:1.21-alpine

# Remove default Nginx configuration
RUN rm /etc/nginx/conf.d/default.conf

# Create SSL-enabled configuration
RUN echo 'server { \
    listen 80; \
    server_name localhost api.vizlync.net; \
    \
    # For Let'"'"'s Encrypt HTTP-01 challenge \
    location ^~ /.well-known/acme-challenge/ { \
        root /var/www/certbot; \
        try_files $uri =404; \
    } \
    \
    # Redirect all other HTTP traffic to HTTPS \
    location / { \
        return 301 https://$host$request_uri; \
    } \
} \
\
server { \
    listen 443 ssl http2; \
    server_name api.vizlync.net; \
    \
    ssl_certificate /etc/letsencrypt/live/api.vizlync.net/fullchain.pem; \
    ssl_certificate_key /etc/letsencrypt/live/api.vizlync.net/privkey.pem; \
    \
    # SSL parameters \
    ssl_protocols TLSv1.2 TLSv1.3; \
    ssl_prefer_server_ciphers on; \
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384; \
    ssl_session_timeout 1d; \
    ssl_session_cache shared:SSL:10m; \
    ssl_session_tickets off; \
    \
    # Security headers \
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always; \
    add_header X-Content-Type-Options nosniff; \
    add_header X-Frame-Options SAMEORIGIN; \
    add_header X-XSS-Protection "1; mode=block"; \
    \
    # Proxy to Node.js application \
    location / { \
        proxy_pass http://esim-backend:3000; \
        proxy_http_version 1.1; \
        proxy_set_header Upgrade $http_upgrade; \
        proxy_set_header Connection '"'"'upgrade'"'"'; \
        proxy_set_header Host $host; \
        proxy_set_header X-Real-IP $remote_addr; \
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; \
        proxy_set_header X-Forwarded-Proto $scheme; \
        proxy_cache_bypass $http_upgrade; \
        \
        # Increase timeouts for long-running requests like sync operations \
        proxy_connect_timeout 300s; \
        proxy_send_timeout 300s; \
        proxy_read_timeout 300s; \
        \
        # Additional timeout settings for WebSocket connections \
        proxy_buffering off; \
        proxy_cache off; \
    } \
}' > /etc/nginx/conf.d/app.conf

# Create ACME challenge directory
RUN mkdir -p /var/www/certbot/.well-known/acme-challenge/

# Create a test file for ACME challenge verification
RUN echo "This is a test file for ACME challenge verification" > /var/www/certbot/.well-known/acme-challenge/test-file

# Set proper permissions
RUN chmod -R 755 /var/www/certbot

# Expose ports
EXPOSE 80 443

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]
