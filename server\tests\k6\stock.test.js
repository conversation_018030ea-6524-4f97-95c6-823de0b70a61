import http from 'k6/http';
import { sleep } from 'k6';
import { CONFIG } from './config.js';
import { checkResponse, getAuthToken } from './helpers.js';

export const options = {
    thresholds: CONFIG.thresholds,
    scenarios: {
        stock_flow: CONFIG.scenarios.load
    }
};

export function setup() {
    const token = getAuthToken(http);
    return { 
        baseUrl: CONFIG.baseUrl,
        token: token
    };
}

export default function (data) {
    const baseUrl = data.baseUrl;
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${data.token}`
    };

    // Get stock levels for all plans
    const stockResponse = http.get(`${baseUrl}/api/esim-plans/stock/all`, {
        headers: headers
    });
    checkResponse(stockResponse);

    sleep(1);

    // Get stock history for a specific plan
    const stockHistoryResponse = http.get(`${baseUrl}/api/esim-plans/stock/history/test-plan-id`, {
        headers: headers
    });
    checkResponse(stockHistoryResponse);

    sleep(1);

    // Check stock threshold alerts
    const thresholdResponse = http.get(`${baseUrl}/api/esim-plans/stock/threshold-alerts`, {
        headers: headers
    });
    checkResponse(thresholdResponse);

    sleep(1);
}
