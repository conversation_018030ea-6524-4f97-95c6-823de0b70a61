import React, { useState, useEffect } from 'react';
import { useToast } from "@/components/ui/use-toast";
import api from '@/lib/axios';
import { useAuth } from '@/contexts/AuthContext';
import { Co<PERSON>, Eye, EyeOff, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from '@/components/ui/skeleton';

export default function ApiAccess() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [apiKeyInfo, setApiKeyInfo] = useState({
    partnerId: '',
    apiKey: '',
    lastReset: null
  });
  const [revealKey, setRevealKey] = useState(false);
  const [regenerating, setRegenerating] = useState(false);
  const [revealLoading, setRevealLoading] = useState(false);
  const [newKeyGenerated, setNewKeyGenerated] = useState(false);

  useEffect(() => {
    fetchApiKeyInfo();
  }, []);

  const fetchApiKeyInfo = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/partner/api-key');
      setApiKeyInfo({
        partnerId: user?.id || '',
        apiKey: response.data.apiKey || '',
        lastReset: response.data.lastReset
      });
    } catch (error) {
      console.error('Error fetching API key info:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to fetch API key information"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRegenerateKey = async () => {
    try {
      setRegenerating(true);
      const response = await api.post('/api/partner/api-key/generate');
      
      // Update the API key info with the new key
      setApiKeyInfo({
        ...apiKeyInfo,
        apiKey: response.data.apiKey,
        lastReset: response.data.lastReset
      });
      
      // Automatically reveal the new key
      setRevealKey(true);
      setNewKeyGenerated(true);
      
      toast({
        title: "API Key Regenerated",
        description: "Your new API key has been generated successfully. Please store it securely as you won't be able to see it again in its entirety."
      });
    } catch (error) {
      console.error('Error regenerating API key:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to regenerate API key"
      });
    } finally {
      setRegenerating(false);
    }
  };

  const handleRevealKey = async () => {
    try {
      setRevealLoading(true);
      
      if (!revealKey) {
        // Only make the API call if we're revealing the key
        const response = await api.get('/api/partner/api-key/reveal');
        setApiKeyInfo({
          ...apiKeyInfo,
          apiKey: response.data.apiKey
        });
      }
      
      setRevealKey(!revealKey);
    } catch (error) {
      console.error('Error revealing API key:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to reveal API key"
      });
    } finally {
      setRevealLoading(false);
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        toast({
          title: "Copied!",
          description: "Copied to clipboard"
        });
      })
      .catch(err => {
        console.error('Failed to copy:', err);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to copy to clipboard"
        });
      });
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div className="container mx-auto py-6">
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/api-access">API Access</BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex flex-col gap-6">
        <div>
          <h1 className="text-2xl font-bold">API Access</h1>
          <p className="text-gray-500">Manage your API credentials to integrate with our platform</p>
        </div>
        <div>
          <button
            className="bg-blue-700 text-white px-4 py-2 rounded-md"
            onClick={() => window.open(`${import.meta.env.VITE_API_URL}/docs`, '_blank')}
          >
            View API Documentation
          </button>
        </div>

        <Tabs defaultValue="credentials">
          <TabsList className="mb-4">
            <TabsTrigger value="credentials">Credentials</TabsTrigger>
            <TabsTrigger value="documentation">Documentation</TabsTrigger>
          </TabsList>
          
          <TabsContent value="credentials">
            <div className="grid gap-6">
              {/* API Key Card */}
              <Card>
                <CardHeader>
                  <CardTitle>API Credentials</CardTitle>
                  <CardDescription>
                    Your unique credentials for authenticating API requests
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="space-y-4">
                      <Skeleton className="h-8 w-full" />
                      <Skeleton className="h-8 w-full" />
                      <Skeleton className="h-8 w-2/3" />
                    </div>
                  ) : (
                    <>
                      {newKeyGenerated && (
                        <Alert className="mb-6 bg-amber-50 border-amber-200">
                          <AlertTitle className="text-amber-800">Important: Save Your New API Key</AlertTitle>
                          <AlertDescription className="text-amber-700">
                            This is the only time you'll see your full API key. Please copy it and store it securely.
                          </AlertDescription>
                        </Alert>
                      )}
                      
                      <div className="space-y-6">
                        {/* Partner ID */}
                        <div>
                          <div className="flex justify-between mb-2">
                            <label className="text-sm font-medium text-gray-700">Partner ID</label>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="h-6 px-2"
                              onClick={() => copyToClipboard(apiKeyInfo.partnerId)}
                            >
                              <Copy className="h-3.5 w-3.5" />
                            </Button>
                          </div>
                          <div className="bg-gray-100 p-3 rounded flex justify-between items-center">
                            <code className="text-sm font-mono">{apiKeyInfo.partnerId}</code>
                          </div>
                        </div>

                        {/* API Key */}
                        <div>
                          <div className="flex justify-between mb-2">
                            <label className="text-sm font-medium text-gray-700">API Key</label>
                            <div className="flex gap-2">
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="h-6 px-2"
                                onClick={handleRevealKey}
                                disabled={revealLoading}
                              >
                                {revealLoading ? (
                                  <RefreshCw className="h-3.5 w-3.5 animate-spin" />
                                ) : revealKey ? (
                                  <EyeOff className="h-3.5 w-3.5" />
                                ) : (
                                  <Eye className="h-3.5 w-3.5" />
                                )}
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="h-6 px-2"
                                onClick={() => copyToClipboard(apiKeyInfo.apiKey)}
                                disabled={!apiKeyInfo.apiKey}
                              >
                                <Copy className="h-3.5 w-3.5" />
                              </Button>
                            </div>
                          </div>
                          <div className="bg-gray-100 p-3 rounded flex justify-between items-center">
                            {apiKeyInfo.apiKey ? (
                              <code className="text-sm font-mono">
                                {revealKey ? apiKeyInfo.apiKey : apiKeyInfo.apiKey}
                              </code>
                            ) : (
                              <span className="text-gray-500">This API key will be displayed only once. Make sure to copy and securely store it now, as it cannot be retrieved later.</span>
                            )}
                          </div>
                          {apiKeyInfo.lastReset && (
                            <p className="text-xs text-gray-500 mt-1">
                              Last generated: {formatDate(apiKeyInfo.lastReset)}
                            </p>
                          )}
                        </div>

                        {/* Regenerate Button */}
                        <Button 
                          variant="destructive" 
                          onClick={handleRegenerateKey}
                          disabled={regenerating}
                          className="mt-4"
                        >
                          {regenerating ? (
                            <>
                              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                              Regenerating...
                            </>
                          ) : (
                            <>
                              <RefreshCw className="mr-2 h-4 w-4" />
                              Regenerate API Key
                            </>
                          )}
                        </Button>
                        
                        <p className="text-sm text-gray-500 mt-2">
                          Warning: Regenerating your API key will invalidate your previous key immediately.
                          Any applications using the old key will stop working.
                        </p>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>

              {/* Usage Instructions */}
              <Card>
                <CardHeader>
                  <CardTitle>API Usage</CardTitle>
                  <CardDescription>
                    Learn how to authenticate your API requests
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <p>
                      To authenticate your API requests, include the following headers:
                    </p>
                    
                    <div className="bg-gray-100 p-4 rounded-md space-y-2 font-mono text-sm">
                      <p><span className="text-blue-600">Authorization:</span> Bearer YOUR_API_KEY</p>
                      <p><span className="text-blue-600">X-Partner-ID:</span> YOUR_PARTNER_ID</p>
                    </div>
                    
                    <p className="text-sm">
                      Replace <code>YOUR_API_KEY</code> with your API key and <code>YOUR_PARTNER_ID</code> with your Partner ID.
                    </p>
                    
                    <div className="bg-gray-100 p-4 rounded-md">
                      <p className="font-semibold mb-2">Example Request:</p>
                      <pre className="text-xs overflow-x-auto">
{`curl -X GET ${window.location.origin}/api/v1/esim-plans \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "X-Partner-ID: YOUR_PARTNER_ID" \\
  -H "Content-Type: application/json"`}
                      </pre>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="documentation">
            <Card>
              <CardHeader>
                <CardTitle>API Documentation</CardTitle>
                <CardDescription>
                  Comprehensive guide to using our Partner API
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <section>
                    <h3 className="text-lg font-medium mb-2">Introduction</h3>
                    <p>
                      Our Partner API allows you to programmatically access and manage your eSIM plans, 
                      create orders, and check stock availability. All API endpoints are accessible via HTTPS 
                      and require authentication.
                    </p>
                  </section>
                  
                  <section>
                    <h3 className="text-lg font-medium mb-2">Available Endpoints</h3>
                    <div className="space-y-4">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="outline" className="bg-green-50">GET</Badge>
                          <code>/api/v1/products</code>
                        </div>
                        <p className="text-sm text-gray-600">List all available eSIM plans</p>
                      </div>
                      
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="outline" className="bg-green-50">GET</Badge>
                          <code>/api/v1/order/{'{orderId}'}</code>
                        </div>
                        <p className="text-sm text-gray-600">Get details of a specific eSIM plan</p>
                      </div>
                      
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="outline" className="bg-blue-50">POST</Badge>
                          <code>/api/v1/order</code>
                        </div>
                        <p className="text-sm text-gray-600">Create a new eSIM order</p>
                      </div>
                      
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="outline" className="bg-green-50">GET</Badge>
                          <code>/api/v1/usage/{'{orderId}'}</code>
                        </div>
                        <p className="text-sm text-gray-600">Get usage details of a specific order</p>
                      </div>
                    </div>
                  </section>
                  
                  <div className="flex justify-center mt-6">
                  <Button variant="outline" onClick={() => window.open(`${import.meta.env.VITE_API_URL}/docs`, '_blank')} className="bg-blue-700 text-white">
                      View Complete API Documentation
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
} 