'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Drop the existing table
    await queryInterface.dropTable('EsimStocks');
    
    // Create the table with new schema
    await queryInterface.createTable('EsimStocks', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.literal('UUID()'),
        primaryKey: true
      },
      esimPlanId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'EsimPlans',
          key: 'id'
        }
      },
      lpaString: {
        type: Sequelize.STRING,
        allowNull: false
      },
      iccid: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      smdpAddress: {
        type: Sequelize.STRING,
        allowNull: false
      },
      accessPointName: {
        type: Sequelize.STRING,
        allowNull: false
      },
      activationCode: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      phoneNumber: {
        type: Sequelize.STRING,
        allowNull: true
      },
      confCode: {
        type: Sequelize.STRING,
        allowNull: true
      },
      walletAuthTransactionId: {
        type: Sequelize.STRING,
        allowNull: true
      },
      orderId: {
        type: Sequelize.STRING,
        allowNull: true
      },
      orderDate: {
        type: Sequelize.DATE,
        allowNull: true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      status: {
        type: Sequelize.ENUM('available', 'assigned', 'activated', 'expired'),
        defaultValue: 'available',
        allowNull: false
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('EsimStocks');
  }
};
