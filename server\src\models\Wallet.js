const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const { sendLowBalanceEmail } = require('../utils/emailService');

const Wallet = sequelize.define('Wallet', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4
    },
    userId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        unique: true,
        references: {
            model: 'users',
            key: 'id'
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE'
    },
    balance: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0.00,
        validate: {
            min: 0
        }
    },
    balanceThreshold: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 500.00,
        validate: {
            min: 0
        }
    },
    maxBalance: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 1000.00,
        validate: {
            min: 0
        }
    },
    currencyCode: {
        type: DataTypes.CHAR(3),
        allowNull: false,
        defaultValue: 'USD'
    },
    isActive: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    }
}, {
    tableName: 'wallets',
    timestamps: true,
    hooks: {
        afterUpdate: async (wallet, options) => {
            try {
                // Only check if balance was changed
                if (wallet.changed('balance')) {
                    const newBalance = Number(wallet.balance);
                    const threshold = Number(wallet.balanceThreshold);

                    // Check if balance is below threshold
                    if (newBalance < threshold) {
                        // Get user email from the database
                        const user = await sequelize.models.User.findByPk(wallet.userId, {
                            attributes: ['email']
                        });

                        if (user?.email) {
                            await sendLowBalanceEmail(user.email, newBalance, threshold);
                        }
                    }
                }
            } catch (error) {
                console.error('Error in wallet afterUpdate hook:', error);
                // Don't throw the error as we don't want to affect the main transaction
            }
        }
    }
});

// Define associations
Wallet.associate = function(models) {
    Wallet.belongsTo(models.User, {
        foreignKey: 'userId',
        as: 'user',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE'
    });

    Wallet.hasMany(models.WalletTransaction, {
        foreignKey: 'walletId',
        as: 'transactions',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE'
    });
};

module.exports = Wallet;
