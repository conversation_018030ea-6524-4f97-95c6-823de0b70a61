import React, { useEffect, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import api from '@/lib/axios';
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Building2, Mail, Phone, MapPin, User, Briefcase } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton"; // Add a skeleton component
import { motion } from "framer-motion"; // For animations
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function Account() {
    const { user } = useAuth();
    const { toast } = useToast();
    const [partnerData, setPartnerData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [showChangePasswordDialog, setShowChangePasswordDialog] = useState(false);
    const [passwordData, setPasswordData] = useState({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
    });
    const [error, setError] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const partnerResponse = await api.get(`/api/partners/${user.id}`);
                setPartnerData(partnerResponse.data);
            } catch (error) {
                console.error('Error fetching data:', error);
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: "Failed to load account information"
                });
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [user.id]);

    const handlePasswordChange = async (e) => {
        e.preventDefault();
        setError('');
        setIsSubmitting(true);

        // Basic validation
        if (!passwordData.currentPassword.trim()) {
            setError('Please enter your current password');
            setIsSubmitting(false);
            return;
        }

        if (!passwordData.newPassword.trim()) {
            setError('Please enter a new password');
            setIsSubmitting(false);
            return;
        }

        if (!passwordData.confirmPassword.trim()) {
            setError('Please confirm your new password');
            setIsSubmitting(false);
            return;
        }

        // Password requirements
        if (passwordData.newPassword.length < 8) {
            setError('New password must be at least 8 characters long');
            setIsSubmitting(false);
            return;
        }

        if (passwordData.newPassword !== passwordData.confirmPassword) {
            setError('New passwords do not match');
            setIsSubmitting(false);
            return;
        }

        if (passwordData.currentPassword === passwordData.newPassword) {
            setError('New password must be different from current password');
            setIsSubmitting(false);
            return;
        }

        try {
            const response = await api.post('/api/auth/change-password', {
                currentPassword: passwordData.currentPassword,
                newPassword: passwordData.newPassword
            });

            if (response.data.message) {
                toast({
                    title: "Success",
                    description: response.data.message,
                    variant: "success"
                });

                setShowChangePasswordDialog(false);
                setPasswordData({
                    currentPassword: '',
                    newPassword: '',
                    confirmPassword: ''
                });
            }
        } catch (error) {
            console.error('Password change error:', error);
            const errorMessage = error.response?.data?.message || 'Failed to change password. Please try again.';
            setError(errorMessage);
            toast({
                title: "Error",
                description: errorMessage,
                variant: "destructive"
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const InfoItem = ({ icon: Icon, label, value }) => (
        <motion.div
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 300 }}
            className="p-4 bg-gradient-to-br from-white to-gray-50 rounded-lg hover:shadow-lg transition-shadow duration-200 border border-gray-100"
        >
            <div className="flex items-center gap-2 mb-2">
                <div className="p-2 bg-primary/10 rounded-full">
                    <Icon className="w-5 h-5 text-primary" />
                </div>
                <Label className="text-sm text-muted-foreground">{label}</Label>
            </div>
            <div className="text-lg font-medium truncate">{value || "—"}</div>
        </motion.div>
    );

    if (loading) {
        return (
            <div className="flex items-center justify-center h-screen">
                <div className="space-y-4 text-center">
                    <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
                    <p className="text-muted-foreground">Loading account information...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto py-8 space-y-8 max-w-7xl px-4">
            <div className="flex justify-between items-center">
                <div className="space-y-1">
                    <h1 className="text-3xl font-bold tracking-tight text-gray-900">Account Details</h1>
                    <p className="text-muted-foreground">Manage your account information and business details</p>
                </div>
                <div className="flex gap-4 items-center">
                    <Button 
                        variant="outline"
                        onClick={() => setShowChangePasswordDialog(true)}
                    >
                        Change Password
                    </Button>
                    <Badge 
                        variant={partnerData?.isActive ? "success" : "destructive"}
                        className="text-sm px-3 py-1"
                    >
                        {partnerData?.isActive ? "Active Partner" : "Inactive Partner"}
                    </Badge>
                </div>
            </div>

            <div className="grid gap-8">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                >
                    <Card className="shadow-lg border border-blue-100">
                        <CardHeader className="bg-gradient-to-r from-blue-800 to-blue-600 border-b">
                            <CardTitle className="flex items-center gap-2 text-xl font-semibold text-white">
                                <Building2 className="w-6 h-6 text-white" />
                                Business Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="grid gap-6 pt-6">
                            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <InfoItem 
                                    icon={Briefcase}
                                    label="Company Name"
                                    value={partnerData?.businessName}
                                />
                                <InfoItem 
                                    icon={Mail}
                                    label="Business Email"
                                    value={partnerData?.businessEmail}
                                />
                                <InfoItem 
                                    icon={User}
                                    label="Contact Person"
                                    value={`${partnerData?.firstName} ${partnerData?.lastName}`}
                                />
                                <InfoItem 
                                    icon={Phone}
                                    label="Phone Number"
                                    value={partnerData?.phoneNumber}
                                />
                                <InfoItem 
                                    icon={Mail}
                                    label="Email"
                                    value={partnerData?.email}
                                />
                                <InfoItem 
                                    icon={MapPin}
                                    label="Country"
                                    value={partnerData?.country?.name}
                                />
                            </div>

                            <div className="mt-6">
                                <h3 className="text-lg font-semibold mb-4 text-gray-900">Billing Address</h3>
                                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <InfoItem 
                                        icon={MapPin}
                                        label="Street Address"
                                        value={partnerData?.billingAddressLine1}
                                    />
                                    <InfoItem 
                                        icon={MapPin}
                                        label="Additional Address"
                                        value={partnerData?.billingAddressLine2}
                                    />
                                    <InfoItem 
                                        icon={MapPin}
                                        label="City"
                                        value={partnerData?.billingCity}
                                    />
                                    <InfoItem 
                                        icon={MapPin}
                                        label="Province/State"
                                        value={partnerData?.billingProvince}
                                    />
                                    <InfoItem 
                                        icon={MapPin}
                                        label="Postal Code"
                                        value={partnerData?.billingPostalCode}
                                    />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </motion.div>
            </div>

            <Dialog open={showChangePasswordDialog} onOpenChange={setShowChangePasswordDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Change Password</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handlePasswordChange} className="space-y-4">
                        {error && (
                            <Alert variant="destructive">
                                <AlertCircle className="h-4 w-4" />
                                <AlertDescription>{error}</AlertDescription>
                            </Alert>
                        )}
                        <div className="space-y-2">
                            <Label htmlFor="currentPassword">Current Password</Label>
                            <Input
                                id="currentPassword"
                                type="password"
                                value={passwordData.currentPassword}
                                onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                                required
                                placeholder="Enter your current password"
                                className="w-full"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="newPassword">New Password</Label>
                            <Input
                                id="newPassword"
                                type="password"
                                value={passwordData.newPassword}
                                onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                                required
                                placeholder="Minimum 8 characters"
                                className="w-full"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="confirmPassword">Confirm New Password</Label>
                            <Input
                                id="confirmPassword"
                                type="password"
                                value={passwordData.confirmPassword}
                                onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                                required
                                placeholder="Re-enter your new password"
                                className="w-full"
                            />
                        </div>
                        <DialogFooter>
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => {
                                    setShowChangePasswordDialog(false);
                                    setError('');
                                    setPasswordData({
                                        currentPassword: '',
                                        newPassword: '',
                                        confirmPassword: ''
                                    });
                                }}
                                disabled={isSubmitting}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? 'Changing Password...' : 'Change Password'}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
}