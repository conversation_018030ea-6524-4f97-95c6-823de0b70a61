#!/bin/bash

# Script to clean up SSL certificate files with proper permissions
# Run this script if you encounter permission issues with SSL certificate files

echo "Cleaning up SSL certificate files..."

cd ~/esim-project

# Stop containers first
if [ -f "docker-compose.yml" ]; then
    echo "Stopping containers..."
    docker-compose down || true
fi

# Clean up SSL certificate files using Dock<PERSON>
if [ -d "nginx/certbot/conf" ]; then
    echo "Removing SSL certificate files with proper permissions..."
    docker run --rm -v "$(pwd)/nginx/certbot/conf:/etc/letsencrypt" alpine:latest \
        sh -c "rm -rf /etc/letsencrypt/*" || true
    
    # Remove the directory itself
    rmdir nginx/certbot/conf 2>/dev/null || true
fi

# Clean up other nginx directories
if [ -d "nginx/certbot/www" ]; then
    rm -rf nginx/certbot/www || true
fi

if [ -d "nginx/conf" ]; then
    rm -rf nginx/conf || true
fi

if [ -d "nginx" ]; then
    rmdir nginx 2>/dev/null || true
fi

echo "✅ SSL certificate cleanup completed!"
echo "You can now run your deployment again."
