import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, FileText, Folder, Eye } from 'lucide-react';
import {
    <PERSON>alog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription
} from "@/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import axios from '@/lib/axios';

const CATEGORIES = [
    { value: 'general', label: 'General' },
    { value: 'technical', label: 'Technical' },
    { value: 'billing', label: 'Billing' },
    { value: 'support', label: 'Support' },
    { value: 'faq', label: 'FAQ' }
];

export default function KnowledgeBase() {
    const { toast } = useToast();
    const [articles, setArticles] = useState([]);
    const [totalPages, setTotalPages] = useState(1);
    const [currentPage, setCurrentPage] = useState(1);
    const [search, setSearch] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [isLoading, setIsLoading] = useState(false);
    const [showArticleDialog, setShowArticleDialog] = useState(false);
    const [selectedArticle, setSelectedArticle] = useState(null);
    const [popularArticles, setPopularArticles] = useState([]);

    const fetchArticles = async () => {
        try {
            setIsLoading(true);
            const params = new URLSearchParams({
                page: currentPage,
                limit: 9,
                ...(search && { search }),
                ...(selectedCategory !== 'all' && { category: selectedCategory })
            });

            const response = await axios.get(`/api/knowledge-base?${params}`);
            setArticles(response.data.articles);
            setTotalPages(response.data.totalPages);

            // Get popular articles (first page, no filters)
            if (currentPage === 1 && !search && selectedCategory === 'all') {
                const popularResponse = await axios.get('/api/knowledge-base?limit=5');
                setPopularArticles(popularResponse.data.articles);
            }
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to fetch articles",
                variant: "destructive"
            });
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchArticles();
    }, [currentPage, search, selectedCategory]);

    const handleViewArticle = (article) => {
        setSelectedArticle(article);
        setShowArticleDialog(true);
    };

    const getCategoryLabel = (value) => {
        const category = CATEGORIES.find(cat => cat.value === value);
        return category ? category.label : value;
    };

    return (
        <div>
            <div className="mb-8">
                <h1 className="text-2xl font-bold">Knowledge Base</h1>
                <p className="text-gray-600 mt-1">Browse help articles and documentation</p>
            </div>

            <div className="mb-6 flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                        placeholder="Search articles..."
                        className="pl-10"
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                    />
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="all">All Categories</SelectItem>
                        {CATEGORIES.map(cat => (
                            <SelectItem key={cat.value} value={cat.value}>
                                {cat.label}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {articles.map((article) => (
                    <Card key={article.id} className="p-6">
                        <div className="flex items-start gap-4">
                            <div className="bg-blue-600/10 p-3 rounded-lg">
                                <FileText className="w-6 h-6 text-blue-600" />
                            </div>
                            <div className="flex-1">
                                <h3 className="font-semibold text-lg">{article.title}</h3>
                                <p className="text-sm text-gray-600 mt-1 whitespace-pre-wrap">
                                    {article.content.substring(0, 100)}
                                    {article.content.length > 100 && '...'}
                                </p>
                                <div className="flex items-center gap-2 mt-2">
                                    <span className="text-sm text-gray-500">
                                        {getCategoryLabel(article.category)}
                                    </span>
                                </div>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="mt-4 bg-blue-600 text-white"
                                    onClick={() => handleViewArticle(article)}
                                >
                                    <Eye className="w-4 h-4 mr-2" />
                                    Read More
                                </Button>
                            </div>
                        </div>
                    </Card>
                ))}
            </div>

            {totalPages > 1 && (
                <div className="flex justify-center gap-2 mt-6 ">
                    {[...Array(totalPages)].map((_, i) => (
                        <Button
                            key={i}
                            variant={currentPage === i + 1 ? "default" : "outline"}
                            size="sm"
                            onClick={() => setCurrentPage(i + 1)}
                        >
                            {i + 1}
                        </Button>
                    ))}
                </div>
            )}

            {popularArticles.length > 0 && (
                <div className="bg-white rounded-lg shadow p-6 mt-8">
                    <div className="flex items-center justify-between mb-4">
                        <h2 className="text-lg font-semibold">Popular Articles</h2>
                    </div>
                    <div className="space-y-4">
                        {popularArticles.map((article) => (
                            <div
                                key={article.id}
                                className="flex items-center gap-4 p-4 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer"
                                onClick={() => handleViewArticle(article)}
                            >
                                <div className="bg-blue-600/10 p-2 rounded">
                                    <FileText className="w-5 h-5 text-blue-600" />
                                </div>
                                <div className="flex-1">
                                    <h3 className="font-medium">{article.title}</h3>
                                    <p className="text-sm text-gray-600">
                                        {getCategoryLabel(article.category)}
                                    </p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            <Dialog open={showArticleDialog} onOpenChange={setShowArticleDialog}>
                <DialogContent className="max-w-3xl max-h-[80vh]">
                    <DialogHeader>
                        <DialogTitle className="text-xl">
                            {selectedArticle?.title}
                        </DialogTitle>
                        <DialogDescription>
                            Article</DialogDescription>
                    </DialogHeader>
                    <div className="mt-4 overflow-y-auto">
                        <div className="prose max-w-none">
                            <div className="whitespace-pre-wrap font-mono text-sm bg-gray-50 p-6 rounded-lg">
                                {selectedArticle?.content}
                            </div>
                        </div>
                        <div className="mt-6 flex items-center gap-4 text-sm text-gray-500">
                            <div>Category: {selectedArticle && getCategoryLabel(selectedArticle.category)}</div>
                            <div>•</div>
                            <div>Last updated: {selectedArticle && new Date(selectedArticle.updatedAt).toLocaleDateString()}</div>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
}
