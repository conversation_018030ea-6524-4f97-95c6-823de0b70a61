const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Country = sequelize.define('Country', {
    id: {
        type: DataTypes.CHAR(2),
        primaryKey: true,
        allowNull: false,
        validate: {
            is: /^[A-Z]{2}$/
        }
    },
    iso3: {
        type: DataTypes.CHAR(3),
        allowNull: false,
        validate: {
            is: /^[A-Z]{3}$/
        }
    },
    name: {
        type: DataTypes.STRING(100),
        allowNull: false
    },
    flagEmoji: {
        type: DataTypes.STRING(10),
        allowNull: true
    },
    flagUrl: {
        type: DataTypes.STRING,
        allowNull: true
    },
    phoneCode: {
        type: DataTypes.STRING(10),
        allowNull: true
    },
    currencyCode: {
        type: DataTypes.CHAR(3),
        allowNull: true
    },
    currencySymbol: {
        type: DataTypes.STRING(5),
        allowNull: true
    },
    region: {
        type: DataTypes.STRING(50),
        allowNull: true
    },
    isActive: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    }
}, {
    tableName: 'countries',
    timestamps: true
});

// Define associations
Country.associate = function(models) {
    Country.hasMany(models.User, {
        foreignKey: 'countryId',
        as: 'users'
    });
    
    Country.hasMany(models.User, {
        foreignKey: 'billingCountryId',
        as: 'billingUsers'
    });

    Country.belongsToMany(models.EsimPlan, {
        through: models.EsimPlanCountries,
        as: 'esimPlans',
        foreignKey: 'countryId'
    });
};

module.exports = Country;
