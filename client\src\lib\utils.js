import { clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs) {
  return twMerge(clsx(inputs))
}

/**
 * Formats a number to display as integer if whole number, or with one decimal place if decimal
 * @param {number|string} value - The number to format
 * @returns {string|null} - Formatted number or null if input is null/undefined
 */
export function formatPlanData(value) {
    if (value === null || value === undefined || value === '') return null;
    const num = Number(value);
    return Number.isInteger(num) ? num.toString() : num.toFixed(1);
}

/**
 * Convert capacity from KB to GB for display
 * @param {number} capacityKB - Capacity in KB
 * @returns {string} - Formatted capacity with appropriate unit
 */
export function formatCapacityFromKB(capacityKB) {
    if (!capacityKB || capacityKB <= 0) return '0 MB';

    const capacityMB = capacityKB / 1024;
    const capacityGB = capacityMB / 1024;

    if (capacityGB >= 1) {
        return `${capacityGB.toFixed(1)} GB`;
    } else {
        return `${Math.round(capacityMB)} MB`;
    }
}

/**
 * Convert price from CNY to USD
 * @param {number} priceInCNY - Price in Chinese Yuan
 * @param {number} exchangeRate - CNY to USD exchange rate (default: 0.14 - approximately 7.1 CNY = 1 USD)
 * @returns {number} - Price in USD
 */
export function convertCNYToUSD(priceInCNY, exchangeRate = 0.14) {
    if (!priceInCNY || priceInCNY <= 0) return 0;
    return Number((priceInCNY * exchangeRate).toFixed(2));
}

/**
 * Format price with currency symbol
 * @param {number} price - Price value
 * @param {string} currency - Currency code (default: 'USD')
 * @returns {string} - Formatted price string
 */
export function formatPrice(price, currency = 'USD') {
    if (!price || price <= 0) return '$0.00';

    const formatter = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });

    return formatter.format(price);
}
