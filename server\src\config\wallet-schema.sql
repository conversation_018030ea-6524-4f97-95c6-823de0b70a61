-- Use the database
USE esim_demo;

-- Create Wallets table
CREATE TABLE IF NOT EXISTS wallets (
    id CHAR(36) PRIMARY KEY,
    userId CHAR(36) NOT NULL,
    balance DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    balanceThreshold DECIMAL(10,2) NOT NULL DEFAULT 500.00,
    maxBalance DECIMAL(10, 2) NOT NULL DEFAULT 1000.00,
    currencyCode CHAR(3) NOT NULL DEFAULT 'USD',
    isActive BOOLEAN DEFAULT true,
    createdAt DATETIME NOT NULL,
    updatedAt DATETIME NOT NULL,
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE RESTRICT,
    UNIQUE KEY unique_user_wallet (userId),
    INDEX idx_wallet_search (userId, isActive)
) ENGINE=InnoDB;

-- Create WalletTransactions table
CREATE TABLE IF NOT EXISTS wallettransactions (
    id CHAR(36) PRIMARY KEY,
    walletId CHAR(36) NOT NULL,
    type ENUM('credit', 'debit') NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    balance DECIMAL(10, 2) NOT NULL,
    description VARCHAR(255) NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'reversed') NOT NULL DEFAULT 'pending',
    metadata JSON,
    referenceId VARCHAR(100),
    referenceType ENUM('order', 'refund', 'manual', 'other') NOT NULL DEFAULT 'other',
    createdAt DATETIME NOT NULL,
    updatedAt DATETIME NOT NULL,
    FOREIGN KEY (walletId) REFERENCES wallets(id) ON DELETE RESTRICT,
    INDEX idx_transaction_search (walletId, type, status, createdAt),
    INDEX idx_transaction_reference (referenceId, referenceType)
) ENGINE=InnoDB;

CREATE TABLE IF NOT EXISTS centralwallet (
    id INT PRIMARY KEY AUTO_INCREMENT,
    totalBalance DECIMAL(12, 2) NOT NULL DEFAULT 0.00,
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- Triggers for safety checks
DELIMITER //

-- Trigger to prevent negative balances in Wallets
CREATE TRIGGER before_wallet_update
BEFORE UPDATE ON wallets
FOR EACH ROW
BEGIN
    IF NEW.balance < 0 THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'Wallet balance cannot be negative';
    END IF;
    
    IF NEW.balance > NEW.maxBalance THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'Cannot exceed maximum wallet balance';
    END IF;
END;
//

-- Trigger to validate transaction amounts
CREATE TRIGGER before_wallet_transaction_insert
BEFORE INSERT ON wallettransactions
FOR EACH ROW
BEGIN
    DECLARE wallet_balance DECIMAL(10,2);
    DECLARE wallet_max_balance DECIMAL(10,2);
    
    -- Get current wallet balance and max balance
    SELECT balance, maxBalance INTO wallet_balance, wallet_max_balance
    FROM wallets WHERE id = NEW.walletId;
    
    -- For debit transactions, check if sufficient balance exists
    IF NEW.type = 'debit' AND wallet_balance < NEW.amount THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'Insufficient wallet balance for debit transaction';
    END IF;
    
    -- For credit transactions, check if it would exceed max balance
    IF NEW.type = 'credit' AND (wallet_balance + NEW.amount) > wallet_max_balance THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'Credit would exceed maximum wallet balance';
    END IF;
    
    -- Validate amount is positive
    IF NEW.amount <= 0 THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'Transaction amount must be positive';
    END IF;
END;
//

-- Trigger to validate central wallet operations
CREATE TRIGGER before_central_wallet_update
BEFORE UPDATE ON centralwallet
FOR EACH ROW
BEGIN
    IF NEW.totalBalance < 0 THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'Central wallet balance cannot be negative';
    END IF;
END;
//

DELIMITER ;
