openapi: 3.0.3
info:
  title: Partner API Documentation
  description: |
    This API allows authorized partners to fetch product data, place and track orders, and view usage information.
    
    ## Authentication
    All API requests require authentication using an API key and Partner ID.
    
    Include the following HTTP headers with each request:
    - `Authorization: Bearer {API_KEY}`
    - `X-Partner-ID: {partner_id}`
    
    You can obtain your API key and Partner ID from the Partner Portal.
  version: v1
  
servers:
  - url: http://localhost:5000/api/v1
    description: Development Server
  - url: https://partner-api.your-domain.com/api/v1
    description: Production Server

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: Authorization
      description: API key obtained from the Partner Portal. Format: `Bearer {API_KEY}`
    PartnerIdAuth:
      type: apiKey
      in: header
      name: X-Partner-ID
      description: Your Partner ID obtained from the Partner Portal.

  schemas:
    Product:
      type: object
      properties:
        productId:
          type: string
          example: "ABCDEF123456"
          description: Unique identifier for the product
        name:
          type: string
          example: "Europe 5GB / 30 Days"
          description: Product name
        description:
          type: string
          example: "5GB data valid for 30 days across Europe"
          description: Detailed product description
        instructions:
          type: string
          nullable: true
          description: Optional instructions for using the product
        price:
          type: number
          format: float
          example: 29.99
          description: Product price in USD
        validityDays:
          type: integer
          example: 30
          description: Number of days the product is valid for
        countries:
          type: array
          items:
            type: string
          example: ["FR", "DE", "IT", "ES", "NL"]
          description: List of country codes where the product is valid
        region:
          type: array
          items:
            type: string
          example: ["Europe", "Asia"]
          description: Regions where the product is valid
        dataAmount:
          type: number
          format: float
          example: 5
          description: Amount of data included (if planType is 'Fixed')
        dataUnit:
          type: string
          example: "GB"
          description: Unit of data measurement (if planType is 'Fixed')
        customPlanData:
          type: string
          example: "2gb 460 kb/sec"
          description: Custom data plan description (if applicable)
        voiceMin:
          type: integer
          example: 50
          nullable: true
          description: Voice minutes included (if applicable)
        voiceMinUnit:
          type: string
          example: "Min"
          nullable: true
          description: Unit for voice minutes (if applicable)
        speed:
          type: string
          example: "Unrestricted"
          description: Data speed description
        planType:
          type: string
          example: "Fixed"
          description: Type of plan (Fixed, Unlimited, or Custom)
        category:
          type: string
          example: "esim_realtime"
          description: Product category
        networkType:
          type: string
          example: "4G/LTE"
          description: Network type supported
        isVoiceAvailable:
          type: boolean
          example: true
          description: Whether voice calls are available
        isSmsAvailable:
          type: boolean
          example: false
          description: Whether SMS is available
        hotspotAvailable:
          type: boolean
          example: true
          description: Whether hotspot functionality is available
        profile:
          type: string
          example: "local"
          description: Profile type
        activationPolicy:
          type: string
          example: "Activation upon purchase"
          description: When the eSIM is activated
        startDateEnabled:
          type: boolean
          example: false
          description: Whether custom activation date is supported
        features:
          type: array
          items:
            type: string
          example: ["5G Support", "Unlimited Calls"]
          description: Additional features of the product

    Order:
      type: object
      properties:
        orderId:
          type: string
          example: "VLZ123456"
          description: Unique identifier for the order
        status:
          type: string
          example: "completed"
          description: Order status
        product:
          type: object
          properties:
            productId:
              type: string
              example: "ABCDEF123456"
              description: Unique identifier for the product
            name:
              type: string
              example: "Europe 5GB / 30 Days"
              description: Product name
        orderTotal:
          type: number
          format: float
          example: 29.99
          description: Total amount charged for the order
        quantity:
          type: integer
          example: 1
          description: Number of items in the order
        startDate:
          type: string
          format: date
          example: "2023-11-01"
          description: Start date for the eSIM (if applicable)
        expiryDate:
          type: string
          format: date
          example: "2023-12-01"
          description: Expiry date for the eSIM
        iccid:
          type: string
          example: "8991000123456789012"
          description: ICCID of the eSIM
        smdpAddress:
          type: string
          example: "trl.prod.ondemandconnectivity.com"
          description: SMDP address for eSIM installation
        accessPointName:
          type: string
          example: "mbb"
          description: Access point name for the eSIM
        lpaString:
          type: string
          example: "LPA:1$trl.prod.ondemandconnectivity.com$AAA22"
          description: LPA string for eSIM installation
        activationCode:
          type: string
          example: "LPA:1$smdp.example.com$123456789-abcdef-123456"
          description: Activation code for the eSIM
        qrCodeUrl:
          type: string
          example: "https://example.com/qr/VLZ2001.png"
          description: URL to the QR code for easy eSIM installation
        createdAt:
          type: string
          format: date-time
          example: "2023-10-25T14:30:45Z"
          description: Date and time when the order was created
        top_up:
          type: string
          example: "Available"
          description: Whether top-up is available for this eSIM

    OrderRequest:
      type: object
      required:
        - productId
      properties:
        productId:
          type: string
          example: "ABCDEF123456"
          description: Product ID to order
        startDate:
          type: string
          format: date
          example: "2023-11-01"
          description: Optional start date, required if the plan has startDateEnabled=true
        category:
          type: string
          example: "esim_realtime"
          description: Optional category

    Usage:
      type: object
      properties:
        orderId:
          type: string
          example: "VLZ123456"
          description: Unique identifier for the order
        totalData:
          type: integer
          example: 5368709120
          description: Total data allowance in bytes
        usedData:
          type: integer
          example: 2147483648
          description: Used data in bytes
        remainingData:
          type: integer
          example: 3221225472
          description: Remaining data in bytes
        status:
          type: string
          example: "Active"
          description: Usage status
        activationDate:
          type: string
          format: date-time
          example: "2023-11-01T00:00:00Z"
          description: Date and time when the eSIM was activated
        expiryDate:
          type: string
          format: date-time
          example: "2023-12-01T00:00:00Z"
          description: Date and time when the eSIM expires
        lastUpdated:
          type: string
          format: date-time
          example: "2023-11-15T09:45:22Z"
          description: Date and time when the usage data was last updated
        usageMessage:
          type: string
          example: "The operator does not yet support package status check, please refer to your device settings to check your data usage"
          description: Message about usage data availability

    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
          description: Indicates if the request was successful
        error:
          type: object
          properties:
            code:
              type: string
              example: "UNAUTHORIZED"
              description: Error code
            message:
              type: string
              example: "Invalid API key"
              description: Error message

  responses:
    UnauthorizedError:
      description: Unauthorized - Invalid API key or partner ID
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    NotFoundError:
      description: Not Found - Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    ServerError:
      description: Internal Server Error - General server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

security:
  - ApiKeyAuth: []
    PartnerIdAuth: []

paths:
  /products:
    get:
      tags:
        - Products
      summary: Get all products
      description: Returns a list of all available products.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      products:
                        type: array
                        items:
                          $ref: '#/components/schemas/Product'
              example:
                success: true
                data:
                  products:
                    - productId: "ABCDEF123456"
                      name: "Europe 5GB / 30 Days"
                      description: "5GB data valid for 30 days across Europe"
                      instructions: null
                      price: 29.99
                      validityDays: 30
                      countries: ["FR", "DE", "IT", "ES", "NL"]
                      region: ["Europe"]
                      dataAmount: 5
                      dataUnit: "GB"
                      customPlanData: null
                      voiceMin: 50
                      voiceMinUnit: "Min"
                      speed: "Unrestricted"
                      planType: "Fixed"
                      category: "esim_realtime"
                      networkType: "4G/LTE"
                      isVoiceAvailable: true
                      isSmsAvailable: false
                      hotspotAvailable: true
                      profile: "local"
                      activationPolicy: "Activation upon purchase"
                      startDateEnabled: false
                      features: ["5G Support", "Unlimited Calls"]
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/ServerError'
      x-code-samples:
        - lang: curl
          source: |
            curl -X GET "https://partner-api.your-domain.com/api/v1/products" \
              -H "Authorization: Bearer your-api-key" \
              -H "X-Partner-ID: your-partner-id"

  /order/{orderId}:
    get:
      tags:
        - Orders
      summary: Get order details
      description: Retrieve order details using order ID.
      parameters:
        - name: orderId
          in: path
          required: true
          schema:
            type: string
          description: The ID of the order to retrieve
          example: "VLZ123456"
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Order'
              example:
                success: true
                data:
                  orderId: "VLZ123456"
                  status: "completed"
                  product:
                    productId: "ABCDEF123456"
                    name: "Europe 5GB / 30 Days"
                  orderTotal: 29.99
                  quantity: 1
                  startDate: "2023-11-01"
                  expiryDate: "2023-12-01"
                  iccid: "8991000123456789012"
                  smdpAddress: "trl.prod.ondemandconnectivity.com"
                  accessPointName: "mbb"
                  lpaString: "LPA:1$trl.prod.ondemandconnectivity.com$AAA22"
                  activationCode: "LPA:1$smdp.example.com$123456789-abcdef-123456"
                  status: "completed"
                  top_up: "Available"
                  qrCodeUrl: "https://example.com/qr/VLZ123456.png"
                  createdAt: "2023-10-25T14:30:45Z"
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
      x-code-samples:
        - lang: curl
          source: |
            curl -X GET "https://partner-api.your-domain.com/api/v1/order/VLZ123456" \
              -H "Authorization: Bearer your-api-key" \
              -H "X-Partner-ID: your-partner-id"

  /order:
    post:
      tags:
        - Orders
      summary: Place a new order
      description: Places a new order for an eSIM product.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderRequest'
      responses:
        '201':
          description: Order created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Order'
              example:
                success: true
                data:
                  orderId: "VLZ123456"
                  status: "completed"
                  qrCodeUrl: "https://example.com/qr/VLZ123456.png"
                  iccid: "8991000123456789012"
                  smdpAddress: "trl.prod.ondemandconnectivity.com"
                  accessPointName: "mbb"
                  lpaString: "LPA:1$trl.prod.ondemandconnectivity.com$AAA22"
                  activationCode: "LPA:1$smdp.example.com$123456789-abcdef-123456"
                  expiryDate: "2023-12-01"
                  orderTotal: 29.99
                  product:
                    productId: "ABCDEF123456"
                    name: "Europe 5GB / 30 Days"
        '400':
          description: Bad request - Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/ServerError'
      x-code-samples:
        - lang: curl
          source: |
            curl -X POST "https://partner-api.your-domain.com/api/v1/order" \
              -H "Authorization: Bearer your-api-key" \
              -H "X-Partner-ID: your-partner-id" \
              -H "Content-Type: application/json" \
              -d '{
                "productId": "ABCDEF123456",
                "startDate": "2023-11-01"
              }'

  /usage/{orderId}:
    get:
      tags:
        - Usage
      summary: Get usage data
      description: Returns usage details for a specific order.
      parameters:
        - name: orderId
          in: path
          required: true
          schema:
            type: string
          description: The ID of the order to retrieve usage for
          example: "VLZ123456"
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/Usage'
              example:
                success: true
                data:
                  orderId: "VLZ123456"
                  totalData: 5368709120
                  usedData: 2147483648
                  remainingData: 3221225472
                  status: "Active"
                  activationDate: "2023-11-01T00:00:00Z"
                  expiryDate: "2023-12-01T00:00:00Z"
                  lastUpdated: "2023-11-15T09:45:22Z"
                  usageMessage: "The operator does not yet support package status check, please refer to your device settings to check your data usage"
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
      x-code-samples:
        - lang: curl
          source: |
            curl -X GET "https://partner-api.your-domain.com/api/v1/usage/VLZ123456" \
              -H "Authorization: Bearer your-api-key" \
              -H "X-Partner-ID: your-partner-id" 