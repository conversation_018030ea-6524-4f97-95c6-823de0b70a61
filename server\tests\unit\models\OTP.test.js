const { OTP, User } = require('../../../src/models');
const sequelize = require('../../config/database');

describe('OTP Model Tests', () => {
    beforeAll(async () => {
        await sequelize.authenticate();
        await sequelize.sync({ force: true });
    });

    afterAll(async () => {
        await sequelize.close();
    });

    beforeEach(async () => {
        await Promise.all([
            OTP.destroy({ where: {}, force: true }),
            User.destroy({ where: {}, force: true })
        ]);
    });

    describe('OTP Creation', () => {
        let testUser;

        beforeEach(async () => {
            testUser = await User.create({
                email: '<EMAIL>',
                password: 'password123',
                role: 'partner'
            });
        });

        it('should create an OTP with valid data', async () => {
            const expiresAt = new Date();
            expiresAt.setMinutes(expiresAt.getMinutes() + 10); // 10 minutes from now

            const otp = await OTP.create({
                userId: testUser.id,
                code: '123456',
                expiresAt,
                isUsed: false
            });

            expect(otp).toBeTruthy();
            expect(otp.code).toBe('123456');
            expect(otp.userId).toBe(testUser.id);
            expect(otp.isUsed).toBe(false);
            expect(new Date(otp.expiresAt)).toBeInstanceOf(Date);
        });

        it('should not create OTP without userId', async () => {
            const expiresAt = new Date();
            expiresAt.setMinutes(expiresAt.getMinutes() + 10);

            await expect(OTP.create({
                code: '123456',
                expiresAt,
                isUsed: false
            })).rejects.toThrow();
        });

        it('should not create OTP with invalid code length', async () => {
            const expiresAt = new Date();
            expiresAt.setMinutes(expiresAt.getMinutes() + 10);

            await expect(OTP.create({
                userId: testUser.id,
                code: '12345', // Should be 6 characters
                expiresAt,
                isUsed: false
            })).rejects.toThrow();
        });

        it('should not create OTP without expiry', async () => {
            await expect(OTP.create({
                userId: testUser.id,
                code: '123456',
                isUsed: false
            })).rejects.toThrow();
        });
    });

    describe('OTP Validation', () => {
        let testUser;
        let testOTP;

        beforeEach(async () => {
            testUser = await User.create({
                email: '<EMAIL>',
                password: 'password123',
                role: 'partner'
            });

            const expiresAt = new Date();
            expiresAt.setMinutes(expiresAt.getMinutes() + 10);

            testOTP = await OTP.create({
                userId: testUser.id,
                code: '123456',
                expiresAt,
                isUsed: false
            });
        });

        it('should find valid unused OTP', async () => {
            const otp = await OTP.findOne({
                where: {
                    userId: testUser.id,
                    code: '123456',
                    isUsed: false,
                    expiresAt: {
                        [sequelize.Op.gt]: new Date()
                    }
                }
            });

            expect(otp).toBeTruthy();
            expect(otp.id).toBe(testOTP.id);
        });

        it('should not find expired OTP', async () => {
            // Create expired OTP
            const expiredDate = new Date();
            expiredDate.setMinutes(expiredDate.getMinutes() - 10); // 10 minutes ago

            await OTP.create({
                userId: testUser.id,
                code: '654321',
                expiresAt: expiredDate,
                isUsed: false
            });

            const otp = await OTP.findOne({
                where: {
                    userId: testUser.id,
                    code: '654321',
                    isUsed: false,
                    expiresAt: {
                        [sequelize.Op.gt]: new Date()
                    }
                }
            });

            expect(otp).toBeFalsy();
        });

        it('should not find used OTP', async () => {
            await testOTP.update({ isUsed: true });

            const otp = await OTP.findOne({
                where: {
                    userId: testUser.id,
                    code: '123456',
                    isUsed: false,
                    expiresAt: {
                        [sequelize.Op.gt]: new Date()
                    }
                }
            });

            expect(otp).toBeFalsy();
        });
    });

    describe('OTP Cleanup', () => {
        let testUser;

        beforeEach(async () => {
            testUser = await User.create({
                email: '<EMAIL>',
                password: 'password123',
                role: 'partner'
            });
        });

        it('should delete expired OTPs', async () => {
            // Create expired OTP
            const expiredDate = new Date();
            expiredDate.setMinutes(expiredDate.getMinutes() - 10);

            await OTP.create({
                userId: testUser.id,
                code: '123456',
                expiresAt: expiredDate,
                isUsed: false
            });

            // Delete expired OTPs
            await OTP.destroy({
                where: {
                    expiresAt: {
                        [sequelize.Op.lt]: new Date()
                    }
                }
            });

            const count = await OTP.count();
            expect(count).toBe(0);
        });

        it('should keep valid OTPs during cleanup', async () => {
            // Create valid OTP
            const validDate = new Date();
            validDate.setMinutes(validDate.getMinutes() + 10);

            await OTP.create({
                userId: testUser.id,
                code: '123456',
                expiresAt: validDate,
                isUsed: false
            });

            // Try to delete expired OTPs
            await OTP.destroy({
                where: {
                    expiresAt: {
                        [sequelize.Op.lt]: new Date()
                    }
                }
            });

            const count = await OTP.count();
            expect(count).toBe(1);
        });
    });
});
