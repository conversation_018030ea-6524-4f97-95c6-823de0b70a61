import { check } from 'k6';
import { CONFIG } from './config.js';

export function checkResponse(response, expectedStatus = 200) {
    const result = check(response, {
        'status is correct': (r) => r.status === expectedStatus,
        'response is not empty': (r) => r.body.length > 0,
        'response is json': (r) => r.headers['Content-Type'] && r.headers['Content-Type'].includes('application/json'),
    });

    if (!result) {
        console.log(`Response validation failed: ${response.status} ${response.body}`);
    }
    return result;
}

export function getAuthToken(http) {
    // Login request
    const loginResponse = http.post(`${CONFIG.baseUrl}/api/auth/login`, JSON.stringify({
        email: CONFIG.testUser.email,
        password: CONFIG.testUser.password
    }), {
        headers: { 'Content-Type': 'application/json' }
    });

    check(loginResponse, {
        'login successful': (r) => r.status === 200 && r.json('tempToken')
    });

    const tempToken = loginResponse.json('tempToken');

    // Send OTP request
    const otpResponse = http.post(`${CONFIG.baseUrl}/api/auth/send-otp`, JSON.stringify({
        tempToken
    }), {
        headers: { 'Content-Type': 'application/json' }
    });

    check(otpResponse, {
        'otp sent successfully': (r) => r.status === 200
    });

    // Verify OTP
    const verifyResponse = http.post(`${CONFIG.baseUrl}/api/auth/verify-otp`, JSON.stringify({
        otp: CONFIG.testUser.defaultOtp,
        tempToken
    }), {
        headers: { 'Content-Type': 'application/json' }
    });

    check(verifyResponse, {
        'otp verified successfully': (r) => r.status === 200 && r.json('token')
    });

    return verifyResponse.json('token');
}
